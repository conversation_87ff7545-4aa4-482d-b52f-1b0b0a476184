<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Third Party Services
    |--------------------------------------------------------------------------
    |
    | This file is for storing the credentials for third party services such
    | as Mailgun, Postmark, AWS and more. This file provides the de facto
    | location for this type of information, allowing packages to have
    | a conventional file to locate the various service credentials.
    |
    */

    'mailgun' => [
        'domain' => env('MAILGUN_DOMAIN'),
        'secret' => env('MAILGUN_SECRET'),
        'endpoint' => env('MAILGUN_ENDPOINT', 'api.mailgun.net'),
    ],

    'postmark' => [
        'token' => env('POSTMARK_TOKEN'),
    ],

    'ses' => [
        'key' => env('AWS_ACCESS_KEY_ID'),
        'secret' => env('AWS_SECRET_ACCESS_KEY'),
        'region' => env('AWS_DEFAULT_REGION', 'us-east-1'),
    ],

    'salla' => [
        'client_id' => env('SALLA_OAUTH_CLIENT_ID'),
        'client_secret' => env('SALLA_OAUTH_CLIENT_SECRET'),
        'redirect' => env('APP_URL').'/store/salla/callback',
        'webhook_secret' => env('SALLA_WEBHOOK_SECRET'),
        'authorization_mode' => env('SALLA_AUTHORIZATION_MODE', 'easy'),   // Supported: "easy", "custom"
    ],
    'barqfleet' => [
        'base_url' => env('BARQ_FLEET_BASE_URL', 'https://staging.barqfleet.com'),
    ],
    'transcorpsb' => [
        'base_url' => env('TRANSCORPSB_BASE_URL', 'https://api.suitefleet.com/api'),
        'username' => env('TRANSCORPSB_USERNAME'),
        'password' => env('TRANSCORPSB_PASSWORD'),
        'token' => env('TRANSCORPSB_TOKEN', null),
        'disable_ssl' => env('TRANSCORPSB_DISABLE_SSL', false),
        'clientid' => env('TRANSCORPSB_CLIENTID', 'transcorp'),
    ],
    'althabit' => [
        'base_url' => env('ALTHABIT_BASE_URL', 'https://prodapi.shipox.com/api'),
    ],
    'zid' => [
        'client_id' => env('ZID_CLIENT_ID'),
        'client_secret' => env('ZID_CLIENT_SECRET'),
        'redirect' => env('APP_URL').'/store/zid/callback',
    ],

    'brevo' => [
        'key' => env('BREVO_API_KEY'),
    ],

];
