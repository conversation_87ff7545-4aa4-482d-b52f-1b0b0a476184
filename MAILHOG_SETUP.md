# MailHog Setup for Development

MailHog has been added to your Docker Compose configuration as a dummy mail service for development.

## What is MailHog?

MailHog is a web and API based SMTP testing tool. It catches emails sent by your application during development and provides a web interface to view them.

## Services Added

- **MailHog SMTP Server**: Port 1025 (internal to Docker network)
- **MailHog Web Interface**: Port 8025 (http://localhost:8025)

## Configuration

To use MailHog, update your `.env` file with these mail settings:

```env
# Development mail settings (MailHog)
MAIL_MAILER=smtp
MAIL_HOST=mailhog
MAIL_PORT=1025
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_ENCRYPTION=null
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME="${APP_NAME}"
```

## Usage

1. Start the services:
   ```bash
   docker-compose up -d
   ```

2. Your Laravel application will now send emails to MailHog instead of real recipients

3. View captured emails in the web interface at: http://localhost:8025

4. All emails sent by your application will appear in the MailHog inbox

## Test Routes

Two test routes have been added to verify MailHog functionality:

### Basic Test Route
```
GET /test-mail
```
Sends a simple test email to `<EMAIL>` with a default message.

**Example:**
```bash
curl http://localhost/test-mail
```

### Custom Test Route
```
GET /test-mail-custom?to=<EMAIL>&subject=Test&content=Hello
```
Sends a customizable test email with optional parameters:
- `to`: Recipient email (default: <EMAIL>)
- `subject`: Email subject (default: Custom MailHog Test Email)
- `content`: Email content (default: This is a custom test email sent to MailHog!)

**Examples:**
```bash
# Basic custom test
curl "http://localhost/test-mail-custom"

# With custom parameters
curl "http://localhost/test-mail-custom?to=<EMAIL>&subject=Hello%20John&content=This%20is%20a%20test%20message"
```

Both routes return JSON responses indicating success/failure and provide links to view emails in MailHog.

## Benefits

- No real emails are sent during development
- Easy to test email functionality
- View email content and headers
- Test email templates and formatting
- No need for real SMTP credentials during development

## Reverting to Production

To switch back to production mail settings, restore your original mail configuration in the `.env` file:

```env
MAIL_MAILER=smtp
MAIL_HOST=mail.privateemail.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
# ... other production settings
``` 