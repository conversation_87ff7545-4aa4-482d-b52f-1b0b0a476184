up:
	./vendor/bin/sail up -d

down:
	./vendor/bin/sail down

bbash:
	./vendor/bin/sail bash

pint:
	./vendor/bin/sail pint --dirty

stan:
	./vendor/bin/sail composer analyze
ci:
	./vendor/bin/sail pint
	./vendor/bin/sail composer analyze

migrate:
	./vendor/bin/sail artisan migrate
reset-db:
	./vendor/bin/sail artisan migrate:fresh --seed
helper:
	./vendor/bin/sail artisan ide-helper:generate
	./vendor/bin/sail artisan ide-helper:models
	./vendor/bin/sail artisan ide-helper:meta
expose:
	expose share --subdomain=ils --server=eu-1 http://localhost


# Variables
REMOTE_USER=root
REMOTE_HOST=*************

LOCAL_DB_CONTAINER=mysql
LOCAL_DB_HOST=mysql
LOCAL_DB_NAME=laravel
LOCAL_DB_USER=root
LOCAL_DB_PASSWORD='password'

DUMP_FILE=./remote_dump.sql
dump-db:
	ssh $(REMOTE_USER)@$(REMOTE_HOST) "mysqldump -u $(LOCAL_DB_USER) -p'$(LOCAL_DB_PASSWORD)' $(LOCAL_DB_NAME)" > $(DUMP_FILE)

import-db:
	@echo "Importing database dump into local MySQL..."
	ssh $(REMOTE_USER)@$(REMOTE_HOST) "mysqldump  --ignore-table=$(LOCAL_DB_NAME).webhooks -u $(LOCAL_DB_USER) -p'$(LOCAL_DB_PASSWORD)' $(LOCAL_DB_NAME)" > $(DUMP_FILE)
	cat $(DUMP_FILE) | docker compose exec -T mysql mysql -u sail -p'password' laravel
	rm -f $(DUMP_FILE)
	@echo "Database dump file removed."
	@echo "Database import completed."

import-db-all:
	@echo "Importing database dump into local MySQL..."
	ssh $(REMOTE_USER)@$(REMOTE_HOST) "mysqldump -u $(LOCAL_DB_USER) -p'$(LOCAL_DB_PASSWORD)' $(LOCAL_DB_NAME)" > $(DUMP_FILE)
	cat $(DUMP_FILE) | docker compose exec -T mysql mysql -u sail -p'password' laravel
	rm -f $(DUMP_FILE)
	@echo "Database dump file removed."
	@echo "Database import completed."
# Variables
REMOTE_LOG_PATH=/var/www/html/Treek/storage/logs
LOCAL_LOG_PATH=./storage/logs

import-logs:
	@echo "Importing log files from remote server..."
	scp $(REMOTE_USER)@$(REMOTE_HOST):$(REMOTE_LOG_PATH)/*.log $(LOCAL_LOG_PATH)/
	@echo "Log files imported to $(LOCAL_LOG_PATH)."
