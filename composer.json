{"name": "laravel/laravel", "type": "project", "description": "Treek", "keywords": ["laravel", "framework"], "license": "MIT", "require": {"php": "^8.3", "aymanalhattami/filament-context-menu": "^1.0", "barryvdh/laravel-dompdf": "^3.0", "barryvdh/laravel-snappy": "^1.0", "bezhansalleh/filament-language-switch": "^3.1", "bezhansalleh/filament-shield": "^3.3", "dutchcodingcompany/filament-developer-logins": "^1.4", "eightynine/filament-excel-import": "^3.1", "filament/filament": "^3.3", "gboquizosanchez/filament-log-viewer": "^1.2", "guzzlehttp/guzzle": "^7.9", "h4cc/wkhtmltoimage-amd64": "0.12.x", "h4cc/wkhtmltopdf-amd64": "0.12.x", "khaled.alshamaa/ar-php": "^6.3", "laravel/framework": "^12.0", "laravel/horizon": "^5.33", "laravel/telescope": "^5.2", "livewire/livewire": "^3.5", "maatwebsite/excel": "*", "picqer/php-barcode-generator": "^3.2", "pxlrbt/filament-environment-indicator": "^2.1", "pxlrbt/filament-excel": "^2.4", "rmsramos/activitylog": "^1.0", "salla/ouath2-merchant": "^1.4", "sentry/sentry-laravel": "^4.13", "setasign/fpdi-fpdf": "^2.3", "shopify/shopify-api": "^5.6", "shuvroroy/filament-spatie-laravel-backup": "^2.2", "shuvroroy/filament-spatie-laravel-health": "^2.1", "simplesoftwareio/simple-qrcode": "^4.2", "stechstudio/filament-impersonate": "^3.15", "swisnl/filament-backgrounds": "^1.1", "symfony/brevo-mailer": "^7.3", "symfony/http-client": "^7.3", "tapp/filament-value-range-filter": "^1.0", "valentin-morice/filament-json-column": "^2.0", "vicmans/filament-number-input": "^1.0", "webbingbrasil/filament-advancedfilter": "^3.0", "ysfkaya/filament-phone-input": "^3.1"}, "require-dev": {"barryvdh/laravel-debugbar": "^3.14", "barryvdh/laravel-ide-helper": "^3.3", "fakerphp/faker": "^1.23", "larastan/larastan": "^3.0", "laravel/pint": "^1.17", "laravel/sail": "^1.32", "mockery/mockery": "^1.6", "nunomaduro/collision": "^8.0", "pestphp/pest": "^3.7", "pestphp/pest-plugin-livewire": "^3.0", "spatie/laravel-ignition": "^2.4"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}, "files": ["app/Helpers/helpers.php"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi", "@php artisan filament:upgrade"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi", "@php -r \"file_exists('database/database.sqlite') || touch('database/database.sqlite');\"", "@php artisan migrate --graceful --ansi"], "analyze": ["vendor/bin/phpstan analyse"], "pint": ["vendor/bin/pint"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true, "php-http/discovery": true}}, "minimum-stability": "beta", "prefer-stable": true}