<?php

return [
    'Menu' => 'Menu',
    'Dashboards' => 'Dashboard',
    'Default' => 'Default',
    'Saas' => 'Saas',
    'Crypto' => 'Crypto',
    'Blog' => 'Blog',
    'Layouts' => 'Layouts',
    'Vertical' => 'Vertical',
    'Light_Sidebar' => 'Light Sidebar',
    'Compact_Sidebar' => 'Compact Sidebar',
    'Icon_Sidebar' => 'Icon Sidebar',
    'Boxed_Width' => 'Boxed Width',
    'Preloader' => 'Preloader',
    'Colored_Sidebar' => 'Colored Sidebar',
    'Scrollable' => 'Scrollable',
    'Horizontal' => 'Horizontal',
    'Topbar_Light' => 'Topbar Light',
    'Colored_Header' => 'Colored Header',
    'Apps' => 'Apps',
    'Calendars' => 'Calendars',
    'TUI_Calendar' => 'TUI Calendar',
    'Full_Calendar' => 'Full Calendar',
    'Chat' => 'Chat',
    'New' => 'New',
    'File_Manager' => 'File Manager',
    'Ecommerce' => 'Ecommerce',
    'Products' => 'Products',
    'Product_Detail' => 'Product Detail',
    'Orders' => 'Orders',
    'order' => 'Order',
    'Customers' => 'Customers',
    'Cart' => 'Cart',
    'Checkout' => 'Checkout',
    'Shops' => 'Shops',
    'Add_Product' => 'Add Product',
    'Wallet' => 'Wallet',
    'Buy_Sell' => 'Buy/Sell',
    'Exchange' => 'Exchange',
    'Lending' => 'Lending',
    'KYC_Application' => 'KYC Application',
    'ICO_Landing' => 'ICO Landing',
    'Email' => 'Email',
    'Inbox' => 'Inbox',
    'Read_Email' => 'Read Email',
    'Templates' => 'Templates',
    'Basic_Action' => 'Basic Action',
    'Alert_Email' => 'Alert Email',
    'Billing_Email' => 'Billing Email',
    'Invoices' => 'Invoices',
    'Invoice_List' => 'Invoice List',
    'Invoice_Detail' => 'Invoice Detail',
    'Projects' => 'Projects',
    'Projects_Grid' => 'Projects Grid',
    'Projects_List' => 'Projects List',
    'Project_Overview' => 'Project Overview',
    'Create_New' => 'Create New',
    'Tasks' => 'Tasks',
    'Task_List' => 'Task List',
    'Kanban_Board' => 'Kanban Board',
    'Create_Task' => 'Create Task',
    'Contacts' => 'Contacts',
    'User_Grid' => 'User Grid',
    'User_List' => 'User List',
    'Profile' => 'Profile',
    'Blog_List' => 'Blog List',
    'Blog_Grid' => 'Blog Grid',
    'Blog_Details' => 'Blog Details',
    'Pages' => 'Pages',
    'Authentication' => 'Authentication',
    'Login' => 'Login',
    'Register' => 'Register',
    'Recover_Password' => 'Recover Password',
    'Lock_Screen' => 'Lock Screen',
    'Confirm_Mail' => 'Confirm Mail',
    'Email_verification' => 'Email verification',
    'Two_step_verification' => 'Two step verification',
    'Utility' => 'Utility',
    'Starter_Page' => 'Starter Page',
    'Maintenance' => 'Maintenance',
    'Coming_Soon' => 'Coming Soon',
    'Timeline' => 'Timeline',
    'FAQs' => 'FAQs',
    'Pricing' => 'Pricing',
    'Error_404' => 'Error 404',
    'Error_500' => 'Error 500',
    'Components' => 'Components',
    'UI_Elements' => 'UI Elements',
    'Alerts' => 'Alerts',
    'Buttons' => 'Buttons',
    'Cards' => 'Cards',
    'Carousel' => 'Carousel',
    'Dropdowns' => 'Dropdowns',
    'Grid' => 'Grid',
    'Images' => 'Images',
    'Lightbox' => 'Lightbox',
    'Modals' => 'Modals',
    'Offcanvas' => 'Off Canvas',
    'Range_Slider' => 'Range Slider',
    'Session_Timeout' => 'Session Timeout',
    'Progress_Bars' => 'Progress Bars',
    'Sweet_Alert' => 'Sweet-Alert',
    'Tabs_&_Accordions' => 'Tabs & Accordions',
    'Typography' => 'Typography',
    'Video' => 'Video',
    'General' => 'General',
    'Colors' => 'Colors',
    'Rating' => 'Rating',
    'Notifications' => 'Notifications',
    'Forms' => 'Forms',
    'Form_Elements' => 'Form Elements',
    'Form_Layouts' => 'Form Layouts',
    'Form_Validation' => 'Form Validation',
    'Form_Advanced' => 'Form Advanced',
    'Form_Editors' => 'Form Editors',
    'Form_File_Upload' => 'Form File Upload',
    'Form_Xeditable' => 'Form Xeditable',
    'Form_Repeater' => 'Form Repeater',
    'Form_Wizard' => 'Form Wizard',
    'Form_Mask' => 'Form Mask',
    'Tables' => 'Tables',
    'Basic_Tables' => 'Basic Tables',
    'Data_Tables' => 'Data Tables',
    'Responsive_Table' => 'Responsive Table',
    'Editable_Table' => 'Editable Table',
    'Charts' => 'Charts',
    'Apex_Charts' => 'Apex Charts',
    'E_Charts' => 'E Charts',
    'Chartjs_Charts' => 'Chartjs Charts',
    'Flot_Charts' => 'Flot Charts',
    'Toast_UI_Charts' => 'Toast UI Charts',
    'Jquery_Knob_Charts' => 'Jquery Knob Charts',
    'Sparkline_Charts' => 'Sparkline Charts',
    'Icons' => 'Icons',
    'Boxicons' => 'Boxicons',
    'Material_Design' => 'Material Design',
    'Dripicons' => 'Dripicons',
    'Font_awesome' => 'Font awesome',
    'Maps' => 'Maps',
    'Google_Maps' => 'Google Maps',
    'Vector_Maps' => 'Vector Maps',
    'Leaflet_Maps' => 'Leaflet Maps',
    'Multi_Level' => 'Multi Level',
    'Level_1.1' => 'Level 1.1',
    'Level_1.2' => 'Level 1.2',
    'Level_2.1' => 'Level 2.1',
    'Level_2.2' => 'Level 2.2',
    'Search' => 'Search...',
    'Mega_Menu' => 'Mega Menu',
    'UI_Components' => 'UI Components',
    'Applications' => 'Applications',
    'Extra_Pages' => 'Extra Pages',
    'Horizontal_layout' => 'Horizontal layout',
    'View_All' => 'View All',
    'Your_order_is_placed' => 'Your order is placed',
    'If_several_languages_coalesce_the_grammar' => 'If several languages coalesce the grammar',
    '3_min_ago' => '3 min ago',
    'James_Lemire' => 'James Lemire',
    'It_will_seem_like_simplified_English' => 'It will seem like simplified English.',
    '1_hours_ago' => '1 hours ago',
    'Your_item_is_shipped' => 'Your item is shipped',
    'Salena_Layfield' => 'Salena Layfield',
    'As_a_skeptical_Cambridge_friend_of_mine_occidental' => 'As a skeptical Cambridge friend of mine occidental.',
    'View_More' => 'View More..',
    'My_Wallet' => 'My Wallet',
    'Settings' => 'Settings',
    'Lock_screen' => 'Lock screen',
    'Logout' => 'Logout',
    'Edit_Details' => 'Edit Details',
    'Placeholders' => 'Placeholders',
    'Toasts' => 'Toasts',
    'Jobs' => 'Jobs',
    'Job_List' => 'Job List',
    'Job_Grid' => 'Job Grid',
    'Apply_Job' => 'Apply Job',
    'Job_Details' => 'Job Details',
    'Jobs_Categories' => 'Jobs Categories',
    'Candidate' => 'Candidate',
    'List' => 'List',
    'Overview' => 'Overview',
    'Utilities' => 'Utilities',
    'hot' => 'Hot',
    'backend' => 'Backend',
    'yajra-datatable' => 'Yajra Datatable',
    'integration' => 'Integrations',

    'shipping_partners' => 'Shipping Partners',
    'Sales_Channels' => 'Sales Channels',
    'management' => 'Management',
    'price_calculator' => 'Price Calculator',
    'home' => 'Home',
    'more_connections' => 'More Connections',
    'connected_sales_channels' => 'Connected Sales Channels',
    'connect_your_store_title' => 'Connect your online store',
    'connect_your_store_description' => 'Connect your online store to our powerful dashboard, and watch as your store orders are effortlessly captured and synchronized in real-time. Say goodbye to manual data entry and hello to streamlined efficiency.',
    'reports' => 'Reports',
    'dashboard' => 'Dashboard',
    'analysis' => 'Analysis',
    'charge' => 'Charge',
    'pickup_location' => 'Pickup location',
    'shipments' => 'Shipments',
    'import_product' => 'Import Product',
    'map_view' => 'Map View',
    'import_data' => 'Import Data',
    'pending_orders' => 'Pending Orders',
    'awaiting_pickup' => 'Awaiting Pickup',
    'currently_shipping' => 'Currently Shipping',
    'shipment_on_hold' => 'Shipment on Hold',
    'delivered' => 'Delivered',
    'returned' => 'Returned',
    'canceled_orders' => 'Canceled Orders',
    'all_orders' => 'All Orders',
    'status' => 'Status',
    'order_grand_total' => 'Order Grand Total',
    'receiver' => 'Receiver',
    'user' => 'User',
    'boxes_count' => 'Boxes Count',
    'previous' => 'Previous',
    'next' => 'Next',
    'import' => 'Import',
    'product_title' => 'Products',
    'product_import' => 'Import Products',
    'product_synchronize' => 'Synchronize with Shopify',
    'product_add' => 'Add Product',
    'product_add_title' => 'Add New Product',
    'product_view_title' => 'View Product',
    'product_edit_title' => 'Edit Product',
    'product_select_edit' => 'Please Select a Product to Edit',
    'product_product_name' => 'Product Name',
    'product_sku' => 'sku',
    'product_price' => 'Price',
    'product_tax_amount' => 'Tax Amount',
    'product_barcode' => 'Barcode',
    'product_category' => 'Category',
    'product_actions' => 'Actions',
    'product_min_price' => 'Minimum Price',
    'product_max_price' => 'Maximum Price',
    'order_info' => 'Order Information',
    'receiver_info' => 'Receiver Information',
    'first_name' => 'First Name',
    'last_name' => 'Last Name',
    'phone_number' => 'Phone Number',
    'email' => 'Email',
    'enter_first_name' => 'Enter Your First Name',
    'enter_last_name' => 'Enter Last Name',
    'enter_phone_number' => 'Enter Your Phone Number',
    'receiver_address_details' => 'Receiver Address Details',
    'country' => 'Country',
    'select_country' => 'Select Country',
    'kingdom_of_saudi_arabia' => 'Kingdom of Saudi Arabia',
    'address_type' => 'Address Type',
    'short_address_code_national_address' => 'Short Address Code / National Address',
    'manual_address_entry' => 'Manual Address Entry',
    'fill_from_google_maps' => 'Fill from Google Maps',
    'short_address_code_placeholder' => 'Short Address Code or Officially Registered National Address',
    'city' => 'City',
    'enter_city' => 'Enter City',
    'address_line' => 'Address Line',
    'enter_address_line' => 'Enter Address Line',
    'postal_code' => 'Postal Code',
    'enter_postal_code' => 'Enter Postal Code',
    'select_location_from_map' => 'Select Location from Map',
    'short_address_code' => 'Short Address Code',
    'enter_email' => 'Enter Your Email',
    'order_details' => 'Order Details',
    'products_details' => 'Products Details',
    'boxes_details' => 'Boxes Details',
    'boxes' => 'Boxes',
    'product' => 'Product',
    'quantity' => 'Quantity',
    'remove' => 'Remove',
    'add_box' => 'Add Box',
    'enter_box_name' => 'Enter Box Name',
    'enter_order_number' => 'Enter Order Number',
    'select_date' => 'Select Date',
    'select_payment_method' => 'Select Payment Method',
    'select_currency' => 'Select Currency',
    'select_warehouse' => 'Select Warehouse',
    'save_order' => 'Save Order',
    'search_product' => 'Search for a Product',
    'type' => 'Type',

    'search_box_type' => 'Box Type Search',
    'back' => 'Back',
    'submit_order' => 'Submit Order',
    'success_message' => 'Product Added Successfully!',
    'error_message' => 'An error occurred while adding the product.',
    'product_name' => 'Product Name',

    'tax_amount' => 'Tax Amount',
    'category' => 'Category',
    'image' => 'Image',
    'other_barcodes' => 'Other Barcodes',
    'add_barcode' => 'Add Barcode',
    'profile_settings' => 'Profile Settings',
    'my_profile' => 'My Profile',
    'company' => 'Company',
    'profile_change_phone_message' => 'If you change your mobile number, it has to be verified again!',
    'system_preferences' => 'System Preferences',
    'system_preferences_description' => 'You can adjust your theme, font size, and default page settings.',
    'configure' => 'Configure',
    'verify' => 'Verify',
    'welcome' => 'Welcome',
    'welcome_description' => 'Let\'s create your first shipment together!',
    'create_first_shipment_message' => 'Ready to create your first shipment?',
    'ship_first_order_button' => 'Ship Your First Order',
    'setup_pickup_location' => 'Setup Pickup Location',
    'setup_pickup_location_description' => 'Start now by adding your order pickup location via Treek to begin shipping with ease.',
    'activate' => 'Activate',
    'change_status' => 'Change Status',
    'shopify' => 'Shopify',
    'shopify_description' => 'A global commerce platform to sell online, offline, and everywhere in between.',
    'connect' => 'Connect',
    'salla' => 'Salla',
    'salla_description' => 'Connect your Salla store with Treek application in Salla apps store',
    'connection_instructions' => 'Connection Instructions',
    'zid' => 'Zid',
    'zid_description' => 'Grow your business, sell, ship, and process your payments from 1 dashboard.',
    'unverified_email_address' => 'Unverified Email Address',
    'unverified_phone_number' => 'Unverified Phone Number',
    'save' => 'Save',
    'edit' => 'Edit',
    'orders_list' => 'Orders List',
    'products' => 'Products',
    'add_order' => 'Add Order',
    'add_product' => 'Add Product',
    'order_number' => 'Order Number',
    'currency' => 'Currency',

    'total_weight' => 'Total Weight',
    'actions' => 'Actions',
    'description' => 'Description',
    'date' => 'Date',
    'order_date' => 'Date',
    'create_shipment' => 'Create Shipment',
    'shipment_with_treek_rates' => 'Shipment With Treek Rates',
    'shipment_with_my_rates' => 'Shipment With My Rates',
    'shipment_preferences' => 'Shipment Preferences',
    'question' => 'Question',
    'answer' => 'Answer',
    'faq_section' => 'Section',
    'new_orders' => 'New Orders',
    'product' => [
        'name' => 'Name',
        'sku' => 'sku',
        'price' => 'Price',
        'tax_amount' => 'Tax Amount',
        'currency' => 'Currency',
        'tax_currency' => 'Tax Currency',
        'barcode' => 'Barcode',
        'category' => 'Category',
        'description' => 'Description',
        'cubic_meter' => 'Cubic Meter',
        'length' => 'Length',
        'width' => 'Width',
        'height' => 'Height',
        'total_weight' => 'Total Weight',
        'image' => 'Image',
        'merchant' => 'Merchant',
        'external_id' => 'External ID',
        'products' => 'Products',
    ],
    'length' => 'Length',
    'width' => 'Width',
    'height' => 'Height',
    'box_type' => 'Box Type',
    'warehouses' => 'Warehouses',
    'warehouse' => [
        'name' => 'Warehouse Name',
        'code' => 'Warehouse Code',
        'city' => 'City',
        'country' => 'Country',
        'detailed_address' => 'Detailed Address',
        'address' => 'Address',
        'status' => 'Status',
        'sender_name' => 'Sender Name',
        'sender_phone' => 'Sender Phone',
        'sender_email' => 'Sender Email',
        'zip_code' => 'Zip Code',
    ],
    'add_warehouse' => 'Add Warehouse',
    'connected_shipping_partners' => 'Connected Shipping Partners',
    'shipment_reference' => 'Shipment Reference',
    'receiver_first_name' => 'Receiver First Name',
    'receiver_address_line' => 'Receiver Address Line',
    'shipment_information' => 'Shipment Information',
    'shipment_confirmation' => 'Shipment Confirmation',
    'choose_order' => 'Choose Order',
    'order_from' => 'Order From',
    'order_to' => 'Order To',
    'select_courier_service_error' => 'Please select a courier service before submitting.',
    'shipment_pre_created' => 'Order created, Shipment will be created automatically in minutes.',
    'shipping_company' => 'Shipping Company',
    'shipping_id' => 'Shipping Reference',
    'shipment_created' => 'Shipment created',
    'picked_up' => 'Picked up',
    'held_for_pickup' => 'Held for pickup',
    'order_histories' => 'Order histories',
    'undefined' => 'Not processed By System',
    'show_order_histories' => 'Show order histories',
    'shipment_my_rates' => 'Shipping at my own rates',
    'receiver_city' => 'Receiver City',
    'shipment_company' => 'Shipment Company',
    'order_updated' => 'Order Canceled',
    'order_delivered' => 'Order Delivered',
    'wallet_transactions' => 'Wallet Transactions',
    'wallet_transactions_filter' => 'Wallet Transactions',
    'all_orders' => 'All Orders',
    'has_wallet_transactions' => 'Has Wallet Transactions',
    'no_wallet_transactions' => 'No Wallet Transactions',
    'payment_processing' => 'Your payment is being processed and will be completed within 24 hours.',
    'box' => 'Box',

    // Navbar Translations

    'solutions' => 'Solutions',
    'faqs' => 'FAQs',

    'our_story' => 'Our Story',
    'track_your_shipment' => 'Track Your Shipment',
    'log_in' => 'Log In',
    'register_now' => 'Register Now',

    // About Section
    'about_us_title' => 'Who are we?',
    'about_us_description' => 'Treek provides a simple and unique shipping experience by bringing together shipping companies on one platform and enabling business owners to choose the companies that suit their e-commerce.',

    // Vision Section
    'our_vision_title' => 'Our Vision',
    'our_vision_description' => 'To be a comprehensive, all-in-one platform that includes the best shipping companies in the region, enabling business owners to meet all their needs and simplify their operations by providing innovative solutions, planning for growth, and integrating technology into our services.',

    // Values Section
    'our_values_title' => 'Our Values',
    'achievement' => 'Achievement',
    'achievement_description' => 'Our outstanding achievements for our clients are the foundation of our leadership, and our constant focus on proper execution drives us towards success.',
    'motivation' => 'Motivation',
    'motivation_description' => 'We work together to create a motivating work environment, proactively offering solutions and delivering results for our clients.',
    'creativity' => 'Creativity',
    'creativity_description' => 'We challenge conventional thinking and seek new, innovative ways to discover opportunities, whether in products or processes, aiming for creative solutions.',

    // CEO Message Section
    'ceo_message_title' => 'CEO\'s Message',
    'ceo_message_description' => 'Committing to running a business is challenging. It requires a clear vision, flexibility, resilience, belief in success, and perseverance in the face of all obstacles. When I built Treek, I was fully aware of the inevitability of facing various challenges in the e-commerce sector in the Arab world. I aimed to reduce the burdens for business owners—like myself—who deal daily with different pressures in managing their businesses. I always receive continuous support from my family, partners, and team. I understand that staying focused on the goal and not deviating from the path is difficult, but we promise to stay committed to our mission, because the growth of your business is our goal. We understand your challenges, and we are with you.',
    'ceo_name' => 'Tarek Mahmoud',
    // Shipping Price Comparison Section
    'shipping_comparison_title' => 'Compare Shipping and Delivery Prices in Saudi Arabia',
    'shipping_comparison_subtitle' => 'Calculate cost, delivery time, and ship with the best shipping companies at preferential rates.',
    'departure_city_label' => 'Departure City',
    'departure_city_placeholder' => 'Enter the address',
    'destination_city_label' => 'Destination City',
    'destination_city_placeholder' => 'Enter the address',
    // Hero Section
    'hero_title' => 'The Best Shipping Companies <span class="d-block"> On One Platform </span>',
    'hero_subtitle' => 'Treek is a shipping and delivery gateway. We enable you to ship with the best delivery companies and enjoy all our technological and digital solutions from one platform without the need for contracts <span class="d-block"> and completely free of charge </span>',
    'start_free_button' => 'Start Free Now',
    'contact_sales_button' => 'Contact Sales Team',

    // Services Section
    'ship' => 'Ship',
    'manage' => 'Manage',
    'track' => 'Track',
    'analyze' => 'Analyze',

    // Partners Section
    'partners_trust_us' => 'Our Partners Trust Us',

    // Integrated Shipping Portal Section

    'lms' => 'Logistics Management System (LMS)',
    'rms' => 'Returns Management System (RMS)',
    'oms' => 'Order Management System (OMS)',
    'platform_integration' => 'Integration with Sales Platforms',
    'analytics_reporting' => 'Comprehensive Analytics and Reporting System',
    'operation_automation' => 'Operations Automation',

    // Needs Section
    'accreditations' => 'Accreditations',
    // FAQ Section
    'faq_title' => 'Have a Question?<br/> Get Your Answer Here',
    'faq_subtitle' => 'On this page, we\'ve answered the frequently asked questions.',
    'faq_services' => 'Our Services',
    'faq_shipment' => 'Shipping',
    'faq_link' => 'Integration',
    // Tracking Page
    'main_title' => 'Track Your Shipment Quickly and Easily',
    'tracking_number_label' => 'Tracking Number',
    'tracking_number_placeholder' => 'Enter the tracking number',
    'order_number_label' => 'Order Number',
    'order_number_placeholder' => 'Enter the order number',
    'search_button' => 'Search',
    'additional_info' => 'You can track orders shipped only through the Treek portal.',
    'footer_email' => '<EMAIL>',
    'footer_copyright' => 'TREEK Global Inc. © 2025',
    // Hero Section

    // Shipping Portal Section
    'shipping_portal_title' => 'Multiple Shipping Options to Meet All Needs',
    'custom_prices_title' => 'Your Custom Prices',
    'custom_prices_description' => 'Easily connect your custom contracts with shipping companies using Treek\'s system and use your own rates.',
    'treek_prices_title' => 'Treek Prices',
    'treek_prices_description' => 'Ship at the best rates with shipping companies without the need for contracts.',

    // Ecommerce Section
    'ecommerce_title' => 'Ship with the Best E-commerce Platforms',
    'ecommerce_description' => 'Connect all your online sales channels with Treek\'s portal, which allows you to integrate with 3 global e-commerce platforms without complex coding.',

    // Local and International Shipping Section
    'local_international_title' => 'Ship Locally and Internationally',
    'local_international_point_1' => '✔️ Same-day delivery',
    'local_international_point_2' => '✔️ Chilled and frozen shipping',
    'local_international_point_3' => '✔️ Shipping to all cities',
    'local_international_point_4' => '✔️ Heavyweight and oversized shipping',
    'local_international_point_5' => '✔️ Shipping to all countries worldwide',
    'arabic' => 'Arabic',
    'english' => 'English',
    'show_more' => 'Show more',
    'global_overview' => 'Global overview',
    'waiting_payment' => 'Waiting payment',
    'shipped' => 'Shipped',
    'total_price' => 'Total price',
    'total_price_shipment' => 'Total shipment price',
    'total_price_shipment_treeq' => 'Total shipment price (Delivered by Treeq)',
    'total_price_shipment_other' => 'Total shipment price (Delivered by other methods)',
    'shipment_credentials_type' => 'Shipment method',
    'with_application' => 'With treeq',
    'without_application' => 'Without other methods',
    'total_price_shipped' => 'Total shipped',
    'account_recharge' => 'Account Recharge',
    'start' => 'Start',
    'shipping_box_dimensions' => 'Specify the dimensions of your shipping box',
    'shipping_box_time_saver' => 'Enter the dimensions of the shipping boxes you use and save time when creating shipments',
    'book_call' => 'Book a call to help you',
    'schedule_meeting' => 'Choose a suitable time to schedule a meeting with our experts to help you complete your account setup and receive some training',
    'contact_via_chat' => 'Contact us via chat',
    'submit_issue_via_chat' => 'You can simply send your inquiry or issue to us by submitting a request via chat from here.',
    'get_more_knowledge' => 'Get more knowledge',
    'get_more_knowledge_treek' => 'Gain more knowledge by reviewing these frequently asked questions, which may help you learn how to use Treek.',
    'reverse_shipment_with_treek_rates' => 'Reverse shipment treek rates',
    'reverse_shipment_my_rates' => 'Reverse shipment my rates',
    'print' => 'Print',
    'open_shipment_tracking_link' => 'Open shipment tracking link',
    'delivery_time' => 'From 2 to 4 business days',
    'same_day' => 'Same day',
    'work_days' => '1 to 3 business days',
    'delivery_by_shipping_company' => 'Delivery by shipping company',
    'delivery_to_customer' => 'Delivery to customer',
    'aramex' => 'Aramex',
    'transcorp' => 'Transcorp',
    'barq' => 'Barq',
    'thabit' => 'Thabit',
    'jt' => 'J&T',
    'spl' => 'Spl',
    'fast_shipping' => 'Fast Shipping',
    'heavy_and_bulk_shipping' => 'Heavy and Bulk Shipping',
    'price' => 'Price',
    'service_type' => 'Service Type',
    'delivery_timee' => 'Delivery Time',
    'order_pickup_method' => 'Order Pickup Method',
    'choose' => 'Choose',
    'inactive_shipping_company' => 'The selected shipping company is currently inactive',
    'insufficient_wallet_balance' => 'Wallet balance is insufficient to complete this transaction.',
    'shipping_company_selected' => 'The shipping company has been successfully selected',
    'stores_synchronization' => 'Stores Synchronization',
    'sales_channels' => 'Sales Channels',
    'service_inactive' => 'This service is currently inactive. Please contact us',
    'shopify_descriptionn' => 'Seamlessly connect your online and offline business with an exceptional global commerce platform.',
    'salla_descriptionn' => 'Connect your Salla store with the Treek app through the Salla App Store.',
    'zid_descriptionn' => 'From a single dashboard, you will be able to manage and grow your business, sell, ship, and process payments with ease',
    'amount' => 'Amount',
    'welcome_to_treek' => 'Welcome to Treek',
    'have_ecommerce_store' => 'Do you currently have an Ecommerce Store?',
    'auto_shipping_info' => 'With Treek, you can save up to 90% on shipping costs with the best local and international shipping companies, without the need to sign any contracts with any shipping company.  We want to know more about your business to help you better ship your orders!',
    'have_online_store' => 'Do you have an online store?',

    'yes' => 'Yes',
    'no' => 'No',
    'Yes' => 'Yes',
    'No' => 'No',
    'salla_webhook' => 'Salla Webhook',
    'salla_webhook_instructions' => 'Please follow the steps shown in the video to complete the integration process via Webhook if you are a subscriber of the Salla Pro package, noting that the video only explains how to sync new orders. If you are a subscriber of any Salla package, you can schedule an appointment with the treek team to assist you with the integration process. Book here.',
    'salla_store_connection' => 'Follow the steps below to connect your Salla store to Treek via Webhook to sync new orders.',
    'woocommerce_webhook' => 'WooCommerce Webhook',
    'woocommerce_webhook_instructions' => 'Please follow the steps shown in the video to complete the integration process via Webhook if you are a subscriber of the WooCommerce Pro package, noting that the video only explains how to sync new orders. If you are a subscriber of any WooCommerce package, you can schedule an appointment with the treek team to assist you with the integration process. Book here.',
    'woocommerce_store_connection' => 'Follow the steps below to connect your WooCommerce store to Treek via Webhook to sync new orders.',
    'instructions' => 'Instructions',
    'account_info' => 'Your Account Information',
    'merchant_name' => 'Merchant Name',
    'domain' => 'Domain',
    'active' => 'Active',
    'webhook_url' => 'Webhook URL',
    'test_connection' => 'Test Connection',
    'main_warehouse' => 'Main Warehouse',
    'username' => 'Username',
    'password' => 'Password',
    'account_number' => 'Account Number',
    'account_entity' => 'Account Entity',
    'account_pin' => 'Account PIN',
    'shipping_company_inactive' => 'The selected shipping company is currently inactive. Please contact us.',
    'no_shipments_found' => 'No shipments found',
    'tools' => 'Tools',
    'store' => 'Store',
    'mobile_number' => 'Mobile Number',
    'address_details' => 'Detailed Address',
    'street_name' => 'Street Name',
    'building_name_or_number' => 'Building Name/Number',
    'order_numberr' => 'Order Number',
    'generate' => 'Generate',
    'payment_method' => 'Payment Method',
    'order_value' => 'Order Value',
    'sku' => 'SKU',
    'weight' => 'Weight',
    'weight_unit' => 'Weight Unit',
    'tax' => 'Tax',
    'total' => 'Total',
    'select_max_1_orders' => 'Please select maximum 1 orders',
    'select_max_50_orders' => 'Please select maximum 50 orders',
    'select_max5_orders' => 'Please select maximum 5 orders',
    'failed_shipment_creation' => 'Failed to create shipment: :message',
    'custom_shipping_rates' => 'Shipping with My Rates',
    'shipment_awb' => 'Shipment AWB',
    'proforma_invoice' => 'Proforma Invoice',
    'edit_order' => 'Edit Order',
    'cancel_order' => 'Cancel Order',
    'view_order' => 'View Order',
    'mark_as_delivered' => 'Mark as Delivered',
    'reassign_order' => 'Reassign Order',
    'order_reassigned_successfully' => 'The order has been reassigned successfully',
    'order_duplicated_successfully' => 'Order duplicated successfully',
    'view_order_contents' => 'View Order Contents',
    'add_new_row' => 'Add a New Row',
    'warehouse_name' => 'Warehouse Name',
    'number_of_orders' => 'Number of Orders',
    'order_distribution_weekly' => 'Order Distribution (Weekly)',
    'warehouse_code' => 'Warehouse Code',
    'warehouse_sender_phone' => 'Warehouse Sender Phone',
    'warehouse_city' => 'Warehouse City',
    'warehouse_address' => 'Warehouse Address',
    'warehouse_status' => 'Warehouse Status',
    'warehouse_detailed_address' => 'Warehouse Detailed Address',
    'warehouse_zip_code' => 'Warehouse Zip Code',
    'warehouse_sender_name' => 'Warehouse Sender Name',
    'warehouse_sender_email' => 'Warehouse Sender Email',

    'name' => 'Name',
    'cities' => 'Cities',
    'code_country' => 'Country Identifier',
    'countries' => 'Countries',
    'currencies' => 'Currencies',
    'code' => 'Code',
    'merchants' => 'Merchants',
    'id' => 'ID',

    'receiver_last_name' => 'Receiver Last Name',
    'shipper_name' => 'Shipper Name',
    'shipper_email' => 'Shipper Email',
    'shipper_phone' => 'Shipper Phone',
    'shipment_tracking_link' => 'Shipment Tracking Link',
    'shipment_status' => 'Shipment Status',

    'shipment_logo' => 'Shipment Logo',
    'shipment_total_weight' => 'Shipment Total Weight',

    'external_id' => 'External ID',
    'webhook_id' => 'Webhook ID',

    'receiver_phone' => 'Receiver Phone',
    'receiver_email' => 'Receiver Email',
    'receiver_country' => 'Receiver Country',
    'receiver_country_code' => 'Receiver Country Code',

    'receiver_block' => 'Receiver Block',
    'receiver_postal_code' => 'Receiver Postal Code',
    'receiver_latitude' => 'Receiver Latitude',
    'receiver_longitude' => 'Receiver Longitude',

    'shipper_country' => 'Shipper Country',
    'shipper_country_code' => 'Shipper Country Code',
    'shipper_city' => 'Shipper City',
    'shipper_address_line' => 'Shipper Address Line',
    'shipper_latitude' => 'Shipper Latitude',
    'shipper_longitude' => 'Shipper Longitude',

    'warehouse_id' => 'Warehouse ID',
    'merchant_id' => 'Merchant ID',
    'merchant_email' => 'Merchant Email',
    'merchant_phone' => 'Merchant Phone',
    'value' => 'Value',
    'key' => 'Key',
    'shipment_courier_services' => 'Shipment Courier Services',
    'courier_name' => 'Courier Name',

    // Courier Status Management
    'courier_status_management' => 'Courier Status Management',
    'courier_status_mapping' => 'Courier Status Mapping',
    'courier_status_mappings' => 'Courier Status Mappings',
    'courier' => 'Courier',
    'status_code' => 'Status Code',
    'status_code_helper' => 'The status code from the courier API',
    'description' => 'Description',
    'description_helper' => 'Human-readable description of the status',
    'order_status_label' => 'Order Status',
    'order_status_helper' => 'The order status this courier status maps to',
    'total_mappings' => 'Total Mappings',
    'all_courier_status_mappings' => 'All courier status mappings',
    'status_mappings' => 'status mappings',
    'all' => 'All',
    'aramex' => 'Aramex',
    'jt_express' => 'JT Express',
    'thabit' => 'Thabit',
    'barq' => 'Barq',
    'transcorp' => 'Transcorp',
    'spl' => 'SPL',
    'created' => 'Created',
    'updated' => 'Updated',

    // Order Status Translations
    'order_status' => [
        'pending' => 'Pending',
        'awaiting_pickup' => 'Awaiting Pickup',
        'currently_shipping' => 'Currently Shipping',
        'delivered' => 'Delivered',
        'returned' => 'Returned',
        'shipment_on_hold' => 'Shipment On Hold',
        'canceled' => 'Canceled',
        'failed' => 'Failed',
        'not_changed' => 'Not Changed',
    ],
    'identifier' => 'Identifier',
    'service_name' => 'Service Name',
    'base_price' => 'Base Price',
    'additional_weight_cost' => 'Additional Weight Cost',
    'cash_on_delivery_cost' => 'Cash on Delivery Cost',
    'distance_cost' => 'Distance Cost',
    'volumetric_divisor' => 'Volumetric Divisor',
    'base_volumetric_divisor' => 'Base Volumetric Divisor',
    'warehouse' => 'Warehouse',
    'name_en' => 'English Name',
    'name_ar' => 'Arabic Name',
    'name_aramex' => 'Aramex Name',
    'name_thabit' => 'Thabit Name',
    'name_salla' => 'Salla Name',
    'name_zid' => 'Zid Name',
    'name_spl' => 'Spl Name',
    'name_salla_second' => 'Salla Second Name',
    'survey_responses' => 'Survey Responses',

    'has_ecommerce_store' => 'Do you have an Ecommerce Store',
    'business_industry' => 'Business Industry',
    'monthly_orders' => 'Monthly Orders',
    'submitted_at' => 'Submitted At',

    'role' => 'Role',
    'email_verified_at' => 'Email Verified At',
    'phone_verified_at' => 'Phone Verified At',

    'dob' => 'Date of Birth',
    'avatar' => 'Avatar',
    'phone' => 'Phone',
    'created_at' => 'Created At',
    'users' => 'Users',
    'error_message1' => 'Error Message',
    're_execute' => 'Re-execute',
    'webhook_reexecuted_successfully' => 'Webhook re-executed successfully!',
    'failed_to_re_execute_webhook' => 'Failed to re-execute webhook',
    'error_re_executing_webhook' => 'Error re-executing webhook',
    'webhooks' => 'Webhooks',
    'survey_statistics' => 'Survey Statistics',
    'ecommerce_store_responses' => 'Ecommerce Store Responses',
    'company_city' => 'Company City',
    'complete_profile' => 'Please complete your profile before payment.',
    'company_name' => 'Company Name',
    'personal_information' => 'Personal Information',
    'company_information' => 'Company Information',
    'commercial_registration_number' => 'Commercial Registration Number',
    'tax_number' => 'Tax Number',
    'calculate_shipping_rates' => 'Calculate Shipping Rates',
    'shipment_countries_prices' => 'Shipment Countries Prices',
    'ask_for_meeting' => 'Ask for meeting for salla integration',
    'cod' => 'Cash on delivery',
    'paid' => 'Paid',
    'debit' => 'debit',
    'credit' => 'credit',
    'refund' => 'refund',
    'next_payment' => 'Next payment',
    'current_cod_balance' => 'Current Cod Balance',
    'cod_wallet_withdrawn' => 'Cod Wallet Withdrawn',
    'cod_wallet' => 'Cod Wallet',
    'create_wallet_transaction' => 'Create Wallet Transaction',
    'wallet_transaction_created_successfully' => 'Wallet transaction created successfully',
    'no_user_found_for_order' => 'No user found for this order',
    'create_wallet' => 'Create Wallet',
    'wallet_created_successfully' => 'Wallet created successfully',
    'duplicate_order' => 'Duplicate Order',
    'order_duplicate_error' => 'You cannot duplicate more than one order.',
    'upload_multiple_orders' => 'Upload Multiple Orders',
    'upload_orders' => 'Upload Orders',
    'upload_orders_salla' => 'Upload Orders Salla',
    'upload_orders_description' => 'Please upload only the file that matches the template provided above',
    'upload_orders_salla_description' => 'Please upload only the file exported from the Salla website',
    'sync_order' => 'Sync Order',
    'enter_order_details' => 'Enter Order Details',
    'choose_connections_method' => 'Choose connections method',
    'salla_connection_type_webhook_description' => 'Connect your Salla store to Treek via Webhook to sync new orders automatically between the two platforms.',
    'next_step_btn' => 'Next step',
    'cancel_btn' => 'Cancel',

    // Shopify connection translations
    'shopify_api_app' => 'Shopify API',
    'shopify_api_app_connection_description' => 'Connect your Shopify store using Private App credentials for secure API integration.',
    'shopify_webhook_app' => 'Shopify App via Webhook',
    'shopify_webhook_app_connection_description' => 'Set up webhook integration to automatically sync new orders from your Shopify store.',

    // Zid connection translations
    'zid_zapier' => 'Link with Zapier',
    'zid_api_connection_description' => 'Connect your Zid store through Zapier for automated workflow integration.',
    'zid_oauth' => 'Link with Zid Application',
    'zid_oauth_connection_description' => 'Connect directly with Zid using OAuth for full integration access.',

    // Zapier integration specific
    'zid_zapier_integration' => 'Zid Integration via Zapier',
    'zid_zapier_connection_description' => 'Set up your Zid store integration through Zapier for automated order processing.',
    'zapier_setup_steps' => 'Zapier Setup Steps:',
    'zapier_step_1' => 'Log in to your Zapier account and create a new Zap',
    'zapier_step_2' => 'Choose Zid as your trigger app and select "New Order" as the trigger event',
    'zapier_step_3' => 'Choose Treek as your action app and select "Create Order" as the action',
    'zapier_step_4' => 'Configure the field mapping between Zid and Treek and test your Zap',
    'zapier_webhook_note' => 'Note: The webhook URL below will be used to receive order data from your Zapier integration.',

    // WooCommerce connection translations
    'woocommerce_rest_api' => 'WooCommerce REST API',
    'woocommerce_rest_api_connection_description' => 'Connect your WooCommerce store using REST API credentials for direct integration.',
    'woocommerce_webhook' => 'WooCommerce Webhook',
    'woocommerce_webhook_connection_description' => 'Set up webhook integration to automatically sync new orders from your WooCommerce store.',
    'widgets' => [
        'daily_orders_distribution' => 'Order Distribution (Last 11 days starting today)',
        'weekly_orders_distribution' => 'Order Distribution (Weekly)',
        'distribution_by_sales_channels' => 'Distribution by sales channels',
        'shipping_companies_distribution_rate' => 'Shipping companies distribution rate',
        'top_destinations' => 'Top destinations',
        'monthly_orders' => 'Monthly Orders',
        'industry_distribution' => 'Industry Distribution',
        'ecommerce_store_responses' => 'Ecommerce Stores Responses',
    ],
    'sunday_label' => 'Sunday',
    'monday_label' => 'Monday',
    'tuesday_label' => 'Tuesday',
    'wednesday_label' => 'Wednesday',
    'thursday_label' => 'Thursday',
    'friday_label' => 'Friday',
    'saturday_label' => 'Saturday',
    'user_shipment_courier_service' => 'Shipment Partner',
    'errors' => 'Errors',
    'bank_information' => 'Bank Information',
    'bank_name' => 'Bank name',
    'bank_account_number' => 'Bank account number',
    'iban' => 'International Bank Account Number (IBAN)',
    'order_status' => [
        'new' => 'New',
        'pending' => 'Pending',
        'awaiting_pickup' => 'Awaiting Pickup',
        'currently_shipping' => 'currently Shipping',
        'delivered' => 'Delivered',
        'returned' => 'Returned',
        'shipment_on_hold' => 'Shipment on Hold',
        'canceled' => 'Canceled',
        'failed' => 'Failed',
        'not_changed' => '',
    ],
    'order_history_events' => [
        'undefined' => 'Undefined',
        'new' => 'New',
        'shipment_created' => 'Shipment Created',
        'searching_driver' => 'Searching Driver',
        'held_for_pickup' => 'Held For Pickup',
        'picked_up' => 'Picked Up',
        'delivered' => 'Delivered',
    ],
    'cancel' => 'Cancel',
    'initial_price' => 'Initial Price',
    'cod_maximum_allowed' => 'Amount must not exceed your current available COD total.',
    'total_with_tax' => 'Total With Tax',
    'must_relate_shipping_cs_first' => 'You must link a shipping company first.',
    'view' => 'View',
    'bank' => 'bank',
    'wallet' => 'wallet',
    'in_progress' => 'In Progress',
    'upload_image' => 'Upload Image',
    'download_invoice' => 'Download Invoice',
    'paid_at' => 'Paid At',
    'accepted' => 'Accepted',
    '' => '',
    'survey' => [
        'responses' => 'Responses',
        'industries' => [
            'Retail' => 'Retail',
            'Health & Beauty' => 'Health & Beauty',
            'Electronics' => 'Electronics',
            'Home & Garden' => 'Home & Garden',
            'Fashion' => 'Fashion',
            'Automotive' => 'Automotive',
            'Food & Beverage' => 'Food & Beverage',
            'Sports & Outdoors' => 'Sports & Outdoors',
        ],
        'orderOptions' => [
            '1-10 orders' => '1-10 orders',
            '11-50 orders' => '11-50 orders',
            '51-100 orders' => '51-100 orders',
            '101-500 orders' => '101-500 orders',
            '500+ orders' => '500+ orders',
        ],
    ],
    'other_destinations' => 'Other Destinations',
    'access_token' => 'Access Token',
    'refresh_token' => 'Refresh Token',
    'expires_in' => 'Expires In',
    'shipment_created_successfully' => 'Shipment created successfully, ID: ',
    'order_errors' => [
        'no_error' => 'no error',
        'wrong_city' => 'wong city',
        'wrong_country' => 'wong country',
        'wrong_receiver_first_name' => 'wrong receiver first name',
        'wrong_receiver_last_name' => 'wrong receiver last name',
        'wrong_reciever_phone_number' => 'wrong reciever phone number',
    ],
    'bank_transfer' => 'Bank Transfer',
    'bank_transfer_message' => 'Please upload a screenshot or proof of your bank transfer.',
    'direct_transfer' => 'Direct Transfer',
    'preview' => 'Preview',
    'transfer_method' => 'Choose a transfer method',
    'submit_transfer' => 'Submit Transfer',
    'bank_transfer_submitted' => 'Bank transfer submitted successfully!',
    'transfer_proof' => 'Transfer Proof',
    'view_proof' => 'View Proof',
    'accept_payment' => 'Accept Payment',
    'status_changed_at' => 'Status Changed At',
    'shipments_costs' => 'Shipments Cost',
    'shipments_charges' => 'Shipments Charges',
    'shipments_numbers' => 'Shipments Numbers',
    'shipments_order_grand_total' => 'Shipments Order Grand Total',
    'delivred_at' => 'Delivred At',
    'start_date' => 'Start Date',
    'end_date' => 'End Date',
    'balance' => 'Balance',
    'balance_recharge_requests' => 'Balance Recharge Requests',
    'refuse_payment_modal_heading' => 'Refuse Payment ?',
    'refuse_payment_modal_description' => 'Are you sure you want to refuse this payment?',
    'download_proof' => 'Download Proof',
    'sum_transfers' => 'Sum Transfers',
    'meeting_date' => 'Meeting Date',
    'meeting_time' => 'Meeting Time',
    'notes' => 'Additional Information',
    'meeting_requested' => 'Meeting requested Successfully',
    'meetings' => 'Meetings',
    'export_shipments' => 'Export Shipments',
    'api_key' => 'api_key',
    'api_secret_key' => 'api_secret_key',
    'setup_webhook' => 'Setup Webhook',
    'admins' => 'Admins',
    'commercials' => 'Commercials',
    'commercial' => 'Commercial',
    'avatar_table_label' => 'Avatar',
    'application_pricing' => 'Application Pricing',
    'user_pricing' => 'User Pricing',
    'other' => 'Other',
    'order_time' => 'Order Time',
    'profits' => 'Profits',
    'cost_cod_type' => 'Cost COD Type',
    'cost_cod_type_options' => [
        'percentage' => 'Percentage',
        'value' => 'Value',
    ],
    'all' => 'All',
    'receiver_city_not_null' => 'Receiver City Is Specified',
    'receiver_city_null' => 'Receiver City Unspecified',
    'update_null_receiver_city_id' => 'Update Receiver City',
    'update_null_receiver_city_id_updated' => 'Receiver City Updated',
    'receiver_country_from_sale_channel' => 'receiver_country_from_sale_channel',
    'receiver_city_from_sale_channel' => 'receiver City From Sale Channel',
    'webhook_is_linked' => 'Webhook Linked',
    'access_token_exist' => 'Access Token Exist',
    'api_key_exist' => 'Api Key Exist',
    'api_secret_key_exist' => 'Api Secret Key Exist',
    'linked' => 'Linked',
    'order_synchronized' => 'The order has been synchronized.',
    'order_sync_failed' => 'order Sync Failed',
    'update_shipment' => 'Update Shipment',
    'update_shipment_description' => 'Enter a shipment reference and click submit to update the shipment status.',
    'enter_shipment_reference' => 'Enter shipment reference',
    'shipment_updated_successfully' => 'Shipment updated successfully',
    'error_updating_shipment' => 'Error updating shipment',
    'command_output' => 'Command Output',
    'error' => 'Error',
    'submit' => 'Submit',
    'required_activated_warehouse' => 'At least one warehouse must be in an active state',
    'not_changed' => 'Not Changed',
    'partial_or_failed_shipments' => 'Partial or failed_shipments',
    'some_shipments_failed_with_ids' => 'Some shipments failed ith ids',
    'some_shipments_failed_check_logs' => 'Some shipments failed check logs',

    'units' => [
        'g' => 'g',
        'kg' => 'Kg',
        'cm' => 'Cm',
    ],
    'channel_status_changed' => 'Channel status changed',

    // Shipment update specific translations
    'shipment_not_found' => 'Shipment not found',
    'shipment_company_not_found' => 'Shipment company not found',
    'unsupported_shipment_company' => 'Unsupported shipment company',
    'has_no_shipment_company' => 'has no shipment company',
    'is_not_supported' => 'is not supported',
    'not_found' => 'not found',
    'company' => 'Company',
    'period' => [
        'period' => 'period',
        'this_day' => 'This day',
        'this_week' => 'This week',
        'this_month' => 'This month',
        'last_month' => 'Last month',
        'this_trimester' => 'This trimester',
        'this_semester' => 'This semester',
        'this_year' => 'This year',
    ],
    'filter' => 'Filter',

    // Missing translations
    'bulk_shipments_all_failed' => 'All Bulk Shipments Failed',
    'bulk_shipments_all_successful' => 'All Bulk Shipments Successful',
    'bulk_shipments_partial' => 'Partial Bulk Shipments',
    'city_source' => 'City Source',
    'city_translations' => 'City Translations',
    'city_value' => 'City Value',
    'cost_additional_weight_cost' => 'Additional Weight Cost',
    'cost_base_price' => 'Base Cost Price',
    'cost_cash_on_delivery_cost' => 'Cash on Delivery Cost',
    'cost_cost_base_price' => 'Cost Base Price',
    'cost_extra_weight_from' => 'Extra Weight From',
    'cost_fuel' => 'Fuel Cost',
    'cost_pricing' => 'Cost Pricing',
    'credentials' => 'Credentials',
    'extra_weight_from' => 'Extra Weight From',
    'extra_weight_price' => 'Extra Weight Price',
    'failed_reverse_shipment_creation' => 'Failed to Create Reverse Shipment',
    'failed_to_generate_invoice' => 'Failed to Generate Invoice',
    'failed_to_print_labels' => 'Failed to Print Labels',
    'kg' => 'KG',
    'open_shipment_tracking_link' => 'Open Shipment Tracking Link',
    'order_id' => 'Order ID',
    'public_pricing' => 'Public Pricing',
    'receiver_street_name' => 'Receiver Street Name',
    'refuse_payment' => 'Refuse Payment',
    'reverse_shipment_created_successfully' => 'Reverse Shipment Created Successfully',
    'salla_ask_for_meeting' => 'Request Meeting for Salla',
    'select_max_1_order' => 'Please Select Maximum 1 Order',
    'shipment_cost' => 'Shipment Cost',
    'shipment_creation_failed' => 'Shipment Creation Failed',
    'trigger_url' => 'Trigger URL',
    'updated_at' => 'Updated At',
    'url' => 'URL',
    'zid_ask_for_meeting' => 'Request Meeting for Zid',
    'zid_store_connection' => 'Zid Store Connection',
    'zid_webhook' => 'Zid Webhook',
    'return_type.' => 'Return Type',
    'missing_credentials' => 'Missing Credentials',
    'no_warehouse_selected' => 'No warehouse selected',
    'no_warehouse_selected_description' => 'please add warehouse to your sale channel before synchronize your order',
    'sar' => 'SAR',
    'application_contract' => 'Treek contract',
    'user_contract' => 'User contract',
    'order_history' => 'Order History',
    'rate_cost' => 'Rate Cost (in cents)',
    'rate_cost_helper' => 'Cost amount in cents (e.g., 100 = 1 SAR)',
    'shipment_company_restriction' => [
        'spl' => [
            'weight_restriction' => [
                'title' => 'Weight Restriction',
                'body' => 'The weight exceeds the limit for SPL. Maximum allowed weight is 30kg.',
            ],
            'mandatory_item_restriction' => [
                'title' => 'Create Shipment Without Products',
                'body' => 'You must have at least one product in the order to create the shipment with SPL.',
            ],
        ],
    ],
    'total_debit' => 'Total Debit',
    'total_charge_cancel' => 'Total Charge + Cancel',
    'difference_charge_cancel_debit' => 'Difference (Charge+Cancel - Debit)',
    'content_total_amount' => 'Content Total Amount',
    'zid_app_connection' => [
        'zid_webhook_instructions' => 'Please follow the steps to complete the linking process (there are images for all steps and a video at the bottom)',
        'title' => 'How to link Zid with Treek via app:',
        'remark' => 'During the linking process, make sure your Treek account remains connected',
        'step_1_title' => 'Click on this button to go to Treek Zid App (You can also find the app on Zid by searching for "Treek" in the "Shipping and Delivery options" page):',
        'step_2_title' => 'Click on the "Start Linking Process" button',
        'step_3_title' => 'Click on the "Next" button',
        'step_4_title' => 'To complete the linking process, you must add at least one specialization. Click on the "Customize Now" button',
        'step_5_title' => 'Then click Next. Enter the customization data here',
        'step_6_title' => 'Here you will find a comprehensive overview of the customizations you have added (you can add more customizations if needed). After that, click the "Next" button',
        'step_7_title' => 'Click on the "Link" button and it will redirect you to the page you will find in step 7',
        'step_7_2_title' => 'Sometimes it may not redirect you directly to the page you will find in step 7. In this case, go back to the "Shipping and Delivery Options" page, then scroll down a bit and you will find the Treek app in an incomplete linking state. Click on the three dots button, then on "Complete Linking", then on "Next", then "Link"',
        'step_8_title' => 'Click on the "Install App" button and it will create a new sales channel specifically for the Zid platform on the Treek platform',
        'step_9_title' => 'Now you have been directed to the sales channel editing page that was created through the app. You can now edit the name and add the main warehouse',
        'general_descrition_1' => 'Now after the linking process, orders can reach from Zid to Treek. However, please note that you need to change the order status in the Zid platform to "Ready"',
        'general_descrition_2' => 'Orders that reach from Zid to Treek will become the same status on both platforms. For example, if the order is changed to "Delivered" in Treek, the order will be changed to "Completed" in Zid',
        'step_2_img_url' => 'build/images/zid_connection_photos/en/step2_en.png',
        'step_3_img_url' => 'build/images/zid_connection_photos/en/step3_en.png',
        'step_4_img_url' => 'build/images/zid_connection_photos/en/step4_en.png',
        'step_5_img_url' => 'build/images/zid_connection_photos/en/step5_en.png',
        'step_6_img_url' => 'build/images/zid_connection_photos/en/step6_en.png',
        'step_7_img_url' => 'build/images/zid_connection_photos/en/step7_en.png',
        'step_7_2_img_url' => 'build/images/zid_connection_photos/en/step7_2_en.png',
        'step_8_img_url' => 'build/images/zid_connection_photos/en/step8_en.png',
        'step_9_img_url' => 'build/images/zid_connection_photos/en/step9_en.png',
        'step_1_button_text' => 'Go to Treek Zid App',
    ],

    'shopify_api_connection' => [
        'shopify_application' => 'Shopify Application via API',
        'title' => 'Steps required to link your Shopify store to Treek via Treek application using API',
        'remark' => 'This method is designed for manual order synchronization with Shopify. For automatic order synchronization, you must link the Shopify application via webhook',
        'shopify_application_instructions' => 'Please follow the steps shown to complete the linking process (there are images for all steps and a video at the bottom)',
        'step_1_title' => 'Open the Treek app installation page in the Shopify App Store. You can open the page directly from the button here or search for it manually. Then click on the "Install" button',
        'step_1_button_text' => 'Go to Treek Shopify App',
        'step_2_title' => 'Click on the "Install" button then click on the app',
        'step_3_title' => 'After clicking on the app, a details page will appear. Click on the "Open app" button to add a new sales channel to Treek and redirect you to it',
        'step_4_title' => 'Now you will find yourself redirected to the sales channel editing page that was created automatically. Now you can change the name that suits you, add the main warehouse and activate it',
        'general_descrition_1' => 'Now after the linking process, you can synchronize the order you want as shown in the following two steps',
        'step_5_title' => 'Now to synchronize a specific order, go to the shipments page and click on the "Sync Order" button',
        'step_6_title' => 'Enter the order number you have in the Shopify platform, then select the sales channel you added',
        'shopify_store_connection' => 'Follow the steps below to link your Shopify store to Treek via the app for new order synchronization.',
    ],

    'shopify_webhook_connection' => [
        'shopify_webhook_app' => 'Shopify App via Webhook',
        'title' => 'Steps required to link your Shopify store to Treek via webhook',
        'shopify_application_instructions' => 'Please follow the steps shown to complete the linking process (there are images for all steps and a video at the bottom)',
        'step_1_title' => 'Open the Shopify sales channel addition page via webhook (exactly like this page). Then click on the "Account Information" button to show the fields required for the linking process. Then enter the name that suits you',
        'step_2_title' => 'Go to the main "General" page in your admin account on the Shopify platform. Then copy the last part of the link shown in yellow (the last part always follows this part admin.shopify.com/store/)',
        'step_3_title' => 'Now paste it in the domain field and add .myshopify.com (the part shown in blue). In the end, you will get the domain in this format xxxxxx.myshopify.com (only the yellow part varies from one user to another)',
        'step_4_title' => 'Return to your Shopify account and click on the "Settings" button',
        'step_5_title' => 'Click on "Apps and sales channels"',
        'step_6_title' => 'Click on "Develop apps"',
        'step_7_title' => 'Click on the "create an app" button and a small window will appear to enter the name that suits you, then click on the "create an app" button again',
        'step_8_title' => 'Click on the "API credentials" button, then scroll down a bit and you will find API key and API secret key.',
        'step_8_2_title' => 'Copy the API key in the "API Key" field as shown in blue. And copy the API secret key in the "API Secret Key" field as shown in yellow',
        'step_9_title' => 'Return to "Api Credentials" and click on the "Configure Admin API Scopes" button',
        'step_10_title' => 'Select "write_orders", "read_orders", "write_fulfillments", "read_fulfillments" then scroll to the bottom of the page and save by clicking the "Save" button',
        'step_11_title' => 'Return to "Api Credentials" and click on the "Install app" button',
        'step_12_title' => 'Now the "Admin API Access Token" field will appear. Click on "Reveal token once" then click on the copy button, then paste it in the "Access Token" field on the Treek platform',
        'step_13_title' => 'Set up and select the main warehouse, then click "Add"',
        'step_14_title' => 'Finally, return to the sales channel and click on connected sales channels, then click on the "Setup Webhook" button',
        'general_descrition_1' => 'Now after the linking process, orders can reach from Shopify to Treek',
        'general_descrition_2' => 'Orders that reach from Shopify to Treek will become the same status on both platforms. For example, if the order is changed to "Delivered" in Treek, the order will be changed to "Fulfilled" in Shopify',
    ],
    'coupon_code' => 'Coupon Code',
    'coupon_hint' => 'Enter the coupon code to get 100 SAR for free',
    'use_coupon_code' => 'Use Coupon (Optional)',
    'coupon_code_invalid' => 'Coupon code is invalid',

    // User Shipment Prices
    'user_shipment_prices' => 'User Shipment Prices',
    'user_shipment_price' => 'User Shipment Price',
    'create_user_shipment_price' => 'Create User Shipment Price',
    'edit_user_shipment_price' => 'Edit User Shipment Price',
    'user_shipment_price_created_successfully' => 'User shipment price created successfully',
    'user_shipment_price_updated_successfully' => 'User shipment price updated successfully',
    'user_shipment_price_deleted_successfully' => 'User shipment price deleted successfully',
    'base_price' => 'Base Price',
    'extra_weight_from' => 'Extra Weight From (kg)',
    'extra_weight_from_helper' => 'Weight from which additional charges apply',
    'additional_weight_cost' => 'Additional Weight Cost (per kg)',
    'cash_on_delivery_cost' => 'Cash on Delivery Cost',
    'distance_cost' => 'Distance Cost',
    'is_active' => 'Active',
    'shipment_courier_service' => 'Shipment Courier Service',
    'user' => 'User',
    'user_name' => 'User Name',
    'courier_service' => 'Courier Service',
    'service_type' => 'Service Type',
    'price_breakdown' => 'Price Breakdown',
    'total_price_for_weight' => 'Total Price for Weight',
    'weight' => 'Weight',
    'additional_services' => 'Additional Services',
    'extra_weight_cost' => 'Extra Weight Cost',
    'no_pricing_configured' => 'No pricing configured for this service',
    'unique_user_service_combination' => 'A price configuration already exists for this user and service combination',
    'filter_by_user' => 'Filter by User',
    'filter_by_courier_service' => 'Filter by Courier Service',
    'active_status' => 'Active Status',
    'created_at' => 'Created At',
    'updated_at' => 'Updated At',

];
