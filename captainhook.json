{"commit-msg": {"enabled": true, "actions": []}, "pre-push": {"enabled": false, "actions": []}, "pre-commit": {"enabled": true, "actions": [{"action": "\\CaptainHook\\App\\Hook\\PHP\\Action\\Linting"}, {"action": "vendor/bin/pint"}, {"action": "vendor/bin/phpstan analyse"}]}, "prepare-commit-msg": {"enabled": false, "actions": []}, "post-commit": {"enabled": false, "actions": []}, "post-merge": {"enabled": false, "actions": []}, "post-checkout": {"enabled": false, "actions": []}, "post-rewrite": {"enabled": false, "actions": []}, "post-change": {"enabled": false, "actions": []}}