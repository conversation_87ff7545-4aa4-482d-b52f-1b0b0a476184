<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('other_barcodes', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('product_id')->nullable()->constrained('products')->onDelete('cascade');
            $table->string('barcode')->default('')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('other_barcodes');
    }
};
