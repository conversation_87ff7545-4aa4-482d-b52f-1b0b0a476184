<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('order_items', function (Blueprint $table) {
            $table->id();

            // Foreign Key to Orders
            $table->unsignedBigInteger('order_id');
            $table->foreign('order_id')->references('id')->on('orders')->onDelete('cascade');

            // Item Details
            $table->string('name');
            $table->string('sku');
            $table->integer('quantity'); // Quantity of items
            $table->integer('weight'); // Weight in grams
            $table->string('weight_unit'); // Weight unit
            $table->integer('price'); // Unit price in smallest currency unit (e.g., cents)
            $table->integer('total_price'); // Total price for the item
            //            $table->integer('discount'); // Discount for the item
            $table->integer('tax'); // Tax for the item
            $table->string('currency')->default('SAR'); // Currency code

            // Product Details
            //            $table->unsignedBigInteger('product_id'); // Link to a product (if exists)
            //            $table->string('product_url'); // URL for the product
            //            $table->string('product_thumbnail'); // Thumbnail for the product
            //            $table->text('notes'); // Additional notes for the item

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('order_product');
    }
};
