<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('orders', function (Blueprint $table) {
            // Helper function to safely add index
            $safeAddIndex = function ($table, $columns, $indexName) {
                try {
                    $table->index($columns, $indexName);
                } catch (\Exception $e) {
                    // Index already exists or other error, continue
                }
            };

            // Primary search and sort columns
            $safeAddIndex($table, 'order_number', 'idx_orders_order_number');
            $safeAddIndex($table, 'date', 'idx_orders_date');
            $safeAddIndex($table, 'shipment_reference', 'idx_orders_shipment_reference');
            $safeAddIndex($table, 'shipper_name', 'idx_orders_shipper_name');
            $safeAddIndex($table, 'payment_method', 'idx_orders_payment_method');
            $safeAddIndex($table, 'status', 'idx_orders_status');

            // Receiver information indexes
            $safeAddIndex($table, 'receiver_first_name', 'idx_orders_receiver_first_name');
            $safeAddIndex($table, 'receiver_last_name', 'idx_orders_receiver_last_name');
            $safeAddIndex($table, ['receiver_first_name', 'receiver_last_name'], 'idx_orders_receiver_full_name');

            // Numeric/filterable columns
            $safeAddIndex($table, 'order_grand_total', 'idx_orders_order_grand_total');
            $safeAddIndex($table, 'return_type', 'idx_orders_return_type');

            // Foreign key indexes (if not already present)
            $safeAddIndex($table, 'receiver_city_id', 'idx_orders_receiver_city_id');
            $safeAddIndex($table, 'receiver_country_id', 'idx_orders_receiver_country_id');
            $safeAddIndex($table, 'warehouse_id', 'idx_orders_warehouse_id');
            $safeAddIndex($table, 'merchant_id', 'idx_orders_merchant_id');

            // Composite indexes for common query patterns
            $safeAddIndex($table, ['status', 'date'], 'idx_orders_status_date');
            $safeAddIndex($table, ['merchant_id', 'status'], 'idx_orders_merchant_status');
            $safeAddIndex($table, ['merchant_id', 'date'], 'idx_orders_merchant_date');
            $safeAddIndex($table, ['date', 'status'], 'idx_orders_date_status');

            // Shipment-related composite indexes
            $safeAddIndex($table, ['shipment_company', 'status'], 'idx_orders_shipment_company_status');
            $safeAddIndex($table, ['receiver_country_id', 'receiver_city_id'], 'idx_orders_location');

            // Application-related indexes (for withApplication/withoutApplication scopes)
            $safeAddIndex($table, 'shipment_credentials_type', 'idx_orders_shipment_credentials_type');
        });

        // Handle TEXT column index separately for MySQL
        if (DB::getDriverName() === 'mysql') {
            try {
                DB::statement('ALTER TABLE orders ADD INDEX idx_orders_receiver_address_line (receiver_address_line(255))');
            } catch (\Exception $e) {
                // Index already exists or other error, continue
            }
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('orders', function (Blueprint $table) {
            // Drop custom indexes with error handling
            $indexes = [
                'idx_orders_order_number',
                'idx_orders_date',
                'idx_orders_shipment_reference',
                'idx_orders_shipper_name',
                'idx_orders_payment_method',
                'idx_orders_status',
                'idx_orders_receiver_first_name',
                'idx_orders_receiver_last_name',
                'idx_orders_receiver_full_name',
                'idx_orders_order_grand_total',
                'idx_orders_return_type',
                'idx_orders_receiver_city_id',
                'idx_orders_receiver_country_id',
                'idx_orders_warehouse_id',
                'idx_orders_merchant_id',
                'idx_orders_status_date',
                'idx_orders_merchant_status',
                'idx_orders_merchant_date',
                'idx_orders_date_status',
                'idx_orders_shipment_company_status',
                'idx_orders_location',
                'idx_orders_shipment_credentials_type',
            ];

            foreach ($indexes as $index) {
                try {
                    $table->dropIndex($index);
                } catch (\Exception $e) {
                    // Index doesn't exist, continue
                }
            }
        });

        // Handle TEXT column index separately for MySQL
        if (DB::getDriverName() === 'mysql') {
            try {
                DB::statement('ALTER TABLE orders DROP INDEX idx_orders_receiver_address_line');
            } catch (\Exception $e) {
                // Index doesn't exist, continue
            }
        }
    }
};
