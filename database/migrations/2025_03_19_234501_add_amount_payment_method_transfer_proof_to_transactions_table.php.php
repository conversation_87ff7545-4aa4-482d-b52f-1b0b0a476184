<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('transactions', function (Blueprint $table) {
            $table->integer('amount')->nullable()->after('user_id')->comment('Remove nullable when doing the datafix');
            $table->string('payment_method')->after('amount');
            $table->string('transfer_proof')->nullable()->after('payment_method');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('transactions', function (Blueprint $table) {
            $table->dropColumn('amount');
            $table->dropColumn('payment_method');
            $table->dropColumn('transfer_proof');
        });
    }
};
