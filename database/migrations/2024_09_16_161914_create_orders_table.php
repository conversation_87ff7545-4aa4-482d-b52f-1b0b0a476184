<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('orders', function (Blueprint $table) {
            $table->id();

            // Order Details
            $table->string('order_number');
            $table->string('status');
            $table->dateTime('date'); // Changed to datetime for precision
            $table->integer('order_grand_total')->default(0);
            $table->string('currency')->default('SAR');
            $table->string('description')->nullable();

            // Payment and Receipt
            $table->string('payment_method');

            // Receiver Details
            $table->string('receiver_first_name');
            $table->string('receiver_last_name');
            $table->string('receiver_phone');
            $table->string('receiver_email')->nullable(); // Email might not always be available
            $table->string('receiver_country');
            $table->string('receiver_country_code')->nullable();
            $table->string('receiver_city')->nullable();
            $table->text('receiver_address_line');
            $table->string('receiver_street_name')->nullable();
            $table->string('receiver_block')->nullable();
            $table->string('receiver_postal_code')->nullable();
            $table->decimal('receiver_latitude', 10, 8)->nullable();
            $table->decimal('receiver_longitude', 11, 8)->nullable();

            // Shipper Details
            $table->string('shipper_name');
            $table->string('shipper_email')->nullable();
            $table->string('shipper_phone')->nullable();
            $table->string('shipper_country');
            $table->string('shipper_country_code');
            $table->string('shipper_city');
            $table->string('shipper_address_line')->default('');
            $table->decimal('shipper_latitude', 10, 8)->nullable();
            $table->decimal('shipper_longitude', 11, 8)->nullable();

            // Shipment Details
            $table->string('shipment_company')->default('');
            $table->string('shipment_tracking_link')->nullable();
            $table->string('shipment_status')->default('');
            $table->string('shipment_reference')->nullable();
            $table->string('shipment_logo')->nullable();
            $table->integer('shipment_total_weight')->default(0);
            $table->json('shipment_credentials')->nullable();
            $table->string('shipment_credentials_type')->nullable();
            $table->text('shipment_label_url')->nullable();

            // Merchant Details
            $table->unsignedBigInteger('merchant_id')->nullable();
            $table->string('merchant_email')->nullable();
            $table->string('merchant_phone')->nullable();

            // Tax and Discounts
            $table->integer('tax')->default(0);
            //            $table->integer('discount')->default(0);
            $table->string('external_id')->nullable();
            // Relationships
            $table->foreign('merchant_id')->references('id')->on('merchants')->onDelete('cascade');
            $table->unsignedBigInteger('webhook_id')->nullable();
            $table->foreign('webhook_id')->references('id')->on('webhooks')->onDelete('cascade');

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('orders');
    }
};
