<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('user_shipment_courier_services', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('shipment_courier_service_id');
            $table->unsignedBigInteger('user_id');
            $table->string('username')->nullable();
            $table->string('password')->nullable();
            $table->string('account_number')->nullable();
            $table->string('account_entity')->nullable();
            $table->string('account_pin')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_shipment_courier_services');
    }
};
