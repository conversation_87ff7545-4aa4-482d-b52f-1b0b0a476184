<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('shipment_countries_prices', function (Blueprint $table) {
            $table->dropColumn('first_half_kg_price');
            $table->dropColumn('additional_half_kg_price');
            $table->dropColumn('price_after_10kg');
            $table->dropColumn('price_after_15kg');
            $table->integer('extra_weight_price')->after('initial_price');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        throw new Exception('Never rollback a migration');
    }
};
