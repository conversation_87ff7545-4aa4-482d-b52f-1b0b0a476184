<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('shipment_courier_services', function (Blueprint $table) {
            $table->integer('cost_base_price')->nullable();
            $table->integer('cost_extra_weight_from')->nullable();
            $table->integer('cost_additional_weight_cost')->nullable();
            $table->integer('cost_cash_on_delivery_cost')->nullable();
            $table->integer('cost_distance_cost')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        throw new Exception('Never rollback a migration');
    }
};
