<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('orders', function (Blueprint $table) {
            $table->unsignedBigInteger('receiver_country_id')->nullable()->after('receiver_email');
            $table->foreign('receiver_country_id')->references('id')->on('countries');

            $table->unsignedBigInteger('shipper_country_id')->nullable()->after('shipper_phone')->default(1);
            $table->foreign('shipper_country_id')->references('id')->on('countries');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('orders', function (Blueprint $table) {
            //
        });
    }
};
