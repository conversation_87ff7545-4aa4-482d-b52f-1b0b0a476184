<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('shipment_countries_prices', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('country_id')->constrained();
            $table->foreign('country_id')->references('id')->on('countries');
            $table->integer('initial_price');
            $table->integer('first_half_kg_price')->nullable();
            $table->integer('additional_half_kg_price')->nullable();
            $table->integer('price_after_10kg')->nullable();
            $table->integer('price_after_15kg')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('shipment_countries_prices');
    }
};
