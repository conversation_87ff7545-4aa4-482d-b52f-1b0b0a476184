<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('products', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('sku')->default('');
            $table->integer('price');
            $table->integer('tax_amount');
            $table->unsignedBigInteger('currency_id')->nullable()->constrained()->onDelete('set null');
            $table->foreign('currency_id')->references('id')->on('currencies');
            $table->unsignedBigInteger('tax_currency_id')->nullable()->constrained()->onDelete('set null');
            $table->foreign('tax_currency_id')->references('id')->on('currencies');

            $table->string('barcode')->default('');
            $table->string('category')->default('');
            $table->string('description')->default('');
            $table->integer('cubic_meter')->default(0);
            $table->integer('length')->default(0);
            $table->integer('width')->default(0);
            $table->integer('height')->default(0);
            $table->integer('total_weight')->default(0);
            $table->string('image')->default('')->nullable();
            $table->unsignedBigInteger('merchant_id')->nullable()->constrained()->onDelete('set null');
            $table->foreign('merchant_id')->references('id')->on('merchants');
            $table->string('external_id')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('products');
    }
};
