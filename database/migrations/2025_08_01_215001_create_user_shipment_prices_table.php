<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('user_shipment_prices', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('user_id');
            $table->unsignedBigInteger('shipment_courier_service_id');
            $table->integer('base_price');
            $table->integer('extra_weight_from');
            $table->integer('additional_weight_cost');
            $table->integer('cash_on_delivery_cost');
            $table->integer('distance_cost');
            $table->integer('volumetric_divisor');
            $table->boolean('is_active')->default(true);
            $table->timestamps();

            // Foreign key constraints
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
            $table->foreign('shipment_courier_service_id')->references('id')->on('shipment_courier_services')->onDelete('cascade');

            // Unique constraint to ensure one price per user per service
            $table->unique(['user_id', 'shipment_courier_service_id'], 'user_shipment_price_unique');

            // Indexes
            $table->index(['user_id', 'is_active']);
            $table->index(['shipment_courier_service_id', 'is_active']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_shipment_prices');
    }
};
