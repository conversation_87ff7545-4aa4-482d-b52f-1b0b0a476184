<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->unsignedBigInteger('company_city_id')->nullable()->constrained()->onDelete('set null');
            $table->foreign('company_city_id')->references('id')->on('cities');
            $table->string('company_name')->nullable();
            $table->string('company_postal_code')->nullable();
            $table->string('company_commercial_registration_number')->nullable();
            $table->string('company_tax_number')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropForeign('users_company_city_id_foreign');
            $table->dropColumn('company_city_id');
            $table->dropColumn('company_name');
            $table->dropColumn('company_postal_code');
            $table->dropColumn('company_commercial_registration_number');
            $table->dropColumn('company_tax_number');

        });
    }
};
