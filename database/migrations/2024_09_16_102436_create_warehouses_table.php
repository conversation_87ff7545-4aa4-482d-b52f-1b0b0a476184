<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('warehouses', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('code')->nullable();
            $table->string('address');
            $table->string('sender_name');
            $table->string('sender_email')->nullable();
            $table->string('sender_phone');
            $table->tinyInteger('status')->default(1);
            $table->unsignedBigInteger('city_id')->nullable()->constrained()->onDelete('set null');
            $table->foreign('city_id')->references('id')->on('cities');
            $table->string('district')->default('')->nullable();
            $table->string('street_name')->default('')->nullable();
            $table->string('building_no_name')->default('')->nullable();
            $table->string('zip_code')->default('')->nullable();
            $table->unsignedBigInteger('user_id')->nullable()->constrained()->onDelete('set null');
            $table->foreign('user_id')->references('id')->on('users');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('warehouses');
    }
};
