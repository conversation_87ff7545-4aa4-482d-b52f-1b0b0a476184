<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('shipment_courier_services', function (Blueprint $table) {
            $table->integer('volumetric_divisor');
            $table->integer('base_volumetric_divisor');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('shipment_courier_services', function (Blueprint $table) {
            $table->dropColumn(['volumetric_divisor', 'base_volumetric_divisor']);
        });
    }
};
