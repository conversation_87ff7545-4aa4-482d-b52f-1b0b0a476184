<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('shipment_courier_services', function (Blueprint $table) {
            $table->id();
            $table->string('identifier');
            $table->string('courier_name');
            $table->string('service_name');
            $table->integer('base_price');
            $table->integer('extra_weight_from');
            $table->integer('additional_weight_cost');
            $table->integer('cash_on_delivery_cost');
            $table->integer('distance_cost');
            $table->string('logo');
            $table->string('logo_big');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('shipment_courier_services');
    }
};
