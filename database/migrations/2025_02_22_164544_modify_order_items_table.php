<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('order_items', function (Blueprint $table) {
            $table->string('name')->nullable()->change();
            $table->string('sku')->nullable()->change();
            $table->integer('quantity')->default(1)->change();
            $table->integer('weight')->nullable()->change();
            $table->string('weight_unit', 191)->nullable()->change();
            $table->integer('price')->nullable()->change();
            $table->integer('total_price')->nullable()->change();
            $table->integer('tax')->nullable()->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('order_items', function (Blueprint $table) {
            $table->string('name')->nullable(false)->change();
            $table->string('sku')->nullable(false)->change();
            $table->integer('quantity')->default(null)->change();
            $table->integer('weight')->nullable(false)->change();
            $table->string('weight_unit')->nullable(false)->change();
            $table->integer('price')->nullable(false)->change();
            $table->integer('total_price')->nullable(false)->change();
            $table->integer('tax')->nullable(false)->change();
        });
    }
};
