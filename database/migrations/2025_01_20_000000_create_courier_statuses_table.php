<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('courier_statuses', function (Blueprint $table) {
            $table->id();
            $table->string('courier')->comment('Courier service identifier (e.g., aramex, jt, thabit)');
            $table->string('code')->comment('Status code from courier (e.g., SH250, DELIVERED)');
            $table->string('description')->nullable()->comment('Description of the status');
            $table->string('order_status')->comment('Corresponding OrderStatusEnum value');
            $table->timestamps();

            $table->index(['courier', 'code'], 'courier_code_index');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        throw new \Exception('Never rollback a migration');
    }
};
