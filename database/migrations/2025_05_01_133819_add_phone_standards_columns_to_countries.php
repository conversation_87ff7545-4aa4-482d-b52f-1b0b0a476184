<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('countries', function (Blueprint $table) {
            $table->string('phone_code', 10)->after('code_country');
            $table->unsignedSmallInteger('phone_number_length')->after('phone_code');
        });
        DB::table('countries')->where('code_country', 'SA')->update([
            'phone_code' => '+966',
            'phone_number_length' => 9,
        ]);

        DB::table('countries')->where('code_country', 'JO')->update([
            'phone_code' => '+962',
            'phone_number_length' => 9,
        ]);

        DB::table('countries')->where('code_country', 'AE')->update([
            'phone_code' => '+971',
            'phone_number_length' => 9,
        ]);

        DB::table('countries')->where('code_country', 'OM')->update([
            'phone_code' => '+968',
            'phone_number_length' => 8,
        ]);

        DB::table('countries')->where('code_country', 'KW')->update([
            'phone_code' => '+965',
            'phone_number_length' => 8,
        ]);

        DB::table('countries')->where('code_country', 'QA')->update([
            'phone_code' => '+974',
            'phone_number_length' => 8,
        ]);

        DB::table('countries')->where('code_country', 'BH')->update([
            'phone_code' => '+973',
            'phone_number_length' => 8,
        ]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('countries', function (Blueprint $table) {
            Schema::dropIfExists('phone_code');
            Schema::dropIfExists('phone_number_length');
        });
    }
};
