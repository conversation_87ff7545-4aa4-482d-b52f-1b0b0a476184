<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('cities', function (Blueprint $table) {
            $table->id();
            // TODO: REMOVE THIS DEFAULT LATER WHEN WE HAVE CITIES OF OTHER COUNTRIES
            $table->unsignedBigInteger('country_id')->constrained()->default(1);
            $table->foreign('country_id')->references('id')->on('countries');
            $table->string('name')->default('')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('cities');
    }
};
