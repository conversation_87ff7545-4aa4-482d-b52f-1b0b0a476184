<?php

use App\Models\User;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('merchants', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->bigInteger('merchant_id')->nullable();
            $table->string('domain')->nullable()->comment('Check if we need this');
            $table->text('access_token')->nullable();
            $table->text('refresh_token')->nullable();
            $table->text('authorization')->nullable()->comment('Needed for Zid integration');
            $table->json('metadata')->nullable();
            $table->string('type');
            $table->integer('expires_in')->nullable();
            $table->dateTime('ask_for_meeting')->nullable();
            $table->boolean('active')->default(0);
            $table->foreignIdFor(User::class)->constrained();
            $table->text('webhook_url')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('merchants');
    }
};
