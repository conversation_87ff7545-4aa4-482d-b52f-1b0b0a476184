<?php

namespace Database\Factories;

use App\Models\ShipmentCourierService;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\ShipmentCourierService>
 */
class ShipmentCourierServiceFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = ShipmentCourierService::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $courierServices = [
            'aramex' => [
                'courier_name' => 'Aramex',
                'service_name' => 'Aramex Express',
                'logo' => 'images/aramex-logo.png',
                'logo_big' => 'images/aramex-logo-big.png',
            ],
            'jt' => [
                'courier_name' => 'J&T Express',
                'service_name' => 'J&T Standard',
                'logo' => 'images/jt-logo.png',
                'logo_big' => 'images/jt-logo-big.png',
            ],
            'thabit' => [
                'courier_name' => 'Thabit',
                'service_name' => 'Thabit Delivery',
                'logo' => 'images/thabit-logo.png',
                'logo_big' => 'images/thabit-logo-big.png',
            ],
            'spl' => [
                'courier_name' => 'SPL',
                'service_name' => 'SPL Express',
                'logo' => 'images/spl-logo.png',
                'logo_big' => 'images/spl-logo-big.png',
            ],
            'barq' => [
                'courier_name' => 'Barq',
                'service_name' => 'Barq Fleet',
                'logo' => 'images/barq-logo.png',
                'logo_big' => 'images/barq-logo-big.png',
            ],
        ];

        $identifier = $this->faker->randomElement(array_keys($courierServices));
        $service = $courierServices[$identifier];

        return [
            'identifier' => $identifier,
            'courier_name' => $service['courier_name'],
            'service_name' => $service['service_name'],
            'base_price' => $this->faker->numberBetween(1500, 3000), // 15-30 SAR in cents
            'extra_weight_from' => $this->faker->numberBetween(500, 1000), // 0.5-1 KG in grams
            'additional_weight_cost' => $this->faker->numberBetween(200, 500), // 2-5 SAR per kg in cents
            'cash_on_delivery_cost' => $this->faker->numberBetween(300, 800), // 3-8 SAR in cents
            'distance_cost' => $this->faker->numberBetween(100, 300), // 1-3 SAR in cents
            'logo' => $service['logo'],
            'logo_big' => $service['logo_big'],

            // Cost-related columns (added later)
            'cost_base_price' => $this->faker->numberBetween(1000, 2500), // Cost base price in cents
            'cost_extra_weight_from' => $this->faker->numberBetween(400, 900), // Cost extra weight in grams
            'cost_additional_weight_cost' => $this->faker->numberBetween(150, 400), // Cost per kg in cents
            'cost_cash_on_delivery_cost' => $this->faker->numberBetween(200, 600), // Cost COD in cents
            'cost_distance_cost' => $this->faker->numberBetween(80, 250), // Cost distance in cents
            'cost_fuel' => $this->faker->numberBetween(50, 150), // Fuel surcharge in cents
            'cost_cod_type' => $this->faker->randomElement(['value', 'fixed']), // COD calculation type
            'volumetric_divisor' => $this->faker->numberBetween(4000, 6000), // Volumetric divisor
            'base_volumetric_divisor' => $this->faker->numberBetween(4000, 6000), // Base volumetric divisor
        ];
    }
}
