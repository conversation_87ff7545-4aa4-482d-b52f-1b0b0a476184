<?php

namespace Database\Factories;

use App\Models\Order;
use App\Models\OrderItem;
use App\Models\Product;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\OrderItem>
 */
class OrderItemFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = OrderItem::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $arabicProductNames = [
            'شامبو الأطفال', 'كريم مرطب', 'عسل طبيعي', 'زيت زيتون', 'قهوة عربية',
            'تمر مجهول', 'حليب اطفال', 'مكمل غذائي', 'صابون طبيعي', 'عطر رجالي',
        ];

        $quantity = $this->faker->numberBetween(1, 5);
        $unitPrice = $this->faker->numberBetween(1000, 10000); // Price in cents
        $totalPrice = $quantity * $unitPrice;
        $tax = $totalPrice * 0.15; // 15% tax

        return [
            'order_id' => Order::factory(),
            'name' => $this->faker->randomElement($arabicProductNames),
            'sku' => $this->faker->unique()->numerify('SKU-#####'),
            'quantity' => $quantity,
            'weight' => $this->faker->numberBetween(100, 2000), // Weight in grams
            'weight_unit' => 'g', // Weight unit
            'price' => $unitPrice, // Unit price in cents
            'total_price' => $totalPrice, // Total price in cents
            'tax' => (int) $tax, // Tax in cents
            'currency' => 'SAR',
        ];
    }

    /**
     * Configure the model factory to use a specific product.
     */
    public function forProduct(Product $product): Factory
    {
        return $this->state(function (array $attributes) use ($product) {
            return [
                'product_id' => $product->id,
                'name' => $product->name,
                'sku' => $product->sku,
                'price' => $product->price,
                'weight' => $product->weight,
            ];
        });
    }
}
