<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\SalesChannel>
 */
class SalesChannelFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $channels = [
            'salla' => 'Salla',
            'shopify' => 'Shopify',
            'zid' => 'Zid',
            'woocommerce' => 'WooCommerce',
            'magento' => 'Magento',
        ];

        $identifier = $this->faker->randomElement(array_keys($channels));
        $name = $channels[$identifier];

        return [
            'name' => $name,
            'identifier' => $identifier,
            'description' => $this->getDescriptionForChannel($identifier),
            'logo' => $this->getLogoForChannel($identifier),
            'active' => $this->faker->boolean(80),
            'config' => $this->getConfigForChannel($identifier),
        ];
    }

    private function getDescriptionForChannel(string $identifier): string
    {
        return match ($identifier) {
            'salla' => 'منصة سلة للتجارة الإلكترونية',
            'shopify' => 'منصة شوبيفاي للتجارة الإلكترونية',
            'zid' => 'منصة زد للتجارة الإلكترونية',
            'woocommerce' => 'إضافة ووكومرس لووردبريس',
            'magento' => 'منصة ماجنتو للتجارة الإلكترونية',
            default => 'منصة للتجارة الإلكترونية'
        };
    }

    private function getLogoForChannel(string $identifier): string
    {
        return match ($identifier) {
            'salla' => 'https://storage.googleapis.com/tryoto-public/sales-channels-logo/salla.png',
            'shopify' => 'https://storage.googleapis.com/tryoto-public/sales-channels-logo/shopify.png',
            'zid' => 'https://storage.googleapis.com/tryoto-public/sales-channels-logo/zid.png',
            'woocommerce' => 'images/woocommerce-logo.png',
            'magento' => 'images/magento-logo.png',
            default => 'images/default-logo.png'
        };
    }

    private function getConfigForChannel(string $identifier): array
    {
        return match ($identifier) {
            'salla' => [
                'api_url' => 'https://api.salla.dev',
                'webhook_url' => 'webhooks/salla',
            ],
            'shopify' => [
                'api_url' => 'https://{{shop}}.myshopify.com/admin/api',
                'webhook_url' => 'webhooks/shopify',
            ],
            'zid' => [
                'api_url' => 'https://api.zid.sa',
                'webhook_url' => 'webhooks/zid',
            ],
            default => [
                'api_url' => 'https://api.example.com',
                'webhook_url' => 'webhooks/default',
            ]
        };
    }

    public function active(): static
    {
        return $this->state(fn (array $attributes) => [
            'active' => true,
        ]);
    }

    public function inactive(): static
    {
        return $this->state(fn (array $attributes) => [
            'active' => false,
        ]);
    }
}
