<?php

namespace Database\Factories;

use App\Models\ShipmentCourierService;
use App\Models\User;
use App\Models\UserShipmentCourierService;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\UserShipmentCourierService>
 */
class UserShipmentCourierServiceFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = UserShipmentCourierService::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $courierServiceNames = [
            'aramex' => ['aramex_user', 'aramex_merchant', 'aramex_account'],
            'jt' => ['jt_express', 'jtexpress_user', 'jt_merchant'],
            'thabit' => ['thabit_user', 'thabit_account', 'thabit_merchant'],
            'spl' => ['spl_user', 'spl_account', 'spl_express'],
            'barq' => ['barq_user', 'barq_fleet', 'barq_delivery'],
        ];

        $courierType = $this->faker->randomElement(array_keys($courierServiceNames));
        $usernames = $courierServiceNames[$courierType];

        return [
            'shipment_courier_service_id' => ShipmentCourierService::factory(),
            'user_id' => User::factory(),
            'username' => $this->faker->randomElement($usernames).'_'.$this->faker->numerify('###'),
            'password' => $this->faker->password(8, 16),
            'account_number' => $this->faker->numerify('ACC-########'),
            'account_entity' => $this->faker->randomElement(['MAIN', 'BRANCH', 'SUBSIDIARY', 'DEPOT']),
            'account_pin' => $this->faker->numerify('####'),
        ];
    }
}
