<?php

namespace Database\Factories;

use App\Models\City;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\CityTranslation>
 */
class CityTranslationFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $sources = ['name_aramex', 'name_salla', 'name_salla_second', 'name_thabit', 'name_zid'];
        $source = $this->faker->randomElement($sources);

        return [
            'city_id' => City::factory(),
            'source' => $source,
            'value' => $this->getValueForSource($source),
        ];
    }

    private function getValueForSource(string $source): string
    {
        $cities = [
            'الرياض', 'جدة', 'مكة المكرمة', 'المدينة المنورة', 'الدمام',
            'الخبر', 'تبوك', 'بريدة', 'خميس مشيط', 'حفر الباطن',
        ];

        return match ($source) {
            'name_aramex' => $this->faker->randomElement($cities),
            'name_salla' => $this->faker->randomElement($cities),
            'name_salla_second' => $this->faker->randomElement($cities),
            'name_thabit' => $this->faker->randomElement($cities),
            'name_zid' => $this->faker->randomElement($cities),
            default => $this->faker->randomElement($cities)
        };
    }

    public function aramex(): static
    {
        return $this->state(fn (array $attributes) => [
            'source' => 'name_aramex',
        ]);
    }

    public function salla(): static
    {
        return $this->state(fn (array $attributes) => [
            'source' => 'name_salla',
        ]);
    }
}
