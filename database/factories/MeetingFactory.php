<?php

namespace Database\Factories;

use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Meeting>
 */
class MeetingFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'user_id' => User::factory(),
            'meeting_date' => $this->faker->dateTimeBetween('now', '+1 month'),
            'meeting_time' => $this->faker->time('H:i'),
            'status' => $this->faker->randomElement(['scheduled', 'completed', 'cancelled', 'rescheduled']),
            'notes' => $this->faker->paragraph(),
            'admin_note' => $this->faker->boolean(60) ? $this->faker->sentence() : null,
        ];
    }

    public function scheduled(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'scheduled',
            'meeting_date' => $this->faker->dateTimeBetween('now', '+2 weeks'),
        ]);
    }

    public function completed(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'completed',
            'meeting_date' => $this->faker->dateTimeBetween('-1 month', 'now'),
            'admin_note' => $this->faker->sentence(),
        ]);
    }
}
