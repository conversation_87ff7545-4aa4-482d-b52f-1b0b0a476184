<?php

namespace Database\Factories;

use App\Models\Currency;
use App\Models\Merchant;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Product>
 */
class ProductFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $arabicProductNames = [
            'شامبو الأطفال', 'كريم مرطب', 'عسل طبيعي', 'زيت زيتون', 'قهوة عربية',
            'تمر مجهول', 'حليب اطفال', 'مكمل غذائي', 'صابون طبيعي', 'عطر رجالي',
            'بخور فاخر', 'شاي أخضر', 'حنة طبيعية', 'كريم واقي شمس', 'فيتامين د',
        ];

        $categories = [
            'العناية بالجسم', 'الغذاء والمشروبات', 'العطور والبخور',
            'المكملات الغذائية', 'منتجات الأطفال', 'العناية بالشعر',
        ];

        return [
            'name' => $this->faker->randomElement($arabicProductNames),
            'sku' => $this->faker->unique()->numerify('SKU-#####'),
            'price' => $this->faker->numberBetween(1000, 50000), // Price in cents (10-500 SAR)
            'tax_amount' => $this->faker->numberBetween(50, 2500), // Tax in cents (0.50-25 SAR)
            'currency_id' => Currency::factory(),
            'tax_currency_id' => Currency::factory(),
            'barcode' => $this->faker->ean13(),
            'category' => $this->faker->randomElement($categories),
            'description' => $this->faker->realText(150),
            'cubic_meter' => $this->faker->numberBetween(1, 1000), // in cubic centimeters
            'length' => $this->faker->numberBetween(10, 500), // in millimeters
            'width' => $this->faker->numberBetween(10, 500), // in millimeters
            'height' => $this->faker->numberBetween(10, 300), // in millimeters
            'total_weight' => $this->faker->numberBetween(50, 5000), // in grams
            'image' => null, // Leave null for cleaner data
            'merchant_id' => Merchant::factory(),
            'external_id' => $this->faker->uuid(),
        ];
    }
}
