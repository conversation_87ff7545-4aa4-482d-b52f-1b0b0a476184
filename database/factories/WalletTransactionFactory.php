<?php

namespace Database\Factories;

use App\Models\Order;
use App\Models\Transaction;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\WalletTransaction>
 */
class WalletTransactionFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $type = $this->faker->randomElement(['credit', 'debit', 'cancel']);

        return [
            'user_id' => User::factory(),
            'type' => $type,
            'amount' => $this->faker->randomFloat(2, 10, 500) * 100, // Store in cents
            'description' => $this->getDescriptionForType($type),
            'order_id' => Order::factory(),
            'transaction_id' => $this->faker->boolean(50) ? Transaction::factory() : null,
        ];
    }

    private function getDescriptionForType(string $type): string
    {
        return match ($type) {
            'credit' => $this->faker->randomElement([
                'رصيد محفظة',
                'استرداد رسوم شحن',
                'رفع رصيد المحفظة',
            ]),
            'debit' => $this->faker->randomElement([
                'رسوم شحنة أرامكس',
                'رسوم شحنة ترانسكورب',
                'رسوم شحنة جي تي',
            ]),
            'cancel' => $this->faker->randomElement([
                'رسوم شحنة إلغاء أرامكس',
                'رسوم شحنة إلغاء ترانسكورب',
                'رسوم شحنة إلغاء جي تي',
            ]),
            default => 'معاملة محفظة'
        };
    }

    public function credit(): static
    {
        return $this->state(fn (array $attributes) => [
            'type' => 'credit',
            'description' => 'رصيد محفظة',
        ]);
    }

    public function debit(): static
    {
        return $this->state(fn (array $attributes) => [
            'type' => 'debit',
            'description' => 'رسوم شحنة',
        ]);
    }

    public function cancel(): static
    {
        return $this->state(fn (array $attributes) => [
            'type' => 'cancel',
            'description' => 'رسوم شحنة إلغاء',
        ]);
    }
}
