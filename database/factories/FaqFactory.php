<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Faq>
 */
class FaqFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $faqs = [
            [
                'question' => 'كيف يمكنني إنشاء حساب جديد؟',
                'answer' => 'يمكنك إنشاء حساب جديد عن طريق الضغط على زر "التسجيل" في أعلى الصفحة وملء البيانات المطلوبة.',
            ],
            [
                'question' => 'ما هي طرق الدفع المتاحة؟',
                'answer' => 'نقبل الدفع عن طريق الفيزا، الماستر كارد، STC Pay، Apple Pay، والتحويل البنكي.',
            ],
            [
                'question' => 'كم تستغرق عملية الشحن؟',
                'answer' => 'عادة ما تستغرق عملية الشحن من 2-5 أيام عمل حسب المنطقة ونوع الخدمة المختارة.',
            ],
            [
                'question' => 'هل يمكنني تتبع شحنتي؟',
                'answer' => 'نعم، يمكنك تتبع شحنتك من خلال رقم الشحنة المرسل إليك عبر البريد الإلكتروني أو الرسائل النصية.',
            ],
            [
                'question' => 'ما هي سياسة الاسترداد؟',
                'answer' => 'يمكنك طلب الاسترداد خلال 14 يوم من تاريخ الشحن، مع مراعاة الشروط والأحكام.',
            ],
        ];

        $selectedFaq = $this->faker->randomElement($faqs);

        return [
            'question' => $selectedFaq['question'],
            'answer' => $selectedFaq['answer'],
            'section' => $this->faker->randomElement(['services', 'shipment', 'link']),
        ];
    }

    public function services(): static
    {
        return $this->state(fn (array $attributes) => [
            'section' => 'services',
        ]);
    }

    public function shipment(): static
    {
        return $this->state(fn (array $attributes) => [
            'section' => 'shipment',
        ]);
    }

    public function link(): static
    {
        return $this->state(fn (array $attributes) => [
            'section' => 'link',
        ]);
    }
}
