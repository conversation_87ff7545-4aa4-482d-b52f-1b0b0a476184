<?php

namespace Database\Factories;

use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Merchant>
 */
class MerchantFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'name' => $this->faker->company(),
            'user_id' => User::factory(),
            'domain' => $this->faker->domainName(),
            'merchant_id' => 1,
            'access_token' => $this->faker->uuid(),
            'refresh_token' => $this->faker->uuid(),
            'authorization' => $this->faker->sentence(),
            'expires_in' => now()->addHours(2)->timestamp, // Token expiration
            'active' => $this->faker->boolean(),
            'type' => $this->faker->randomElement(['salla', 'shopify', 'woocommerce', 'custom']),
            'webhook_url' => $this->faker->url(),
        ];
    }
}
