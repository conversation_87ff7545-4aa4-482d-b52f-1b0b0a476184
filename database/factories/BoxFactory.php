<?php

namespace Database\Factories;

use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Box>
 */
class BoxFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'type' => $this->faker->randomElement(['Small Box', 'Medium Box', 'Large Box', 'Extra Large Box']),
            'length' => $this->faker->randomFloat(2, 10, 100),
            'width' => $this->faker->randomFloat(2, 5, 50),
            'height' => $this->faker->randomFloat(2, 5, 50),
            'weight' => $this->faker->randomFloat(2, 1, 50),
            'user_id' => User::factory(),
        ];
    }
}
