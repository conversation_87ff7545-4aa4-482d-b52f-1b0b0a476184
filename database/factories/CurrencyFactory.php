<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Currency>
 */
class CurrencyFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'code' => $this->faker->randomElement(['USD', 'EUR', 'SAR', 'AED', 'GBP', 'KWD', 'OMR', 'BHD', 'EGP', 'QAR', 'TRY']),
            'name' => $this->faker->randomElement([
                'US Dollar',
                'Euro',
                'Saudi Riyal',
                'UAE Dirham',
                'British Pound Sterling',
                'Kuwaiti Dinar',
                'Omani Rial',
                'Bahraini Dinar',
                'Egyptian Pound',
                'Qatari Riyal',
                'Turkish Lira',
            ]),
        ];
    }
}
