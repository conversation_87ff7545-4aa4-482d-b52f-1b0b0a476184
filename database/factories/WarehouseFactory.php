<?php

namespace Database\Factories;

use App\Models\City;
use App\Models\User;
use App\Models\Warehouse;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Warehouse>
 */
class WarehouseFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Warehouse::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $arabicFirstNames = ['محمد', 'أحمد', 'علي', 'عبدالله', 'عبدالرحمن', 'فهد', 'خالد', 'سعد', 'عمر'];
        $arabicLastNames = ['العتيبي', 'المطيري', 'الدوسري', 'القحطاني', 'الغامدي', 'الزهراني', 'السلمي', 'الحربي', 'العنزي', 'الشهري'];
        $warehouseTypes = ['مستودع', 'مخزن', 'مرفق تخزين', 'مجمع لوجستي'];
        $districts = ['حي الشفا', 'حي النهضة', 'حي العليا', 'حي الملز', 'حي الروضة', 'حي البديعة'];
        $streets = ['شارع الملك فهد', 'طريق الملك عبدالعزيز', 'شارع التحلية', 'طريق الأمير محمد بن سلمان'];

        return [
            'name' => $this->faker->randomElement($warehouseTypes).' '.$this->faker->randomElement(['الشرق', 'الغرب', 'الشمال', 'الجنوب', 'المركزي']),
            'code' => $this->faker->unique()->regexify('[A-Z]{3}[0-9]{3}'),
            'address' => $this->faker->randomElement($streets).'، '.$this->faker->randomElement($districts),
            'sender_name' => $this->faker->randomElement($arabicFirstNames).' '.$this->faker->randomElement($arabicLastNames),
            'sender_email' => $this->faker->email(),
            'sender_phone' => '966'.$this->faker->numerify('5########'),
            'status' => $this->faker->randomElement([1, 1, 1, 0]), // Most warehouses are active
            'city_id' => City::factory(),
            'district' => $this->faker->randomElement($districts),
            'street_name' => $this->faker->randomElement($streets),
            'building_no_name' => $this->faker->numerify('مبنى رقم ###'),
            'zip_code' => $this->faker->numerify('#####'),
            'user_id' => User::factory(),
        ];
    }

    /**
     * Indicate that the warehouse is the default one.
     */
    public function default(): Factory
    {
        return $this->state(function (array $attributes) {
            return [
                'status' => 1,
            ];
        });
    }
}
