<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Setting>
 */
class SettingFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $keys = [
            'site_name',
            'site_description',
            'currency',
            'tax_rate',
            'shipping_rate',
            'notification_email',
            'maintenance_mode',
            'api_rate_limit',
        ];

        $key = $this->faker->randomElement($keys);

        return [
            'key' => $key,
            'value' => $this->getValueForKey($key),
            'type' => $this->getTypeForKey($key),
            'description' => $this->getDescriptionForKey($key),
        ];
    }

    private function getValueForKey(string $key): string
    {
        return match ($key) {
            'site_name' => 'Treek Shipping',
            'site_description' => 'أفضل خدمة شحن في المملكة العربية السعودية',
            'currency' => 'SAR',
            'tax_rate' => '15',
            'shipping_rate' => '25',
            'notification_email' => '<EMAIL>',
            'maintenance_mode' => 'false',
            'api_rate_limit' => '1000',
            default => $this->faker->word()
        };
    }

    private function getTypeForKey(string $key): string
    {
        return match ($key) {
            'site_name', 'site_description', 'currency', 'notification_email' => 'string',
            'tax_rate', 'shipping_rate', 'api_rate_limit' => 'integer',
            'maintenance_mode' => 'boolean',
            default => 'string'
        };
    }

    private function getDescriptionForKey(string $key): string
    {
        return match ($key) {
            'site_name' => 'اسم الموقع',
            'site_description' => 'وصف الموقع',
            'currency' => 'العملة الأساسية',
            'tax_rate' => 'معدل الضريبة',
            'shipping_rate' => 'أسعار الشحن',
            'notification_email' => 'بريد الإشعارات',
            'maintenance_mode' => 'وضع الصيانة',
            'api_rate_limit' => 'حد معدل API',
            default => 'إعداد النظام'
        };
    }
}
