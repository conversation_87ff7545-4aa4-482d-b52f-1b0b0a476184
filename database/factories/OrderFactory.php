<?php

namespace Database\Factories;

use App\Models\City;
use App\Models\Merchant;
use App\Models\Warehouse;
use App\Models\Webhook;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Order>
 */
class OrderFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $saudiCities = ['الرياض', 'جدة', 'مكة المكرمة', 'المدينة المنورة', 'الدمام', 'الخبر', 'تبوك', 'بريدة'];
        $receiverCity = $this->faker->randomElement($saudiCities);
        $shipmentCompanies = ['aramex', 'transcorp', 'jt', 'barq', 'spl'];

        return [
            'order_number' => $this->faker->unique()->numerify('ORD-##########'),
            'date' => $this->faker->dateTimeThisYear(),
            'payment_method' => $this->faker->randomElement(['cod', 'credit_card', 'bank_transfer']),
            'status' => $this->faker->randomElement([
                'pending', 'processing', 'awaiting_pickup', 'picked_up',
                'in_transit', 'delivered', 'cancelled', 'returned',
            ]),
            'external_id' => $this->faker->uuid(),
            'order_grand_total' => $this->faker->randomFloat(2, 50, 2000) * 100, // Store in cents
            'warehouse_id' => Warehouse::factory(),
            'description' => $this->faker->randomElement([
                'طلب متنوع - منتجات إلكترونية',
                'طلب ملابس وأكسسوارات',
                'طلب منتجات منزلية',
                'طلب كتب ومواد تعليمية',
                'طلب منتجات العناية الشخصية',
            ]),
            'shipment_total_weight' => $this->faker->randomFloat(2, 0.5, 50) * 1000, // Store in grams
            'shipper_name' => $this->faker->randomElement(['تريك للشحن', 'شركة التاجر', 'متجر الإلكتروني']),
            'shipper_city' => $this->faker->randomElement($saudiCities),
            'receiver_first_name' => $this->faker->firstName(),
            'receiver_last_name' => $this->faker->lastName(),
            'receiver_country_code' => '+966',
            'receiver_phone' => '966'.$this->faker->numerify('5########'),
            'receiver_email' => $this->faker->safeEmail(),
            'receiver_country' => 'المملكة العربية السعودية',
            'receiver_city' => $receiverCity,
            'receiver_address_line' => $this->faker->randomElement([
                'شارع الملك فهد، حي العليا',
                'طريق الملك عبدالعزيز، حي الشفا',
                'شارع التحلية، حي النهضة',
                'طريق الأمير محمد بن سلمان',
            ]),
            'receiver_postal_code' => $this->faker->numerify('#####'),
            'receiver_street_name' => $this->faker->streetName(),
            'merchant_id' => Merchant::factory(),
            'tax' => $this->faker->randomFloat(2, 0, 200) * 100, // Store in cents
            'webhook_id' => $this->faker->boolean(30) ? Webhook::factory() : null,
            'shipment_credentials' => null,
            'receiver_city_id' => City::factory(),
            'receiver_country_id' => 1, // Assuming Saudi Arabia has ID 1
            'shipper_country_id' => 1,
            'shipment_cost' => $this->faker->randomFloat(2, 15, 150) * 100, // Store in cents
            'shipment_cost_without_cod' => function (array $attributes) {
                return $attributes['payment_method'] === 'cod'
                    ? $attributes['shipment_cost'] - ($attributes['shipment_cost'] * 0.025) // Subtract COD fee
                    : $attributes['shipment_cost'];
            },
            'shipment_company' => $this->faker->randomElement($shipmentCompanies),
            'source' => $this->faker->randomElement(['salla', 'shopify', 'zid', 'woocommerce', 'manual']),
            'return_type' => $this->faker->randomElement(['none', 'full', 'partial']),
            'volumetric_divisor' => $this->faker->randomElement([4000, 5000, 6000]),
            'applied_rate_cost' => $this->faker->randomFloat(2, 2, 8) * 100, // Store in cents
        ];
    }
}
