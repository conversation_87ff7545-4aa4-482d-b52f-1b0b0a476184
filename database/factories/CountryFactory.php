<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Country>
 */
class CountryFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $arabicCountries = [
            'الصومال', 'إثيوبيا', 'السودان', 'العراق', 'سوريا', 'لبنان', 'فلسطين',
            'المغرب', 'الجزائر', 'تونس', 'ليبيا', 'اليمن', 'الإمارات', 'قطر',
        ];

        return [
            'name' => $this->faker->country(),
            'name_ar' => $this->faker->randomElement($arabicCountries),
            'code_country' => $this->faker->countryCode(),
            'phone_code' => '+'.$this->faker->numberBetween(1, 999),
            'phone_number_length' => $this->faker->numberBetween(8, 15),
        ];
    }
}
