<?php

namespace Database\Factories;

use App\Models\Country;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\ShipmentCountriesPrice>
 */
class ShipmentCountriesPriceFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'country_id' => Country::factory(),
            'initial_price' => $this->faker->numberBetween(2000, 10000), // Price in cents
            'extra_weight_price' => $this->faker->numberBetween(500, 2000), // Price in cents
        ];
    }

    public function domestic(): static
    {
        return $this->state(fn (array $attributes) => [
            'initial_price' => $this->faker->numberBetween(1500, 5000), // Domestic pricing
            'extra_weight_price' => $this->faker->numberBetween(300, 1500),
        ]);
    }

    public function international(): static
    {
        return $this->state(fn (array $attributes) => [
            'initial_price' => $this->faker->numberBetween(5000, 20000), // International pricing
            'extra_weight_price' => $this->faker->numberBetween(1000, 5000),
        ]);
    }
}
