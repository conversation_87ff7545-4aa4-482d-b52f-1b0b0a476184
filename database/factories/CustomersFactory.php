<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Customers>
 */
class CustomersFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $arabicFirstNames = ['محمد', 'أحمد', 'علي', 'عبدالله', 'عبدالرحمن', 'فهد', 'خالد', 'سعد', 'عمر', 'فاطمة', 'عائشة', 'خديجة', 'مريم', 'نورا', 'ريم', 'سارة'];
        $arabicLastNames = ['العتيبي', 'المطيري', 'الدوسري', 'القحطاني', 'الغامدي', 'الزهراني', 'السلمي', 'الحربي', 'العنزي', 'الشهري'];
        $saudiCities = ['الرياض', 'جدة', 'مكة المكرمة', 'المدينة المنورة', 'الدمام', 'الخبر', 'تبوك', 'بريدة'];

        $firstName = $this->faker->randomElement($arabicFirstNames);
        $lastName = $this->faker->randomElement($arabicLastNames);

        return [
            'username' => $firstName.' '.$lastName,
            'email' => $this->faker->unique()->safeEmail(),
            'phone' => '966'.$this->faker->numerify('5########'),
            'address' => $this->faker->randomElement([
                'شارع الملك فهد، حي العليا، '.$this->faker->randomElement($saudiCities),
                'طريق الملك عبدالعزيز، حي الشفا، '.$this->faker->randomElement($saudiCities),
                'شارع التحلية، حي النهضة، '.$this->faker->randomElement($saudiCities),
                'طريق الأمير محمد بن سلمان، '.$this->faker->randomElement($saudiCities),
            ]),
            'rating' => $this->faker->randomFloat(2, 3.0, 5.0), // Better ratings for customers
            'balance' => $this->faker->randomFloat(2, 0, 5000), // Higher balance range
            'joining_date' => $this->faker->dateTimeBetween('-2 years', 'now'),
        ];
    }
}
