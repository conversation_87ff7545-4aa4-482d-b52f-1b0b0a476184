<?php

namespace Database\Factories;

use App\Models\Webhook;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Webhook>
 */
class WebhookFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Webhook::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'payload' => ['data' => $this->faker->sentence],
            'status' => $this->faker->randomElement(['pending', 'success', 'failed']),
            'error_message' => $this->faker->optional(0.3)->sentence,
            'url' => $this->faker->url,
        ];
    }

    /**
     * Indicate that the webhook is pending.
     *
     * @return \Illuminate\Database\Eloquent\Factories\Factory
     */
    public function pending(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'pending',
            'error_message' => null,
        ]);
    }

    /**
     * Indicate that the webhook is successful.
     *
     * @return \Illuminate\Database\Eloquent\Factories\Factory
     */
    public function success(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'success',
            'error_message' => null,
        ]);
    }

    /**
     * Indicate that the webhook has failed.
     *
     * @return \Illuminate\Database\Eloquent\Factories\Factory
     */
    public function failed(?string $errorMessage = null): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'failed',
            'error_message' => $errorMessage ?? $this->faker->sentence,
        ]);
    }
}
