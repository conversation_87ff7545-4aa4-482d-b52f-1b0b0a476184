<?php

namespace Database\Factories;

use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\SurveyResponse>
 */
class SurveyResponseFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'user_id' => User::factory(),
            'has_ecommerce_store' => $this->faker->randomElement(['yes', 'no']),
            'business_industry' => $this->faker->randomElement([
                'Electronics',
                'Fashion',
                'Health & Beauty',
                'Home & Garden',
                'Sports & Outdoors',
                'Books',
                'Jewelry',
                'Automotive',
                'Food & Beverages',
                'Toys & Games',
            ]),
            'monthly_orders' => $this->faker->randomElement([
                '0-50',
                '50-100',
                '100-500',
                '500-1000',
                '1000+',
            ]),
        ];
    }
}
