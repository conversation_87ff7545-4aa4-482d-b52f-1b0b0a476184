<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\CourierStatus>
 */
class CourierStatusFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $couriers = ['aramex', 'transcorp', 'jt', 'barq', 'spl', 'thabit'];
        $statuses = [
            'pending',
            'picked_up',
            'in_transit',
            'out_for_delivery',
            'delivered',
            'failed_delivery',
            'returned',
        ];

        $courier = $this->faker->randomElement($couriers);
        $orderStatus = $this->faker->randomElement($statuses);

        return [
            'courier' => $courier,
            'code' => $this->getCodeForCourier($courier),
            'description' => $this->getDescriptionForStatus($orderStatus),
            'order_status' => $orderStatus,
        ];
    }

    private function getCodeForCourier(string $courier): string
    {
        return match ($courier) {
            'aramex' => $this->faker->randomElement(['SH002', 'SH003', 'SH004', 'SH023', 'SH007']),
            'transcorp' => $this->faker->randomElement(['001', '002', '003', '004', '005']),
            'jt' => $this->faker->randomElement(['10', '20', '30', '40', '50']),
            'barq' => $this->faker->randomElement(['created', 'picked_up', 'in_transit', 'delivered']),
            'spl' => $this->faker->randomElement(['1', '2', '3', '4', '5']),
            'thabit' => $this->faker->randomElement(['NEW', 'PIC', 'INT', 'DEL', 'RET']),
            default => $this->faker->numerify('##')
        };
    }

    private function getDescriptionForStatus(string $status): string
    {
        return match ($status) {
            'pending' => 'بانتظار الاستلام',
            'picked_up' => 'تم الاستلام',
            'in_transit' => 'في الطريق',
            'out_for_delivery' => 'خرجت للتسليم',
            'delivered' => 'تم التسليم',
            'failed_delivery' => 'فشل في التسليم',
            'returned' => 'تم الإرجاع',
            default => 'حالة غير معروفة'
        };
    }

    public function aramex(): static
    {
        return $this->state(fn (array $attributes) => [
            'courier' => 'aramex',
        ]);
    }

    public function transcorp(): static
    {
        return $this->state(fn (array $attributes) => [
            'courier' => 'transcorp',
        ]);
    }
}
