<?php

namespace Database\Factories;

use App\Models\City;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;

class UserFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $arabicFirstNames = ['محمد', 'أحمد', 'علي', 'عبدالله', 'عبدالرحمن', 'فهد', 'خالد', 'سعد', 'عمر', 'فاطمة', 'عائشة', 'خديجة', 'مريم', 'نورا', 'ريم', 'سارة'];
        $arabicLastNames = ['العتيبي', 'المطيري', 'الدوسري', 'القحطاني', 'الغامدي', 'الزهراني', 'السلمي', 'الحربي', 'العنزي', 'الشهري'];
        $companyTypes = ['شركة', 'مؤسسة', 'متجر', 'مصنع', 'مكتب'];

        return [
            'first_name' => $this->faker->randomElement($arabicFirstNames),
            'last_name' => $this->faker->randomElement($arabicLastNames),
            'email' => $this->faker->userName().time().rand(1000, 9999).'@example.com',
            'email_verified_at' => $this->faker->boolean(70) ? $this->faker->dateTimeThisYear() : null,
            'password' => Hash::make('password'), // Default password
            'remember_token' => Str::random(10),
            'dob' => $this->faker->dateTimeBetween('-60 years', '-18 years'),
            'avatar' => null,
            'phone' => '966'.$this->faker->numerify('5########'),
            'role' => $this->faker->randomElement(['merchant', 'admin']),
            'company_city_id' => City::factory(),
            'company_name' => $this->faker->randomElement($companyTypes).' '.$this->faker->randomElement([
                'النجاح للتجارة',
                'الإبداع للمنتجات',
                'الجودة للاستيراد والتصدير',
                'التميز للخدمات',
                'الريادة للتكنولوجيا',
            ]),
            'company_commercial_registration_number' => $this->faker->numerify('1010######'),
            'company_tax_number' => $this->faker->numerify('3000####01'),
            'wallet_balance' => $this->faker->randomFloat(2, 0, 5000) * 100, // Store in cents
            'rate_cost' => $this->faker->numberBetween(50, 200), // Rate cost in cents (0.50 - 2.00 SAR)
        ];
    }

    /**
     * Indicate that the model's email address should be unverified.
     *
     * @return \Illuminate\Database\Eloquent\Factories\Factory
     */
    public function unverified(): static
    {
        return $this->state(function (array $attributes) {
            return [
                'email_verified_at' => null,
            ];
        });
    }

    /**
     * Indicate that the user is an admin.
     */
    public function admin(): static
    {
        return $this->state(function (array $attributes) {
            return [
                'role' => 'admin',
            ];
        });
    }

    /**
     * Indicate that the user is a merchant.
     */
    public function merchant(): static
    {
        return $this->state(function (array $attributes) {
            return [
                'role' => 'merchant',
            ];
        });
    }
}
