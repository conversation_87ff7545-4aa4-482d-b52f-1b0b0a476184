<?php

namespace Database\Factories;

use App\Models\Order;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\OrderHistory>
 */
class OrderHistoryFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $shipmentCompanies = ['aramex', 'transcorp', 'jt', 'barq', 'spl', 'thabit'];
        $eventTypes = [
            'order_created',
            'shipment_booked',
            'picked_up',
            'in_transit',
            'out_for_delivery',
            'delivered',
            'failed_delivery',
            'returned',
        ];

        $eventType = $this->faker->randomElement($eventTypes);
        $shipmentCompany = $this->faker->randomElement($shipmentCompanies);

        return [
            'order_id' => Order::factory(),
            'performed_by' => $this->faker->randomElement(['system', 'admin', 'courier', 'customer']),
            'description' => $this->getDescriptionForEvent($eventType),
            'shipment_id' => $this->faker->numerify('SHP-########'),
            'event_type' => $eventType,
            'additional_info' => $this->getAdditionalInfoForEvent($eventType),
            'shipment_company' => $shipmentCompany,
            'action_time' => $this->faker->dateTimeThisMonth(),
        ];
    }

    private function getDescriptionForEvent(string $eventType): string
    {
        return match ($eventType) {
            'order_created' => 'تم إنشاء الطلب',
            'shipment_booked' => 'تم حجز الشحنة',
            'picked_up' => 'تم استلام الشحنة من التاجر',
            'in_transit' => 'الشحنة في الطريق',
            'out_for_delivery' => 'خرجت للتسليم',
            'delivered' => 'تم التسليم',
            'failed_delivery' => 'فشل في التسليم',
            'returned' => 'تم إرجاع الشحنة',
            default => 'تحديث الطلب'
        };
    }

    private function getAdditionalInfoForEvent(string $eventType): array
    {
        return match ($eventType) {
            'delivered' => [
                'signature' => $this->faker->name(),
                'delivery_time' => $this->faker->time(),
            ],
            'failed_delivery' => [
                'reason' => $this->faker->randomElement(['العميل غير متواجد', 'عنوان خاطئ', 'رقم هاتف خاطئ']),
            ],
            'returned' => [
                'reason' => $this->faker->randomElement(['رفض العميل', 'عنوان خاطئ', 'تأخير في التسليم']),
            ],
            default => []
        };
    }

    public function delivered(): static
    {
        return $this->state(fn (array $attributes) => [
            'event_type' => 'delivered',
            'description' => 'تم التسليم',
        ]);
    }

    public function inTransit(): static
    {
        return $this->state(fn (array $attributes) => [
            'event_type' => 'in_transit',
            'description' => 'الشحنة في الطريق',
        ]);
    }
}
