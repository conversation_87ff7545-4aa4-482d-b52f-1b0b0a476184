<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\PaymentMethod>
 */
class PaymentMethodFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $methods = [
            'Cash on Delivery',
            'Credit Card',
            'Debit Card',
            'Bank Transfer',
            'STC Pay',
            'Apple Pay',
            'Mada',
            'Visa',
            'Mastercard',
        ];

        return [
            'name' => $this->faker->unique()->randomElement($methods),
        ];
    }
}
