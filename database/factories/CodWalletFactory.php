<?php

namespace Database\Factories;

use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\CodWallet>
 */
class CodWalletFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $isPaid = $this->faker->boolean(60);

        return [
            'user_id' => User::factory(),
            'amount' => $this->faker->numberBetween(5000, 200000), // Amount in cents (50-2000 SAR)
            'status' => $this->faker->randomElement(['pending', 'processing', 'paid', 'cancelled']),
            'type' => $this->faker->randomElement(['weekly', 'monthly', 'on_demand']),
            'bank_invoice' => $isPaid ? $this->faker->numerify('INV-########') : null,
            'paid_at' => $isPaid ? $this->faker->dateTimeThisMonth() : null,
        ];
    }

    public function paid(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'paid',
            'paid_at' => $this->faker->dateTimeThisMonth(),
        ]);
    }

    public function pending(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'pending',
            'paid_at' => null,
        ]);
    }
}
