<?php

namespace Database\Seeders;

use App\Models\Setting;
use Illuminate\Database\Seeder;

class SettingSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $settings = [
            [
                'key' => 'aramex.userName',
                'value' => '<EMAIL>',
            ],
            [
                'key' => 'aramex.password',
                'value' => 'Tt@121314',
            ],
            [
                'key' => 'aramex.accountNumber',
                'value' => '********',
            ],
            [
                'key' => 'aramex.version',
                'value' => 'v1',
            ],
            [
                'key' => 'aramex.accountEntity',
                'value' => 'JED',
            ],
            [
                'key' => 'aramex.accountPin',
                'value' => '797696',
            ],
            [
                'key' => 'aramex.countryCode',
                'value' => 'SA',
            ],
            [
                'key' => 'aramex.source',
                'value' => '24',
            ],
            [
                'key' => 'volumetric_divisor',
                'value' => '4000',
            ],
        ];
        foreach ($settings as $setting) {
            Setting::create($setting);
        }
    }
}
