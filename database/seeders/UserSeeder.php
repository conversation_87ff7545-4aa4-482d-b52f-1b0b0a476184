<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create admin users (only if they don't exist)
        User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'first_name' => 'admin',
                'last_name' => 'admin',
                'role' => 'admin',
                'phone' => '05123456789',
                'dob' => '2000-10-10',
                'password' => Hash::make('12345678'),
                'email_verified_at' => '2022-01-02 17:04:58',
                'avatar' => 'images/avatar-1.jpg',
            ]
        );

        User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'first_name' => 'admin',
                'last_name' => 'admin',
                'role' => 'admin',
                'phone' => '05123456789',
                'dob' => '2000-10-10',
                'password' => Hash::make('12345678'),
                'email_verified_at' => '2022-01-02 17:04:58',
                'avatar' => 'images/avatar-1.jpg',
            ]
        );

        User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'first_name' => 'merchant',
                'last_name' => 'admin',
                'role' => 'merchant',
                'phone' => '05123456789',
                'dob' => '2000-10-10',
                'password' => Hash::make('12345678'),
                'email_verified_at' => '2022-01-02 17:04:58',
                'avatar' => 'images/avatar-1.jpg',
            ]
        );

        User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'first_name' => 'Tareq',
                'last_name' => 'Bin shihon',
                'role' => 'merchant',
                'phone' => '05123456789',
                'dob' => '2000-10-10',
                'password' => Hash::make('12345678'),
                'email_verified_at' => '2022-01-02 17:04:58',
                'avatar' => 'images/avatar-1.jpg',
            ]
        );

        // Create additional random merchant users using the factory
        User::factory()->merchant()->count(15)->create();

        // Create some admin users using the factory
        User::factory()->admin()->count(3)->create();
    }
}
