<?php

namespace Database\Seeders;

use App\Models\Product;
use Illuminate\Database\Seeder;

class ProductsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run()
    {
        // Create products using factory
        Product::factory()->count(20)->create();

        // Create some specific products for testing
        Product::factory()->create([
            'name' => 'Test Product 1',
            'sku' => 'TEST-001',
            'price' => 99.99,
            'category' => 'Electronics',
            'description' => 'A test product for development',
        ]);

        Product::factory()->create([
            'name' => 'Test Product 2',
            'sku' => 'TEST-002',
            'price' => 149.99,
            'category' => 'Clothing',
            'description' => 'Another test product for development',
        ]);
    }
}
