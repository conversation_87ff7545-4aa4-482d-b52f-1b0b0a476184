<?php

namespace Database\Seeders;

use App\Models\UserShipmentCourierService;
use Illuminate\Database\Seeder;

class UserShipmentCourierServiceSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create multiple user shipment courier services using factory
        UserShipmentCourierService::factory()->count(5)->create();

        // Create one specific service for testing purposes
        UserShipmentCourierService::factory()->create([
            'shipment_courier_service_id' => 1,
            'user_id' => 2,
            'username' => 'test_user',
            'password' => 'test_password',
            'account_number' => 'ACC-123456',
            'account_entity' => 'JED',
            'account_pin' => '797696',
        ]);
    }
}
