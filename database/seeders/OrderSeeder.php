<?php

namespace Database\Seeders;

use App\Models\Order;
use App\Models\OrderItem;
use Illuminate\Database\Seeder;

class OrderSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // Create 5 orders with related items
        Order::factory()
            ->count(5)
            ->has(OrderItem::factory()->count(rand(1, 3)), 'items')
            ->create();

        // Create one specific order for testing purposes
        $order = Order::factory()->create([
            'order_number' => 'ORD123456',
            'status' => 'pending',
            'order_grand_total' => 5500,
            'description' => 'Order created for testing',
            'receiver_first_name' => '<PERSON>',
            'receiver_last_name' => 'Doe',
            'receiver_phone' => '1234567890',
            'receiver_email' => '<EMAIL>',
            'receiver_country' => 'USA',
            'receiver_address_line' => '123 Test St, Apt 456',
            'receiver_city' => 'Los Angeles',
            'receiver_country_code' => 'US',
            'payment_method' => 'Credit Card',
            'shipper_name' => 'Treek',
            'tax' => 500,
        ]);

        // Add items to the specific test order
        OrderItem::factory()->create([
            'order_id' => $order->id,
            'name' => 'Sample Product',
            'sku' => 'SP123',
            'quantity' => 2,
            'weight' => 500,
            'price' => 2000,
        ]);
    }
}
