<?php

namespace Database\Seeders;

use App\Models\Meeting;
use App\Models\User;
use Illuminate\Database\Seeder;

class MeetingSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $users = User::where('role', 'merchant')->take(15)->get();

        foreach ($users as $user) {
            // Scheduled meeting
            if (fake()->boolean(40)) {
                Meeting::factory()->scheduled()->create([
                    'user_id' => $user->id,
                ]);
            }

            // Completed meeting
            if (fake()->boolean(30)) {
                Meeting::factory()->completed()->create([
                    'user_id' => $user->id,
                ]);
            }
        }

        // Create additional random meetings
        Meeting::factory(20)->create();
    }
}
