<?php

namespace Database\Seeders;

use App\Models\City;
use App\Models\Country;
use Illuminate\Database\Seeder;

class CitySeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // Get Saudi Arabia
        $saudiArabia = Country::where('code_country', 'SA')->first();
        $saudiId = $saudiArabia ? $saudiArabia->id : 1;

        // Create major Saudi cities
        $saudiCities = [
            [
                'name' => 'Riyadh',
                'name_ar' => 'الرياض',
                'country_id' => $saudiId,
            ],
            [
                'name' => 'Jeddah',
                'name_ar' => 'جدة',
                'country_id' => $saudiId,
            ],
            [
                'name' => 'Mecca',
                'name_ar' => 'مكة المكرمة',
                'country_id' => $saudiId,
            ],
            [
                'name' => 'Medina',
                'name_ar' => 'المدينة المنورة',
                'country_id' => $saudiId,
            ],
            [
                'name' => 'Dammam',
                'name_ar' => 'الدمام',
                'country_id' => $saudiId,
            ],
            [
                'name' => 'Khobar',
                'name_ar' => 'الخبر',
                'country_id' => $saudiId,
            ],
            [
                'name' => 'Tabuk',
                'name_ar' => 'تبوك',
                'country_id' => $saudiId,
            ],
            [
                'name' => 'Buraidah',
                'name_ar' => 'بريدة',
                'country_id' => $saudiId,
            ],
            [
                'name' => 'Khamis Mushait',
                'name_ar' => 'خميس مشيط',
                'country_id' => $saudiId,
            ],
            [
                'name' => 'Hafar Al Batin',
                'name_ar' => 'حفر الباطن',
                'country_id' => $saudiId,
            ],
            [
                'name' => 'Taif',
                'name_ar' => 'الطائف',
                'country_id' => $saudiId,
            ],
            [
                'name' => 'Najran',
                'name_ar' => 'نجران',
                'country_id' => $saudiId,
            ],
            [
                'name' => 'Abha',
                'name_ar' => 'أبها',
                'country_id' => $saudiId,
            ],
            [
                'name' => 'Jazan',
                'name_ar' => 'جازان',
                'country_id' => $saudiId,
            ],
            [
                'name' => 'Qatif',
                'name_ar' => 'القطيف',
                'country_id' => $saudiId,
            ],
        ];

        foreach ($saudiCities as $cityData) {
            City::firstOrCreate(
                ['name' => $cityData['name'], 'country_id' => $cityData['country_id']],
                $cityData
            );
        }

        // Create cities for other countries
        $countries = Country::where('code_country', '!=', 'SA')->take(5)->get();
        foreach ($countries as $country) {
            City::factory(rand(3, 8))->create([
                'country_id' => $country->id,
            ]);
        }

        // Create additional random cities
        City::factory(20)->create();
    }
}
