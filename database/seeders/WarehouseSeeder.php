<?php

namespace Database\Seeders;

use App\Models\Warehouse;
use Illuminate\Database\Seeder;

class WarehouseSeeder extends Seeder
{
    public function run()
    {
        // Create multiple warehouses using factory
        Warehouse::factory()->count(3)->create();

        // Create one specific warehouse for testing purposes
        Warehouse::factory()->create([
            'name' => 'Main Warehouse',
            'code' => 'MAIN001',
            'address' => '123 Main St',
            'sender_name' => 'Warehouse Manager',
            'sender_email' => '<EMAIL>',
            'sender_phone' => '+966123456789',
            'status' => 1,
        ]);
    }
}
