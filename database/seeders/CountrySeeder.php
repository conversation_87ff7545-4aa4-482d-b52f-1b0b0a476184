<?php

namespace Database\Seeders;

use App\Models\Country;
use Illuminate\Database\Seeder;

class CountrySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $countries = [
            [
                'name' => 'Saudi Arabia',
                'name_ar' => 'المملكة العربية السعودية',
                'code_country' => 'SA',
                'phone_code' => '+966',
                'phone_number_length' => 9,
            ],
            [
                'name' => 'Jordan',
                'name_ar' => 'الأردن',
                'code_country' => 'JO',
                'phone_code' => '+962',
                'phone_number_length' => 9,
            ],
            [
                'name' => 'United Arab Emirates',
                'name_ar' => 'الإمارات العربية المتحدة',
                'code_country' => 'AE',
                'phone_code' => '+971',
                'phone_number_length' => 9,
            ],
            [
                'name' => 'Kuwait',
                'name_ar' => 'الكويت',
                'code_country' => 'KW',
                'phone_code' => '+965',
                'phone_number_length' => 8,
            ],
            [
                'name' => 'Qatar',
                'name_ar' => 'قطر',
                'code_country' => 'QA',
                'phone_code' => '+974',
                'phone_number_length' => 8,
            ],
            [
                'name' => 'Bahrain',
                'name_ar' => 'البحرين',
                'code_country' => 'BH',
                'phone_code' => '+973',
                'phone_number_length' => 8,
            ],
            [
                'name' => 'Oman',
                'name_ar' => 'عمان',
                'code_country' => 'OM',
                'phone_code' => '+968',
                'phone_number_length' => 8,
            ],
            [
                'name' => 'Egypt',
                'name_ar' => 'مصر',
                'code_country' => 'EG',
                'phone_code' => '+20',
                'phone_number_length' => 10,
            ],
            [
                'name' => 'United States',
                'name_ar' => 'الولايات المتحدة',
                'code_country' => 'US',
                'phone_code' => '+1',
                'phone_number_length' => 10,
            ],
            [
                'name' => 'United Kingdom',
                'name_ar' => 'المملكة المتحدة',
                'code_country' => 'GB',
                'phone_code' => '+44',
                'phone_number_length' => 10,
            ],
        ];

        foreach ($countries as $country) {
            Country::firstOrCreate(
                ['code_country' => $country['code_country']],
                $country
            );
        }

        // Create additional countries using factory with duplicate handling
        $additionalCountriesCreated = 0;
        $maxAttempts = 50; // Prevent infinite loop
        $attempts = 0;

        while ($additionalCountriesCreated < 15 && $attempts < $maxAttempts) {
            try {
                $countryData = Country::factory()->make()->toArray();
                Country::firstOrCreate(
                    ['code_country' => $countryData['code_country']],
                    $countryData
                );
                $additionalCountriesCreated++;
            } catch (\Exception $e) {
                // Skip this iteration if there's a duplicate
            }
            $attempts++;
        }
    }
}
