<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     *
     * @return void
     */
    public function run()
    {
        $this->command->info('🌱 Starting database seeding...');

        // Core data that other seeders depend on
        $this->command->info('📍 Seeding core geographical data...');
        $this->call(CountrySeeder::class);
        $this->call(CitySeeder::class);
        $this->call(CityTranslationSeeder::class);

        // System configuration
        $this->command->info('⚙️ Seeding system configuration...');
        $this->call(CurrencySeeder::class);
        $this->call(PaymentMethodSeeder::class);
        $this->call(SettingSeeder::class);
        $this->call(FaqSeeder::class);

        // Shipping services and pricing
        $this->command->info('🚚 Seeding shipping services...');
        $this->call(ShipmentCourierServicesSeeder::class);
        $this->call(SalesChannelSeeder::class);
        $this->call(ShipmentCountriesPriceSeeder::class);
        $this->call(CourierStatusSeeder::class);

        // Users and accounts
        $this->command->info('👥 Seeding users and merchants...');
        $this->call(UserSeeder::class);
        $this->call(CustomersSeeder::class);
        $this->call(MerchantSeeder::class);
        $this->call(WarehouseSeeder::class);
        $this->call(UserShipmentCourierServiceSeeder::class);

        // Business data
        $this->command->info('📦 Seeding business data...');
        $this->call(ProductsSeeder::class);
        $this->call(BoxesSeeder::class);
        $this->call(OrderSeeder::class);

        // Additional data that depends on orders/users
        $this->command->info('📊 Seeding transactional data...');
        $this->call(OrderHistorySeeder::class);
        $this->call(TransactionSeeder::class);
        $this->call(WalletTransactionSeeder::class);
        $this->call(CodWalletSeeder::class);
        $this->call(InvoiceSeeder::class);

        // User engagement data
        $this->command->info('🎯 Seeding user engagement data...');
        $this->call(SurveyResponseSeeder::class);
        $this->call(MeetingSeeder::class);

        $this->command->info('✅ Database seeding completed successfully!');
    }
}
