<?php

namespace Database\Seeders;

use App\Models\Transaction;
use App\Models\User;
use Illuminate\Database\Seeder;

class TransactionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $users = User::where('role', 'merchant')->take(15)->get();

        // Create transactions for existing users
        foreach ($users as $user) {
            // Completed transactions
            Transaction::factory()->completed()->create([
                'user_id' => $user->id,
            ]);

            // Some pending transactions
            if (fake()->boolean(40)) {
                Transaction::factory()->pending()->create([
                    'user_id' => $user->id,
                ]);
            }

            // Some failed transactions
            if (fake()->boolean(20)) {
                Transaction::factory()->failed()->create([
                    'user_id' => $user->id,
                ]);
            }
        }

        // Create additional random transactions
        Transaction::factory(30)->create();
    }
}
