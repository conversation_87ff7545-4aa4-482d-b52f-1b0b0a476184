<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Lang;

class SalesChannelSeeder extends Seeder
{
    public function run()
    {
        $salesChannels = [
            [
                'name' => json_encode(['en' => 'Shopify', 'ar' => 'شوبيفاي'], JSON_UNESCAPED_UNICODE),
                'identifier' => 'shopify',
                'description' => json_encode([
                    'en' => 'A global commerce platform to sell online, offline, and everywhere in between.',
                    'ar' => 'اربط بين تجارتك الإلكترونية وغير الإلكترونية بسهولة مع منصة عالمية استثنائية للتجارة.',
                ], JSON_UNESCAPED_UNICODE),
                'logo' => 'https://storage.googleapis.com/tryoto-public/sales-channels-logo/shopify.png',
                'url' => '/sales-channels/shopify-form',
                'action_btn_label' => json_encode(['en' => 'link', 'ar' => 'ربط'], JSON_UNESCAPED_UNICODE),
                'action_btn_label_en' => Lang::get('translation.connect'),
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => json_encode(['en' => 'Salla', 'ar' => 'سلة'], JSON_UNESCAPED_UNICODE),
                'identifier' => 'salla',
                'description' => json_encode([
                    'en' => 'Connect your Salla store with Treek application in Salla apps store.',
                    'ar' => 'ربط متجرك في سلة مع تطبيق ترييك من خلال متجر تطبيقات سلة.',
                ], JSON_UNESCAPED_UNICODE),
                'logo' => 'https://storage.googleapis.com/tryoto-public/sales-channels-logo/salla.png',
                'url' => null,
                'action_btn_label' => json_encode(['en' => 'link steps', 'ar' => 'خطوات الربط'], JSON_UNESCAPED_UNICODE),
                'action_btn_label_en' => Lang::get('translation.connection_instructions'),
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => json_encode(['en' => 'Zid', 'ar' => 'زد'], JSON_UNESCAPED_UNICODE),
                'identifier' => 'zid',
                'description' => json_encode([
                    'en' => 'Grow your business, sell, ship, and process your payments from 1 dashboard.',
                    'ar' => 'من لوحة تحكم واحدة ستتمكن من إدارة وتنمية تجارتك وبيع وشحن ومعالجة مدفوعات بسهولة.',
                ], JSON_UNESCAPED_UNICODE),
                'logo' => 'https://storage.googleapis.com/tryoto-public/sales-channels-logo/zid.png',
                'url' => '/sales-channels/zid-form',
                'action_btn_label' => json_encode(['en' => 'link steps', 'ar' => 'خطوات الربط'], JSON_UNESCAPED_UNICODE),
                'action_btn_label_en' => Lang::get('translation.connection_instructions'),
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ];

        DB::table('sales_channels')->insert($salesChannels);
    }
}
