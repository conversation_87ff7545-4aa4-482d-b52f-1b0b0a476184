<?php

namespace Database\Seeders;

use App\Models\Order;
use App\Models\OrderHistory;
use Illuminate\Database\Seeder;

class OrderHistorySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $orders = Order::take(20)->get();

        foreach ($orders as $order) {
            // Create order created history
            OrderHistory::factory()->create([
                'order_id' => $order->id,
                'event_type' => 'order_created',
                'description' => 'تم إنشاء الطلب',
                'performed_by' => 'system',
            ]);

            // Create shipment booked history if order has been processed
            if (fake()->boolean(70)) {
                OrderHistory::factory()->create([
                    'order_id' => $order->id,
                    'event_type' => 'shipment_booked',
                    'description' => 'تم حجز الشحنة',
                    'performed_by' => 'system',
                ]);
            }

            // Create in transit history for some orders
            if (fake()->boolean(50)) {
                OrderHistory::factory()->inTransit()->create([
                    'order_id' => $order->id,
                ]);
            }

            // Create delivered history for some orders
            if (fake()->boolean(30)) {
                OrderHistory::factory()->delivered()->create([
                    'order_id' => $order->id,
                ]);
            }
        }

        // Create additional random order histories
        OrderHistory::factory(80)->create();
    }
}
