<?php

namespace Database\Seeders;

use App\Models\Country;
use App\Models\ShipmentCountriesPrice;
use Illuminate\Database\Seeder;

class ShipmentCountriesPriceSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $countries = Country::all();

        // Create pricing for Saudi Arabia (domestic)
        $saudiArabia = $countries->where('code_country', 'SA')->first();
        if ($saudiArabia) {
            ShipmentCountriesPrice::create([
                'country_id' => $saudiArabia->id,
                'initial_price' => 2500, // 25.00 SAR in cents
                'extra_weight_price' => 500, // 5.00 SAR in cents
            ]);
        }

        // Create pricing for GCC countries
        $gccCountries = $countries->whereIn('code_country', ['AE', 'KW', 'QA', 'BH', 'OM'])->take(5);
        foreach ($gccCountries as $country) {
            ShipmentCountriesPrice::create([
                'country_id' => $country->id,
                'initial_price' => fake()->numberBetween(4000, 8000), // 40-80 SAR in cents
                'extra_weight_price' => fake()->numberBetween(800, 2000), // 8-20 SAR in cents
            ]);
        }

        // Create pricing for other Arab countries
        $arabCountries = $countries->whereIn('code_country', ['EG', 'JO', 'LB', 'SY', 'IQ'])->take(5);
        foreach ($arabCountries as $country) {
            ShipmentCountriesPrice::create([
                'country_id' => $country->id,
                'initial_price' => fake()->numberBetween(8000, 15000), // 80-150 SAR in cents
                'extra_weight_price' => fake()->numberBetween(1500, 3500), // 15-35 SAR in cents
            ]);
        }

        // Create pricing for international countries
        $intlCountries = $countries->whereIn('code_country', ['US', 'GB', 'DE', 'FR', 'CN', 'IN'])->take(6);
        foreach ($intlCountries as $country) {
            ShipmentCountriesPrice::create([
                'country_id' => $country->id,
                'initial_price' => fake()->numberBetween(15000, 30000), // 150-300 SAR in cents
                'extra_weight_price' => fake()->numberBetween(2500, 6000), // 25-60 SAR in cents
            ]);
        }

        // Create random pricing for remaining countries
        $remainingCountries = $countries->whereNotIn('code_country', [
            'SA', 'AE', 'KW', 'QA', 'BH', 'OM', 'EG', 'JO', 'LB', 'SY', 'IQ', 'US', 'GB', 'DE', 'FR', 'CN', 'IN',
        ])->take(10);

        foreach ($remainingCountries as $country) {
            ShipmentCountriesPrice::create([
                'country_id' => $country->id,
                'initial_price' => fake()->numberBetween(2000, 15000),
                'extra_weight_price' => fake()->numberBetween(500, 3000),
            ]);
        }
    }
}
