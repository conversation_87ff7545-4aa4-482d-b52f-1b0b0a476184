<?php

namespace Database\Seeders;

use App\Models\City;
use App\Models\CityTranslation;
use Illuminate\Database\Seeder;

class CityTranslationSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $cities = City::take(20)->get();
        $sources = ['name_aramex', 'name_salla', 'name_salla_second', 'name_thabit', 'name_zid'];

        foreach ($cities as $city) {
            // Create translations for major shipping services
            foreach ($sources as $source) {
                if (fake()->boolean(70)) { // 70% chance to have translation for each source
                    CityTranslation::factory()->create([
                        'city_id' => $city->id,
                        'source' => $source,
                        'value' => $this->getTranslationValue($city->name, $source),
                    ]);
                }
            }
        }

        // Create additional random city translations
        CityTranslation::factory(50)->create();
    }

    private function getTranslationValue(string $cityName, string $source): string
    {
        // Create variation of city name for different sources
        $variations = [
            'الرياض' => [
                'name_aramex' => 'الرياض',
                'name_salla' => 'الرياض',
                'name_salla_second' => 'مدينة الرياض',
                'name_thabit' => 'الرياض',
                'name_zid' => 'RIYADH',
            ],
            'جدة' => [
                'name_aramex' => 'جدة',
                'name_salla' => 'جدة',
                'name_salla_second' => 'مدينة جدة',
                'name_thabit' => 'جدة',
                'name_zid' => 'JEDDAH',
            ],
            'مكة' => [
                'name_aramex' => 'مكة المكرمة',
                'name_salla' => 'مكة',
                'name_salla_second' => 'مكة المكرمة',
                'name_thabit' => 'مكة',
                'name_zid' => 'MAKKAH',
            ],
        ];

        if (isset($variations[$cityName]) && isset($variations[$cityName][$source])) {
            return $variations[$cityName][$source];
        }

        // Default behavior for other cities
        return match ($source) {
            'name_zid' => strtoupper(str_replace(['ا', 'ة', 'ي'], ['A', 'H', 'I'], $cityName)),
            default => $cityName
        };
    }
}
