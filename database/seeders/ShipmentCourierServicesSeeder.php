<?php

namespace Database\Seeders;

use App\Models\ShipmentCourierService;
use Illuminate\Database\Seeder;

class ShipmentCourierServicesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $shipmentCourierServices = [
            [
                'courier_name' => 'أرامكس',
                'identifier' => 'aramex',
                'service_name' => 'شحن سريع',
                'logo' => 'aramex.png',
                'logo_big' => 'aramex.png',
                'base_price' => 20,
                'extra_weight_from' => 20,
                'additional_weight_cost' => 20,
                'cash_on_delivery_cost' => 20,
                'distance_cost' => 20,
                'volumetric_divisor' => 5000,
                'base_volumetric_divisor' => 5000,
            ],
            [
                'courier_name' => 'برق',
                'identifier' => 'barq',
                'service_name' => 'الشحن الثقيل والضخم',
                'logo' => 'barq.png',
                'logo_big' => 'barq.png',
                'base_price' => 20,
                'extra_weight_from' => 20,
                'additional_weight_cost' => 20,
                'cash_on_delivery_cost' => 20,
                'distance_cost' => 20,
                'volumetric_divisor' => 5000,
                'base_volumetric_divisor' => 5000,
            ],
            [
                'courier_name' => 'ترانسكورب',
                'identifier' => 'transcorp',
                'service_name' => 'الشحن الثقيل والضخم',
                'logo' => 'transcorp.png',
                'logo_big' => 'transcorp.png',
                'base_price' => 20,
                'extra_weight_from' => 20,
                'additional_weight_cost' => 20,
                'cash_on_delivery_cost' => 20,
                'distance_cost' => 20,
                'volumetric_divisor' => 5000,
                'base_volumetric_divisor' => 5000,
            ],
            [
                'courier_name' => 'ثابت',
                'identifier' => 'thabit',
                'service_name' => 'الشحن الثقيل والضخم',
                'logo' => 'thabit.png',
                'logo_big' => 'thabit.png',
                'base_price' => 20,
                'extra_weight_from' => 20,
                'additional_weight_cost' => 20,
                'cash_on_delivery_cost' => 20,
                'distance_cost' => 20,
                'volumetric_divisor' => 5000,
                'base_volumetric_divisor' => 5000,
            ],
        ];

        foreach ($shipmentCourierServices as $shipmentCourierService) {
            ShipmentCourierService::create($shipmentCourierService);
        }
    }
}
