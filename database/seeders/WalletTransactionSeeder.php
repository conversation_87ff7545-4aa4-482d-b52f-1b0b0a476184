<?php

namespace Database\Seeders;

use App\Models\Order;
use App\Models\User;
use App\Models\WalletTransaction;
use Illuminate\Database\Seeder;

class WalletTransactionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $users = User::where('role', 'merchant')->take(10)->get();
        $orders = Order::take(20)->get();

        // Create credit transactions (adding money to wallet)
        foreach ($users as $user) {
            WalletTransaction::factory()->credit()->create([
                'user_id' => $user->id,
            ]);

            // Update user wallet balance
            $totalCredits = WalletTransaction::where('user_id', $user->id)
                ->where('type', 'credit')
                ->sum('amount');
            $totalDebits = WalletTransaction::where('user_id', $user->id)
                ->where('type', 'debit')
                ->sum('amount');

            $user->update(['wallet_balance' => $totalCredits - $totalDebits]);
        }

        // Create debit transactions (spending money from wallet) with orders
        foreach ($orders->take(15) as $order) {
            if ($order->merchant && $order->merchant->user) {
                WalletTransaction::factory()->debit()->create([
                    'user_id' => $order->merchant->user->id,
                    'order_id' => $order->id,
                ]);
            }
        }

        // Create some cancel transactions (refunds)
        foreach ($orders->take(5) as $order) {
            if ($order->merchant && $order->merchant->user) {
                WalletTransaction::factory()->cancel()->create([
                    'user_id' => $order->merchant->user->id,
                    'order_id' => $order->id,
                ]);
            }
        }

        // Create additional random transactions
        WalletTransaction::factory(50)->create();
    }
}
