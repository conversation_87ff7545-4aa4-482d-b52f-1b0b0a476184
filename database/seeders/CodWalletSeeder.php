<?php

namespace Database\Seeders;

use App\Models\CodWallet;
use App\Models\User;
use Illuminate\Database\Seeder;

class CodWalletSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $users = User::where('role', 'merchant')->take(12)->get();

        // Create COD wallet entries for merchants
        foreach ($users as $user) {
            // Paid COD wallet
            CodWallet::factory()->paid()->create([
                'user_id' => $user->id,
            ]);

            // Pending COD wallet
            if (fake()->boolean(60)) {
                CodWallet::factory()->pending()->create([
                    'user_id' => $user->id,
                ]);
            }
        }

        // Create additional random COD wallets
        CodWallet::factory(25)->create();
    }
}
