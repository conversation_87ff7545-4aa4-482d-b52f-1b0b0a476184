<?php

namespace Database\Seeders;

use App\Models\Box;
use Illuminate\Database\Seeder;

class BoxesSeeder extends Seeder
{
    public function run()
    {
        // Create boxes using factory
        Box::factory()->count(10)->create();

        // Create specific box types for testing
        Box::factory()->create([
            'type' => 'Standard Box',
            'length' => 20.00,
            'width' => 15.00,
            'height' => 10.00,
            'weight' => 15.00,
            'user_id' => 1,
        ]);
    }
}
