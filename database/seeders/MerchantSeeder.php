<?php

namespace Database\Seeders;

use App\Enums\SalesChannelEnum;
use App\Models\Merchant;
use Illuminate\Database\Seeder;

class MerchantSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create some random merchants using factory
        Merchant::factory()->count(3)->create();

        // Create specific merchants for testing
        Merchant::create([
            'name' => 'Dive Salla',
            'merchant_id' => null,
            'user_id' => 2,
            'type' => SalesChannelEnum::SALLA->value,
            'webhook_url' => 'https://gotreek.com/api/webhook/salla/67538d183b40a',
        ]);

        Merchant::create([
            'name' => '<PERSON><PERSON>',
            'merchant_id' => null,
            'user_id' => 2,
            'type' => SalesChannelEnum::SALLA->value,
            'webhook_url' => 'https://gotreek.com/api/webhook/salla/67547a903c49e',
        ]);
    }
}
