<?php

namespace Database\Seeders;

use App\Models\Currency;
use Illuminate\Database\Seeder;

class CurrencySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $currencies = [
            [
                'code' => 'USD',
                'name' => 'US Dollar',
            ],
            [
                'code' => 'EUR',
                'name' => 'Euro',
            ],
            [
                'code' => 'SAR',
                'name' => 'Saudi Riyal',
            ],
            [
                'code' => 'AED',
                'name' => 'UAE Dirham',
            ],
            [
                'code' => 'KWD',
                'name' => 'Kuwaiti Dinar',
            ],
            [
                'code' => 'OMR',
                'name' => 'Omani Rial',
            ],
            [
                'code' => 'BHD',
                'name' => 'Bahraini Dinar',
            ],
            [
                'code' => 'EGP',
                'name' => 'Egyptian Pound',
            ],
            [
                'code' => 'GBP',
                'name' => 'British Pound Sterling',
            ],
            [
                'code' => 'QAR',
                'name' => 'Qatari Riyal',
            ],
            [
                'code' => 'TRY',
                'name' => 'Turkish Lira',
            ],
        ];

        foreach ($currencies as $currency) {
            Currency::create($currency);
        }
    }
}
