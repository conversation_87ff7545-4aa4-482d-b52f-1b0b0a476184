<?php

namespace Database\Seeders;

use App\Models\Invoice;
use App\Models\WalletTransaction;
use Illuminate\Database\Seeder;

class InvoiceSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $walletTransactions = WalletTransaction::where('type', 'credit')->take(15)->get();

        foreach ($walletTransactions as $transaction) {
            // Create invoice for credit transactions (wallet top-ups)
            if (fake()->boolean(60)) {
                Invoice::factory()->create([
                    'wallet_transaction_id' => $transaction->id,
                    'amount' => $transaction->amount / 100, // Convert from cents
                    'tax_amount' => ($transaction->amount / 100) * 0.15, // 15% tax
                    'total_amount' => ($transaction->amount / 100) * 1.15,
                ]);
            }
        }

        // Create some paid invoices
        $paidTransactions = WalletTransaction::where('type', 'credit')->skip(15)->take(10)->get();
        foreach ($paidTransactions as $transaction) {
            if (fake()->boolean(40)) {
                Invoice::factory()->paid()->create([
                    'wallet_transaction_id' => $transaction->id,
                    'amount' => $transaction->amount / 100,
                    'tax_amount' => ($transaction->amount / 100) * 0.15,
                    'total_amount' => ($transaction->amount / 100) * 1.15,
                ]);
            }
        }

        // Create some overdue invoices
        $overdueTransactions = WalletTransaction::where('type', 'credit')->skip(25)->take(5)->get();
        foreach ($overdueTransactions as $transaction) {
            Invoice::factory()->overdue()->create([
                'wallet_transaction_id' => $transaction->id,
                'amount' => $transaction->amount / 100,
                'tax_amount' => ($transaction->amount / 100) * 0.15,
                'total_amount' => ($transaction->amount / 100) * 1.15,
            ]);
        }

        // Create additional random invoices
        Invoice::factory(20)->create();
    }
}
