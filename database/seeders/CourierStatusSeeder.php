<?php

namespace Database\Seeders;

use App\Enums\OrderStatusEnum;
use App\Models\CourierStatus;
use Illuminate\Database\Seeder;

class CourierStatusSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->command->info('Starting CourierStatusSeeder...');

        // Clear existing data
        //        CourierStatus::truncate();
        $this->command->info('Cleared existing data');

        // Add JT status mappings
        $this->command->info('Seeding JT statuses...');
        $this->seedJtStatuses();

        // Add other courier status mappings
        $this->command->info('Seeding Thabit statuses...');
        $this->seedThabitStatuses();

        $this->command->info('Seeding Barq statuses...');
        $this->seedBarqStatuses();

        $this->command->info('Seeding Transcorp statuses...');
        $this->seedTranscorpStatuses();

        $this->command->info('Seeding SPL statuses...');
        $this->seedSplStatuses();

        $this->command->info('CourierStatusSeeder completed successfully!');
    }

    /**
     * Seed JT status mappings
     */
    private function seedJtStatuses(): void
    {
        $jtMappings = [
            // currently_shipping
            [
                'codes' => [
                    'Pickup scan',
                    'Station sending/DC sending',
                    'Station arrival',
                    'DC arrival',
                    'International warehousing',
                    'International Air Arrival',
                    'International Air Shipment',
                    'Customs declaration scan',
                    'Customs clearance scan',
                    'Change Address scan',
                ],
                'status' => OrderStatusEnum::CURRENTLY_SHIPPING->value,
                'description' => 'Currently Shipping',
            ],

            // delivered
            [
                'codes' => ['Delivery scan', 'Sign scan'],
                'status' => OrderStatusEnum::DELIVERED->value,
                'description' => 'Delivered',
            ],

            // returned
            [
                'codes' => ['Returned parcel scan', 'Returned signed'],
                'status' => OrderStatusEnum::RETURNED->value,
                'description' => 'Returned',
            ],

            // canceled (handled specially in the command)
            [
                'codes' => ['Abnormal parcel scan'],
                'status' => OrderStatusEnum::CANCELED->value,
                'description' => 'Canceled',
            ],
        ];

        foreach ($jtMappings as $mapping) {
            foreach ($mapping['codes'] as $code) {
                CourierStatus::updateOrCreate(
                    [
                        'courier' => 'jt',
                        'code' => $code,
                    ],
                    [
                        'description' => $mapping['description'],
                        'order_status' => $mapping['status'],
                    ]
                );
            }
        }
    }

    /**
     * Seed Thabit status mappings
     */
    private function seedThabitStatuses(): void
    {
        $thabitMappings = [
            // pending
            [
                'codes' => ['empty_status'],
                'status' => OrderStatusEnum::PENDING->value,
                'description' => 'Pending',
            ],

            // awaiting_pickup
            [
                'codes' => [
                    'assigned_to_courier', 'accepted', 'first_pickup_attempt',
                    'second_pickup_attempt', 'third_pickup_attempt', 'parcel_not_ready',
                ],
                'status' => OrderStatusEnum::AWAITING_PICKUP->value,
                'description' => 'Awaiting Pickup',
            ],

            // currently_shipping
            [
                'codes' => [
                    'first_delivery_attempt', 'second_delivery_attempt', 'third_delivery_attempt',
                    'recipient_address_change_requested', 'future_delivery_requested',
                    'recipient_mobile_no_response', 'dispatched', 'in_transit',
                    'in_sorting_facility', 'picked_up', 'pickup_rejected',
                    'out_for_delivery', 'on_his_way',
                ],
                'status' => OrderStatusEnum::CURRENTLY_SHIPPING->value,
                'description' => 'Currently Shipping',
            ],

            // shipment_on_hold
            [
                'codes' => ['bad_recipient_address', 'on_hold'],
                'status' => OrderStatusEnum::SHIPMENT_ON_HOLD->value,
                'description' => 'Shipment On Hold',
            ],

            // delivered
            [
                'codes' => ['completed', 'arrived_to_delivery_address'],
                'status' => OrderStatusEnum::DELIVERED->value,
                'description' => 'Delivered',
            ],

            // returned
            [
                'codes' => ['to_be_returned', 'out_for_return', 'returned_to_origin'],
                'status' => OrderStatusEnum::RETURNED->value,
                'description' => 'Returned',
            ],

            // canceled
            [
                'codes' => ['cancelled'],
                'status' => OrderStatusEnum::CANCELED->value,
                'description' => 'Canceled',
            ],
        ];

        foreach ($thabitMappings as $mapping) {
            foreach ($mapping['codes'] as $code) {
                CourierStatus::updateOrCreate(
                    [
                        'courier' => 'thabit',
                        'code' => $code,
                    ],
                    [
                        'description' => $mapping['description'],
                        'order_status' => $mapping['status'],
                    ]
                );
            }
        }
    }

    /**
     * Seed Barq status mappings
     */
    private function seedBarqStatuses(): void
    {
        $barqMappings = [
            // delivered
            [
                'codes' => ['completed'],
                'status' => OrderStatusEnum::DELIVERED->value,
                'description' => 'Delivered',
            ],

            // failed
            [
                'codes' => ['SH280', 'SH492'],
                'status' => OrderStatusEnum::FAILED->value,
                'description' => 'Failed',
            ],
        ];

        foreach ($barqMappings as $mapping) {
            foreach ($mapping['codes'] as $code) {
                CourierStatus::updateOrCreate(
                    [
                        'courier' => 'barq',
                        'code' => $code,
                    ],
                    [
                        'description' => $mapping['description'],
                        'order_status' => $mapping['status'],
                    ]
                );
            }
        }
    }

    /**
     * Seed Transcorp status mappings
     */
    private function seedTranscorpStatuses(): void
    {
        $transcorpMappings = [
            // currently_shipping
            [
                'codes' => ['PICKED_UP', 'HUB_TRANSFER', 'OUT_FOR_DELIVERY'],
                'status' => OrderStatusEnum::CURRENTLY_SHIPPING->value,
                'description' => 'Currently Shipping',
            ],

            // delivered
            [
                'codes' => ['DELIVERED'],
                'status' => OrderStatusEnum::DELIVERED->value,
                'description' => 'Delivered',
            ],
        ];

        foreach ($transcorpMappings as $mapping) {
            foreach ($mapping['codes'] as $code) {
                CourierStatus::updateOrCreate(
                    [
                        'courier' => 'transcorp',
                        'code' => $code,
                    ],
                    [
                        'description' => $mapping['description'],
                        'order_status' => $mapping['status'],
                    ]
                );
            }
        }
    }

    /**
     * Seed SPL status mappings
     */
    private function seedSplStatuses(): void
    {
        $splMappings = [
            // pending
            [
                'codes' => [
                    '251', '292', '305', '68', '221', '229', '248', '259', '263', '272', '318', '34',
                    '380', '88', '127', '2', '217', '379', '52', '53', '128', '246', '66', '1', '223',
                    '262', '33', '389', '6', '208', '222', '294', '323', '388', '7', '92', '20', '297',
                    '48', '121', '129', '130', '154', '288', '335', '378', '9',
                ],
                'status' => OrderStatusEnum::PENDING->value,
                'description' => 'Pending',
            ],

            // awaiting_pickup
            [
                'codes' => ['45', '47', '77', '328', '336'],
                'status' => OrderStatusEnum::AWAITING_PICKUP->value,
                'description' => 'Awaiting Pickup',
            ],

            // currently_shipping
            [
                'codes' => ['316', '269', '142', '18', '300', '299', '295', '202', '297', '48', '320'],
                'status' => OrderStatusEnum::CURRENTLY_SHIPPING->value,
                'description' => 'Currently Shipping',
            ],

            // delivered
            [
                'codes' => ['295', '299', '300', '297', '9'],
                'status' => OrderStatusEnum::DELIVERED->value,
                'description' => 'Delivered',
            ],

            // returned
            [
                'codes' => ['317', 'RET'],
                'status' => OrderStatusEnum::RETURNED->value,
                'description' => 'Returned',
            ],

            // shipment_on_hold
            [
                'codes' => ['294', '142', 'H', 'EDE'],
                'status' => OrderStatusEnum::SHIPMENT_ON_HOLD->value,
                'description' => 'Shipment On Hold',
            ],

            // canceled
            [
                'codes' => ['333'],
                'status' => OrderStatusEnum::CANCELED->value,
                'description' => 'Canceled',
            ],

            // failed
            [
                'codes' => ['329'],
                'status' => OrderStatusEnum::FAILED->value,
                'description' => 'Failed',
            ],
        ];

        foreach ($splMappings as $mapping) {
            foreach ($mapping['codes'] as $code) {
                CourierStatus::updateOrCreate(
                    [
                        'courier' => 'spl',
                        'code' => $code,
                    ],
                    [
                        'description' => $mapping['description'],
                        'order_status' => $mapping['status'],
                    ]
                );
            }
        }
    }
}
