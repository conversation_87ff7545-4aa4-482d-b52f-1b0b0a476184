<?php

namespace Database\Seeders;

use App\Models\SurveyResponse;
use App\Models\User;
use Illuminate\Database\Seeder;

class SurveyResponseSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $users = User::where('role', 'merchant')->take(20)->get();

        foreach ($users as $user) {
            if (! $user->survey) {
                SurveyResponse::factory()->create([
                    'user_id' => $user->id,
                ]);
            }
        }

        // Create additional random survey responses
        SurveyResponse::factory(30)->create();
    }
}
