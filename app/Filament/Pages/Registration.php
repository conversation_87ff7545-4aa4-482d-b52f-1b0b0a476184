<?php

namespace App\Filament\Pages;

use App\Models\Country;
use App\Models\User;
use App\Services\SettingsService;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Components\Wizard;
use Filament\Forms\Form;
use Filament\Forms\Get;
use Filament\Pages\Auth\Register;
use Illuminate\Support\Facades\Blade;
use Illuminate\Support\Facades\Request;
use Illuminate\Support\HtmlString;
use Ysfkaya\FilamentPhoneInput\Forms\PhoneInput;

class Registration extends Register
{
    protected ?string $maxWidth = '3xl';

    public ?string $coupon_code = null;

    public bool $use_coupon_code = false;

    public function mount(): void
    {
        parent::mount();

        $code = Request::query('coupon_code');

        if ($code) {
            $this->form->fill([
                'coupon_code' => $code,
                'use_coupon_code' => $code = true,
            ]);
        }
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Wizard::make([
                    Wizard\Step::make('Contact')
                        ->label('')
                        ->schema([

                            $this->getEmailFormComponent(),
                            $this->getPasswordFormComponent(),
                            $this->getPasswordConfirmationFormComponent(),
                            Toggle::make('use_coupon_code')
                                ->label(__('translation.use_coupon_code'))
                                ->reactive(),
                            TextInput::make('coupon_code')
                                ->label(__('translation.coupon_code'))
                                ->hint(__('translation.coupon_hint'))
                                ->disabled(fn (Get $get) => ! $get('use_coupon_code'))
                                ->requiredIf('use_coupon_code', true)
                                ->rule(function (Get $get) {
                                    return function (string $attribute, $value, \Closure $fail) use ($get) {
                                        if ($get('use_coupon_code') && $value !== app(SettingsService::class)->getCouponCode()) {
                                            $fail(__('translation.coupon_code_invalid'));
                                        }
                                    };
                                })
                                ->reactive(),

                        ]),
                    Wizard\Step::make('Social')
                        ->label('')
                        ->schema([
                            TextInput::make('first_name')
                                ->label(__('translation.first_name'))
                                ->required()
                                ->maxLength(255),
                            TextInput::make('last_name')
                                ->label(__('translation.last_name'))
                                ->required()
                                ->maxLength(255),
                            Select::make('country_code')
                                ->options(Country::pluck('name', 'code_country'))
                                ->required()
                                ->reactive()
                                ->label(__('الدولة')),
                            PhoneInput::make('phone')
                                ->label('رقم الجوال')
                                ->initialCountry('sa')
                                ->required(),
                        ]),
                ])->submitAction(new HtmlString(Blade::render(<<<'BLADE'
                    <x-filament::button
                        type="submit"
                        size="sm"
                        wire:submit="register"
                    >
                        تسجيل
                    </x-filament::button>
                    BLADE))),
            ]);
    }

    /**
     * @param  array<string, mixed>  $data
     */
    protected function handleRegistration(array $data): User
    {
        $coupon = app(SettingsService::class)->getCouponCode();

        $bonusAmount = 10000;

        $usedCouponCode = $data['coupon_code'] ?? null;
        $usedCouponToggle = $data['use_coupon_code'] ?? false;

        unset($data['coupon_code'], $data['use_coupon_code']);

        $user = User::create($data);

        if ($usedCouponToggle && $usedCouponCode === $coupon) {
            $user->increaseWalletBalanceByCorrectValue($bonusAmount);
            $user->save();

            $user->walletTransactions()->create([
                'type' => 'credit',
                'amount' => $bonusAmount,
                'description' => 'رصيد محفظة إضافي لاستخدام رمز الكوبون',
            ]);
        }

        return $user;
    }

    protected function getFormActions(): array
    {
        return [];
    }
}
