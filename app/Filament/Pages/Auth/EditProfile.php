<?php

namespace App\Filament\Pages\Auth;

use App\Models\City;
use Filament\Forms\Components\Fieldset;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Split;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Pages\Auth\EditProfile as BaseEditProfile;

class EditProfile extends BaseEditProfile
{
    public function form(Form $form): Form
    {
        return $form
            ->schema([

                Fieldset::make(__('translation.personal_information'))
                    ->schema([
                        Split::make([
                            Section::make()
                                ->schema([
                                    TextInput::make('first_name')
                                        ->label(__('translation.first_name'))
                                        ->required()
                                        ->maxLength(255),
                                    TextInput::make('last_name')
                                        ->label(__('translation.last_name'))
                                        ->required()
                                        ->maxLength(255),
                                    $this->getPasswordFormComponent(),
                                    $this->getPasswordConfirmationFormComponent(),
                                ]),
                            Section::make()
                                ->schema([
                                    FileUpload::make('avatar')
                                        ->label(__('translation.avatar'))
                                        ->image()
                                        ->disk('public')
                                        ->directory('avatar')
                                        ->maxSize(1024),
                                ])->grow(false)->columns(1),

                        ])->from('md')->columnSpanFull(),
                        Fieldset::make(__('translation.company_information'))
                            ->schema([
                                TextInput::make('company_name')
                                    ->label(__('translation.company_name'))
                                    ->maxLength(255),
                                Select::make('company_city_id')
                                    ->label(__('translation.company_city'))
                                    ->options(function () {
                                        $lang = app()->getLocale();
                                        $columnName = $lang === 'ar' ? 'name_ar' : 'name';

                                        return City::whereNotNull($columnName)
                                            ->pluck($columnName, 'id');
                                    }),
                                TextInput::make('company_postal_code')
                                    ->label(__('translation.postal_code'))
                                    ->maxLength(255),
                                TextInput::make('company_commercial_registration_number')
                                    ->label(__('translation.commercial_registration_number'))
                                    ->maxLength(255),
                                TextInput::make('company_tax_number')
                                    ->label(__('translation.tax_number'))
                                    ->maxLength(255),
                            ]),
                        Fieldset::make(__('translation.bank_information'))
                            ->schema([
                                TextInput::make('bank_name')
                                    ->label(__('translation.bank_name'))
                                    ->maxLength(255),
                                TextInput::make('account_number')
                                    ->label(__('translation.bank_account_number'))
                                    ->maxLength(255),
                                TextInput::make('iban')
                                    ->label(__('translation.iban'))
                                    ->maxLength(255),
                            ]),
                    ]),
            ]);
    }
}
