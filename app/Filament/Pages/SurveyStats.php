<?php

namespace App\Filament\Pages;

use App\Filament\Widgets\EcommerceStoreChart;
use App\Filament\Widgets\IndustryChart;
use App\Filament\Widgets\MonthlyOrdersChart;
use Filament\Pages\Page;
use Illuminate\Contracts\Support\Htmlable;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\HtmlString;

class SurveyStats extends Page
{
    protected static ?string $navigationIcon = 'fcyboqbm.json';

    protected static ?string $navigationGroup = 'home';

    protected static string $view = 'filament.pages.survey-stats';

    public static function canAccess(): bool
    {
        return Auth::check() && Auth::user()->hasRole('super_admin');
    }

    /**
     * @return string[]
     */
    protected function getWidgets(): array
    {
        return [
            EcommerceStoreChart::class,
            IndustryChart::class,
            MonthlyOrdersChart::class,
        ];
    }

    public function getTitle(): string|Htmlable
    {
        return new HtmlString('
        <div class="flex items-center">
            <lord-icon
                src="https://cdn.lordicon.com/fcyboqbm.json"
                trigger="hover"
                stroke="light"
                colors="primary:#6c16c7,secondary:#6c16c7"
                style="width:50px;height:50px">
            </lord-icon>
            <span>'.__('translation.survey_statistics').'</span>
        </div>
    ');
    }

    public static function getNavigationLabel(): string
    {
        return __('translation.survey_statistics');
    }
}
