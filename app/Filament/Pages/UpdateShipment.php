<?php

namespace App\Filament\Pages;

use App\Models\Order;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Filament\Pages\Page;
use Illuminate\Contracts\Support\Htmlable;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\HtmlString;

class UpdateShipment extends Page implements HasForms
{
    use InteractsWithForms;

    protected static ?string $navigationIcon = 'heroicon-o-truck';

    protected static string $view = 'filament.pages.update-shipment';

    protected static ?string $navigationGroup = 'shipments';

    protected static ?int $navigationSort = 5;

    public static function canAccess(): bool
    {
        return Auth::check() && Auth::user()->hasRole('super_admin');
    }

    public ?string $shipmentReference = null;

    public ?string $commandOutput = null;

    public function mount(): void
    {
        // @phpstan-ignore-next-line
        $this->form->fill();
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make(__('translation.update_shipment'))
                    ->description(__('translation.update_shipment_description'))
                    ->schema([
                        TextInput::make('shipmentReference')
                            ->label(__('translation.shipment_reference'))
                            ->required()
                            ->placeholder(__('translation.enter_shipment_reference')),
                    ]),
            ]);
    }

    public function submit(): void
    {
        // @phpstan-ignore-next-line
        $data = $this->form->getState();
        $this->shipmentReference = $data['shipmentReference'];

        // Find the order by shipment reference to get the company
        $order = Order::where('shipment_reference', $this->shipmentReference)->first();

        if (! $order) {
            $this->commandOutput = __('translation.error').': '.__('translation.shipment_not_found');

            Notification::make()
                ->title(__('translation.shipment_not_found'))
                ->body(__('translation.shipment_reference').' '.$this->shipmentReference.' '.__('translation.not_found'))
                ->danger()
                ->send();

            return;
        }

        // Get the shipment company
        $shipmentCompany = $order->shipment_company;

        if (! $shipmentCompany) {
            $this->commandOutput = __('translation.error').': '.__('translation.shipment_company_not_found');

            Notification::make()
                ->title(__('translation.shipment_company_not_found'))
                ->body(__('translation.order').' '.$order->order_number.' '.__('translation.has_no_shipment_company'))
                ->danger()
                ->send();

            return;
        }

        // Map shipment company to command
        $commandMap = [
            'aramex' => 'shipments:update-aramex',
            'barq' => 'shipments:update-barq',
            'transcorp' => 'shipments:update-transcorp',
            'thabit' => 'shipments:update-thabit',
            'jt' => 'shipments:update-jt',
            'spl' => 'shipments:update-spl',
        ];

        if (! isset($commandMap[$shipmentCompany])) {
            $this->commandOutput = __('translation.error').': '.__('translation.unsupported_shipment_company').' '.$shipmentCompany;

            Notification::make()
                ->title(__('translation.unsupported_shipment_company'))
                ->body(__('translation.company').' '.$shipmentCompany.' '.__('translation.is_not_supported'))
                ->danger()
                ->send();

            return;
        }

        $command = $commandMap[$shipmentCompany];

        // Execute the appropriate command based on the company
        $output = '';
        try {
            $output = Artisan::call($command, [
                '--shipment_reference' => $this->shipmentReference,
            ]);

            // Get the output buffer
            $output = Artisan::output();

            // Store the output for display
            $this->commandOutput = $output;

            Notification::make()
                ->title(__('translation.shipment_updated_successfully'))
                ->body(__('translation.company').': '.__('translation.'.$shipmentCompany))
                ->success()
                ->send();
        } catch (\Exception $e) {
            $this->commandOutput = __('translation.error').': '.$e->getMessage();

            Notification::make()
                ->title(__('translation.error_updating_shipment'))
                ->body($e->getMessage())
                ->danger()
                ->send();
        }
    }

    public static function getNavigationLabel(): string
    {
        return __('translation.update_shipment');
    }

    public function getTitle(): string|Htmlable
    {
        return new HtmlString('
        <div class="flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
            </svg>
            <span>'.__('translation.update_shipment').'</span>
        </div>
        ');
    }

    /**
     * @return array<\Filament\Forms\Components\Actions\Action>
     */
    protected function getFormActions(): array
    {
        return [
            \Filament\Forms\Components\Actions\Action::make('submit')
                ->label(__('translation.update_shipment'))
                ->submit('submit'),
        ];
    }
}
