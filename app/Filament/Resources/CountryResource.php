<?php

namespace App\Filament\Resources;

use App\Filament\Resources\CountryResource\Pages;
use App\Models\Country;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;

class CountryResource extends Resource
{
    protected static ?string $model = Country::class;

    protected static ?string $navigationIcon = 'aabwqxsm.json';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('name')
                    ->label(__('translation.name_en'))
                    ->maxLength(191),
                Forms\Components\TextInput::make('name_ar')
                    ->label(__('translation.name_ar'))
                    ->maxLength(191),
                Forms\Components\TextInput::make('name_zid')
                    ->label(__('translation.name_zid'))
                    ->maxLength(191),
                Forms\Components\TextInput::make('code_country')
                    ->label(__('translation.code_country'))
                    ->maxLength(191),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name_ar')
                    ->label(__('translation.name_ar'))
                    ->searchable(),
                Tables\Columns\TextColumn::make('name')
                    ->label(__('translation.name_en'))
                    ->searchable(),
                Tables\Columns\TextColumn::make('name_zid')
                    ->label(__('translation.name_zid'))
                    ->translateLabel()
                    ->searchable(),
                Tables\Columns\TextColumn::make('code_country')
                    ->label(__('translation.code_country'))
                    ->searchable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->label(__('translation.created_at'))
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->label(__('translation.updated_at'))
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->defaultSort('id', 'desc')
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListCountries::route('/'),
            'create' => Pages\CreateCountry::route('/create'),
            'edit' => Pages\EditCountry::route('/{record}/edit'),
        ];
    }

    public static function getNavigationLabel(): string
    {
        //
        return __('translation.countries');

    }

    public static function getLabel(): ?string
    {

        return __('translation.countries');
    }
}
