<?php

namespace App\Filament\Resources\BalanceRequestsResource\Pages;

use App\Filament\Resources\BalanceRequestsResource;
use Filament\Resources\Pages\ListRecords;

class ListBalanceRequests extends ListRecords
{
    protected static string $resource = BalanceRequestsResource::class;

    protected function getHeaderActions(): array
    {
        return [];
    }

    public function getTitle(): string
    {
        return __('translation.balance_recharge_requests');
    }
}
