<?php

namespace App\Filament\Resources\AdminResource\Pages;

use App\Filament\Resources\AdminResource;
use App\Filament\Resources\AdminResource\Widgets\AdminOrdersOverview;
use App\Models\Order;
use App\Models\User;
use Filament\Pages\Concerns\ExposesTableToWidgets;
use Filament\Resources\Pages\ListRecords;
use Filament\Tables;
use Filament\Tables\Table;

class AdminOrders extends ListRecords
{
    use ExposesTableToWidgets;

    protected static string $resource = AdminResource::class;

    public ?int $userId = null;

    public function getTitle(): string
    {
        $user = $this->getUser();
        if (! $user) {
            return __('translation.shipments');
        }

        return __('translation.shipments').' '.$user->first_name.' '.$user->last_name;
    }

    public function getUser(): ?User
    {
        return User::findOrFail($this->getOrderUserId());
    }

    public function getOrderUserId(): ?int
    {
        $record = request()->route('record');

        if (is_numeric($record)) {
            return (int) $record;
        }

        $referer = request()->server('HTTP_REFERER', '');

        if (empty($referer)) {
            return null;
        }

        $url = parse_url($referer);

        if (! is_array($url) || ! isset($url['path'])) {
            return null;
        }

        $segments = explode('/', $url['path']);

        return isset($segments[3]) && is_numeric($segments[3]) ? (int) $segments[3] : null;
    }

    protected function getHeaderWidgets(): array
    {
        return [
            AdminOrdersOverview::class,
        ];
    }

    public function table(Table $table): Table
    {
        return $table
            ->query(fn () => Order::query()->shipmentsByAdmin($this->getUser()))
            ->columns([
                Tables\Columns\TextColumn::make('order_number')
                    ->copyable()
                    ->label(__('translation.order_number'))
                    ->searchable(),
                Tables\Columns\TextColumn::make('status')
                    ->formatStateUsing(fn ($state) => trans("translation.order_status.$state"))
                    ->label(__('translation.status')),
                Tables\Columns\TextColumn::make('updated_at')
                    ->label(__('translation.status_changed_at'))
                    ->dateTime(),
                Tables\Columns\TextColumn::make('shipment_reference')
                    ->copyable()
                    ->searchable()
                    ->label(__('translation.shipment_reference')),
                Tables\Columns\TextColumn::make('payment_method')
                    ->label(__('translation.payment_method')),
                Tables\Columns\TextColumn::make('delivred_at')
                    ->label(__('translation.delivred_at'))
                    ->dateTime(),

            ])
            ->actions([

            ])
            ->bulkActions([
            ])
            ->filters([
            ]);
    }
}
