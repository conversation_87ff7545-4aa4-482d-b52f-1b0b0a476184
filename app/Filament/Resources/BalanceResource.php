<?php

namespace App\Filament\Resources;

use App\Enums\TransactionPaymentMethodEnum;
use App\Enums\WalletTransactionTypeEnum;
use App\Filament\Resources\BalanceResource\Pages;
use App\Models\User;
use App\Models\WalletTransaction;
use Barryvdh\Snappy\Facades\SnappyPdf;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Actions\Action;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Table;
use Illuminate\Contracts\View\View;
use Illuminate\Database\Eloquent\Builder;
use SimpleSoftwareIO\QrCode\Facades\QrCode;
use Storage;
use Symfony\Component\HttpFoundation\StreamedResponse;

class BalanceResource extends Resource
{
    protected static ?string $model = WalletTransaction::class;

    protected static bool $shouldRegisterNavigation = false;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->modifyQueryUsing(function (Builder $query): Builder {
                return $query->where('type', WalletTransactionTypeEnum::CREDIT->value);
            })
            ->columns([
                Tables\Columns\TextColumn::make('user.full_name')
                    ->label(__('translation.user'))
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('type')
                    ->label(__('translation.type'))
                    ->formatStateUsing(fn ($state) => trans("translation.$state")),
                Tables\Columns\TextColumn::make('amount')
                    ->label(__('translation.amount'))
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('description')
                    ->label(__('translation.description')),
                Tables\Columns\TextColumn::make('transaction.payment_method')
                    ->label(__('translation.payment_method')),
                Tables\Columns\TextColumn::make('transaction.status')
                    ->label(__('translation.status')),
                Tables\Columns\TextColumn::make('created_at')
                    ->label(__('translation.created_at'))
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->label(__('translation.updated_at'))
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->defaultSort('id', 'desc')
            ->filters([
                SelectFilter::make('user_id')
                    ->label(__('translation.user'))
                    ->options(User::all()->mapWithKeys(fn ($user) => [
                        $user->id => $user->first_name.' '.$user->last_name,
                    ])),
            ])
            ->actions([

                Action::make('view_image')
                    ->label(__('translation.view_proof'))
                    ->modalContent(fn (WalletTransaction $record): View => view(
                        'filament.resources.transaction-resource.modals.view-image',
                        ['record' => $record->transaction]
                    ))
                    ->visible(fn (WalletTransaction $record): bool => $record->transaction?->payment_method === TransactionPaymentMethodEnum::BANK->value)
                    ->modalSubmitAction(false)
                    ->modalCancelAction(false),
                Tables\Actions\Action::make('download_proof')
                    ->label(__('translation.download_proof'))
                    ->icon('heroicon-o-document-arrow-down')
                    ->color('primary')
                    ->action(fn (WalletTransaction $record) => Storage::disk('public')->download(
                        $record->transaction?->transfer_proof,
                        'proof.'.pathinfo($record->transaction?->transfer_proof, PATHINFO_EXTENSION)
                    ))
                    ->visible(fn (WalletTransaction $record): bool => $record->transaction?->payment_method === TransactionPaymentMethodEnum::BANK->value &&
                        $record->transaction->transfer_proof !== null),
                Tables\Actions\Action::make('print_invoice')
                    ->label(__('translation.print'))
                    ->icon('heroicon-o-printer')
                    ->visible(fn ($record) => $record->type === WalletTransactionTypeEnum::CREDIT->value)
                    ->action(fn ($record) => self::generateInvoice($record)),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                ]),
            ]);
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ManageBalances::route('/'),
        ];
    }

    public static function generateInvoice(WalletTransaction $record): StreamedResponse
    {
        // توليد بيانات QR
        $qrData = self::generateZatcaQrBase64(
            'شركة مسار تريك لتقنية المعلومات',                 // 1. اسم البائع
            '311250710800003',            // 2. الرقم الضريبي
            now()->toIso8601String(),     // 3. التاريخ
            number_format($record->amount, 2, '.', ''), // 4. الإجمالي
            number_format(($record->amount * 15) / 100, 2, '.', '') // 5. الضريبة
        );
        if (empty($qrData)) {
            $jsonMessage = json_encode([
                'error' => 'Invalid QR content',
                'qr_content' => $qrData,
                'user_id' => auth()->id(),
            ], JSON_UNESCAPED_UNICODE);
            if ($jsonMessage === false) {
                throw new \Exception('JSON encode failed: '.json_last_error_msg());
            }
            throw new \Exception($jsonMessage);
        }

        $qrCode = QrCode::format('png')->size(200)->generate($qrData);

        // @phpstan-ignore-next-line
        $qrImage = base64_encode($qrCode);

        // تمرير البيانات إلى الـ View
        $pdf = SnappyPdf::loadView('invoices.cod_wallet', [
            'wallet' => $record,
            'qrImage' => $qrImage,
        ]);

        return response()->streamDownload(
            fn () => print ($pdf->output()),
            "invoice_cod_wallet_{$record->id}.pdf"
        );
    }

    private static function generateZatcaQrBase64(string $sellerName, string $vatNumber, string $timestamp, string $invoiceTotal, string $vatTotal): string
    {
        $elements = [
            1 => $sellerName,
            2 => $vatNumber,
            3 => $timestamp,
            4 => $invoiceTotal,
            5 => $vatTotal,
        ];

        $tlv = '';

        foreach ($elements as $tag => $value) {
            $length = strlen($value);
            $tlv .= chr($tag).chr($length).$value;
        }

        return base64_encode($tlv);
    }
}
