<?php

namespace App\Filament\Resources\WebhookResource\Pages;

use App\Filament\Resources\WebhookResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use Illuminate\Contracts\Support\Htmlable;
use Illuminate\Support\HtmlString;

class ListWebhooks extends ListRecords
{
    protected static string $resource = WebhookResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }

    public function getTitle(): string|Htmlable
    {
        return new HtmlString('
        <div class="flex items-center">


<lord-icon
    src="https://cdn.lordicon.com/bnxnryzv.json"
    trigger="hover"
    colors="primary:#6c16c7,secondary:#000000"
    style="width:50px;height:50px">
</lord-icon>
   <span>'.__('translation.webhooks').'</span>
</div>
    ');
    }
}
