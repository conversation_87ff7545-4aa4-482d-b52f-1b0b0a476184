<?php

namespace App\Filament\Resources\ShipmentCountriesPriceResource\Pages;

use App\Filament\Resources\ShipmentCountriesPriceResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use Illuminate\Contracts\Support\Htmlable;
use Illuminate\Support\HtmlString;

class ListShipmentCountriesPrices extends ListRecords
{
    protected static string $resource = ShipmentCountriesPriceResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }

    public function getTitle(): string|Htmlable
    {
        return new HtmlString('
        <div class="flex items-center">

<script src="https://cdn.lordicon.com/lordicon.js"></script>
<lord-icon
    src="https://cdn.lordicon.com/ysqeagpz.json"
    trigger="hover"
    style="width:50px;height:50px">
</lord-icon>
        <span>'.__('translation.shipment_countries_prices').'</span>
</div>
    ');
    }
}
