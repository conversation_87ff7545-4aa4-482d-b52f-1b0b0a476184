<?php

namespace App\Filament\Resources\CourierStatusResource\Pages;

use App\Filament\Resources\CourierStatusResource;
use App\Models\CourierStatus;
use Filament\Actions;
use Filament\Resources\Components\Tab;
use Filament\Resources\Pages\ListRecords;
use Illuminate\Database\Eloquent\Builder;

class ListCourierStatuses extends ListRecords
{
    protected static string $resource = CourierStatusResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }

    protected function getHeaderWidgets(): array
    {
        return [
        ];
    }

    public function getTabs(): array
    {
        return [
            'all' => Tab::make(__('translation.all'))
                ->badge(CourierStatus::count()),
            'aramex' => Tab::make(__('translation.aramex'))
                ->modifyQueryUsing(fn (Builder $query) => $query->where('courier', 'aramex'))
                ->badge(CourierStatus::where('courier', 'aramex')->count()),
            'jt' => Tab::make(__('translation.jt_express'))
                ->modifyQueryUsing(fn (Builder $query) => $query->where('courier', 'jt'))
                ->badge(CourierStatus::where('courier', 'jt')->count()),
            'thabit' => Tab::make(__('translation.thabit'))
                ->modifyQueryUsing(fn (Builder $query) => $query->where('courier', 'thabit'))
                ->badge(CourierStatus::where('courier', 'thabit')->count()),
            'barq' => Tab::make(__('translation.barq'))
                ->modifyQueryUsing(fn (Builder $query) => $query->where('courier', 'barq'))
                ->badge(CourierStatus::where('courier', 'barq')->count()),
            'transcorp' => Tab::make(__('translation.transcorp'))
                ->modifyQueryUsing(fn (Builder $query) => $query->where('courier', 'transcorp'))
                ->badge(CourierStatus::where('courier', 'transcorp')->count()),
            'spl' => Tab::make(__('translation.spl'))
                ->modifyQueryUsing(fn (Builder $query) => $query->where('courier', 'spl'))
                ->badge(CourierStatus::where('courier', 'spl')->count()),
        ];
    }
}
