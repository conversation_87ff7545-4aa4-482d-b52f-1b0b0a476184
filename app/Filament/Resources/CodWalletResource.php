<?php

namespace App\Filament\Resources;

use App\Enums\CodWalletStatusEnum;
use App\Enums\CodWalletTypeEnum;
use App\Filament\Resources\CodWalletResource\Pages;
use App\Models\CodWallet;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Table;
use Illuminate\Contracts\Database\Eloquent\Builder;
use Webbingbrasil\FilamentAdvancedFilter\Filters\DateFilter;
use Webbingbrasil\FilamentAdvancedFilter\Filters\TextFilter;

class CodWalletResource extends Resource
{
    protected static ?string $model = CodWallet::class;

    protected static ?string $navigationIcon = 'alwzvvks.json';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Select::make('status')
                    ->label(__('translation.status'))
                    ->options([
                        CodWalletStatusEnum::INPROGRESS->value => __('translation.'.CodWalletStatusEnum::INPROGRESS->value),
                        CodWalletStatusEnum::PAID->value => __('translation.'.CodWalletStatusEnum::PAID->value),
                    ])
                    ->required()
                    ->afterStateUpdated(fn ($state, $set) => ($state === CodWalletStatusEnum::PAID->value) ? $set('paid_at', now()) : $set('paid_at', null)),
                Forms\Components\Hidden::make('paid_at'),
                Forms\Components\FileUpload::make('bank_invoice')
                    ->label(__('translation.upload_image'))
                    ->acceptedFileTypes(['image/*', 'application/pdf'])
                    ->directory('cod_wallet_images')
                    ->disk('public')
                    ->visibility('private')
                    ->maxSize(1024),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table->modifyQueryUsing(fn (Builder $query) => $query->where('type', 'bank'))
            ->columns([
                Tables\Columns\TextColumn::make('user.full_name')->searchable(isIndividual: true)->label(__('translation.name'))->formatStateUsing(fn ($record) => "{$record->user->full_name} ({$record->user->id})")->sortable(),
                Tables\Columns\TextColumn::make('user.bank_name')->searchable(isIndividual: true)->label(__('translation.bank_name'))->sortable(),
                Tables\Columns\TextColumn::make('user.account_number')->searchable(isIndividual: true)->label(__('translation.account_number'))->sortable(),
                Tables\Columns\TextColumn::make('user.iban')->searchable(isIndividual: true)->label(__('translation.iban'))->sortable(),
                Tables\Columns\TextColumn::make('amount')->searchable(isIndividual: true)->label(__('translation.amount'))->numeric()->sortable()->formatStateUsing(fn ($state) => number_format($state / 100, 2)),
                Tables\Columns\TextColumn::make('status')->searchable(isIndividual: true)->label(__('translation.status'))
                    ->formatStateUsing(fn ($state) => trans('translation.'.$state))
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        CodWalletStatusEnum::INPROGRESS->value => 'warning',
                        CodWalletStatusEnum::PAID->value => 'success',
                        CodWalletStatusEnum::ACCEPTED->value => 'success',
                        default => 'secondary'
                    }),
                Tables\Columns\TextColumn::make('paid_at')->label(__('translation.paid_at'))->dateTime()->sortable()->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('created_at')->label(__('translation.created_at'))->dateTime()->sortable()->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')->label(__('translation.updated_at'))->dateTime()->sortable()->toggleable(isToggledHiddenByDefault: true),
            ])
            ->defaultSort('id', 'desc')
            ->filters([
                SelectFilter::make('status')
                    ->label(__('translation.status'))
                    ->options([
                        CodWalletStatusEnum::INPROGRESS->value => __('translation.'.CodWalletStatusEnum::INPROGRESS->value),
                        CodWalletStatusEnum::PAID->value => __('translation.'.CodWalletStatusEnum::PAID->value),
                        CodWalletStatusEnum::ACCEPTED->value => __('translation.'.CodWalletStatusEnum::ACCEPTED->value),
                    ])
                    ->searchable()
                    ->preload(),
                SelectFilter::make('type')
                    ->label(__('translation.type'))
                    ->options([
                        CodWalletTypeEnum::COD->value => __('translation.'.CodWalletTypeEnum::COD->value),
                        CodWalletTypeEnum::BANK->value => __('translation.'.CodWalletTypeEnum::BANK->value),
                        CodWalletTypeEnum::WALLET->value => __('translation.'.CodWalletTypeEnum::WALLET->value),
                    ])
                    ->searchable()
                    ->preload(),
                TextFilter::make('amount')->translateLabel()->label('translation.amount'),
                DateFilter::make('created_at')->translateLabel()->label('translation.created_at'),
                DateFilter::make('paid_at')->translateLabel()->label('translation.paid_at'),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListCodWallets::route('/'),
            'view' => Pages\ViewCodWallet::route('/{record}'),
            'edit' => Pages\EditCodWallet::route('/{record}/edit'),
        ];
    }

    public static function getNavigationLabel(): string
    {
        //
        return __('translation.cod_wallet');

    }

    public static function getLabel(): ?string
    {

        return __('translation.cod_wallet');
    }
}
