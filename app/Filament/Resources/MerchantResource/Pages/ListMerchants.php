<?php

namespace App\Filament\Resources\MerchantResource\Pages;

use App\Filament\Resources\MerchantResource;
use Filament\Resources\Pages\ListRecords;
use Illuminate\Contracts\Support\Htmlable;
use Illuminate\Support\HtmlString;

class ListMerchants extends ListRecords
{
    protected static string $resource = MerchantResource::class;

    protected function getHeaderActions(): array
    {
        return [
        ];
    }

    public function getTitle(): string|Htmlable
    {
        return new HtmlString('
        <div class="flex items-center">

<lord-icon
    src="https://cdn.lordicon.com/eyvkykfo.json"
    trigger="hover"
    colors="primary:#242424,secondary:#8930e8"
    style="width:50px;height:50px">
</lord-icon>
        <span>'.__('translation.Sales_Channels').'</span>
</div>
    ');
    }
}
