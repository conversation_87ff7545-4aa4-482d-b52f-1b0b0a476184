<?php

namespace App\Filament\Resources;

use App\Enums\OrderStatusEnum;
use App\Filament\Resources\CourierStatusResource\Pages;
use App\Models\CourierStatus;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;

class CourierStatusResource extends Resource
{
    protected static ?string $model = CourierStatus::class;

    protected static ?string $navigationIcon = 'heroicon-o-truck';

    protected static ?string $navigationGroup = 'Courier Management';

    public static function getNavigationGroup(): ?string
    {
        return __('translation.courier_status_management');
    }

    protected static ?string $label = 'Courier Status Mapping';

    protected static ?string $pluralLabel = 'Courier Status Mappings';

    public static function getLabel(): ?string
    {
        return __('translation.courier_status_mapping');
    }

    public static function getPluralLabel(): ?string
    {
        return __('translation.courier_status_mappings');
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Select::make('courier')
                    ->label(__('translation.courier'))
                    ->options([
                        'aramex' => __('translation.aramex'),
                        'jt' => __('translation.jt_express'),
                        'thabit' => __('translation.thabit'),
                        'barq' => __('translation.barq'),
                        'transcorp' => __('translation.transcorp'),
                        'spl' => __('translation.spl'),
                    ])
                    ->required()
                    ->searchable(),

                Forms\Components\TextInput::make('code')
                    ->label(__('translation.status_code'))
                    ->required()
                    ->maxLength(255)
                    ->helperText(__('translation.status_code_helper')),

                Forms\Components\TextInput::make('description')
                    ->label(__('translation.description'))
                    ->maxLength(255)
                    ->helperText(__('translation.description_helper')),

                Forms\Components\Select::make('order_status')
                    ->label(__('translation.order_status_label'))
                    ->options([
                        OrderStatusEnum::PENDING->value => __('translation.order_status.pending'),
                        OrderStatusEnum::AWAITING_PICKUP->value => __('translation.order_status.awaiting_pickup'),
                        OrderStatusEnum::CURRENTLY_SHIPPING->value => __('translation.order_status.currently_shipping'),
                        OrderStatusEnum::DELIVERED->value => __('translation.order_status.delivered'),
                        OrderStatusEnum::RETURNED->value => __('translation.order_status.returned'),
                        OrderStatusEnum::SHIPMENT_ON_HOLD->value => __('translation.order_status.shipment_on_hold'),
                        OrderStatusEnum::CANCELED->value => __('translation.order_status.canceled'),
                        OrderStatusEnum::FAILED->value => __('translation.order_status.failed'),
                        OrderStatusEnum::NOT_CHANGED->value => __('translation.order_status.not_changed'),
                    ])
                    ->required()
                    ->searchable()
                    ->helperText(__('translation.order_status_helper')),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('courier')
                    ->label(__('translation.courier'))
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'aramex' => 'warning',
                        'jt' => 'success',
                        'thabit' => 'info',
                        'barq' => 'danger',
                        'transcorp' => 'primary',
                        'spl' => 'secondary',
                        default => 'gray',
                    })
                    ->formatStateUsing(fn (string $state): string => match ($state) {
                        'aramex' => __('translation.aramex'),
                        'jt' => __('translation.jt_express'),
                        'thabit' => __('translation.thabit'),
                        'barq' => __('translation.barq'),
                        'transcorp' => __('translation.transcorp'),
                        'spl' => __('translation.spl'),
                        default => $state,
                    })
                    ->sortable()
                    ->searchable(),

                Tables\Columns\TextColumn::make('code')
                    ->label(__('translation.status_code'))
                    ->sortable()
                    ->searchable(),

                Tables\Columns\TextColumn::make('description')
                    ->label(__('translation.description'))
                    ->searchable()
                    ->limit(50),

                Tables\Columns\TextColumn::make('order_status')
                    ->label(__('translation.order_status_label'))
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'pending' => 'gray',
                        'awaiting_pickup' => 'warning',
                        'currently_shipping' => 'info',
                        'delivered' => 'success',
                        'returned' => 'danger',
                        'shipment_on_hold' => 'warning',
                        'canceled' => 'danger',
                        'failed' => 'danger',
                        'not_changed' => 'gray',
                        default => 'gray',
                    })
                    ->formatStateUsing(fn (string $state): string => __("translation.order_status.{$state}"))
                    ->sortable()
                    ->searchable(),

                Tables\Columns\TextColumn::make('created_at')
                    ->label(__('translation.created'))
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\TextColumn::make('updated_at')
                    ->label(__('translation.updated'))
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('courier')
                    ->label(__('translation.courier'))
                    ->options([
                        'aramex' => __('translation.aramex'),
                        'jt' => __('translation.jt_express'),
                        'thabit' => __('translation.thabit'),
                        'barq' => __('translation.barq'),
                        'transcorp' => __('translation.transcorp'),
                        'spl' => __('translation.spl'),
                    ]),

                Tables\Filters\SelectFilter::make('order_status')
                    ->label(__('translation.order_status_label'))
                    ->options([
                        OrderStatusEnum::PENDING->value => __('translation.order_status.pending'),
                        OrderStatusEnum::AWAITING_PICKUP->value => __('translation.order_status.awaiting_pickup'),
                        OrderStatusEnum::CURRENTLY_SHIPPING->value => __('translation.order_status.currently_shipping'),
                        OrderStatusEnum::DELIVERED->value => __('translation.order_status.delivered'),
                        OrderStatusEnum::RETURNED->value => __('translation.order_status.returned'),
                        OrderStatusEnum::SHIPMENT_ON_HOLD->value => __('translation.order_status.shipment_on_hold'),
                        OrderStatusEnum::CANCELED->value => __('translation.order_status.canceled'),
                        OrderStatusEnum::FAILED->value => __('translation.order_status.failed'),
                        OrderStatusEnum::NOT_CHANGED->value => __('translation.order_status.not_changed'),
                    ]),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('courier')
            ->searchable();
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListCourierStatuses::route('/'),
            'create' => Pages\CreateCourierStatus::route('/create'),
            'edit' => Pages\EditCourierStatus::route('/{record}/edit'),
        ];
    }

    public static function getNavigationBadge(): ?string
    {
        return static::getModel()::count();
    }
}
