<?php

namespace App\Filament\Resources\SurveyResponseResource\Pages;

use App\Filament\Resources\SurveyResponseResource;
use Filament\Resources\Pages\ListRecords;
use Illuminate\Contracts\Support\Htmlable;
use Illuminate\Support\HtmlString;

class ListSurveyResponses extends ListRecords
{
    protected static string $resource = SurveyResponseResource::class;

    protected function getHeaderActions(): array
    {
        return [
        ];
    }

    public function getTitle(): string|Htmlable
    {
        return new HtmlString('
        <div class="flex items-center">

<lord-icon
    src="https://cdn.lordicon.com/vtmfbfkv.json"
    trigger="hover"
    colors="primary:#242424,secondary:#8930e8"
    style="width:50px;height:50px">
</lord-icon>
   <span>'.__('translation.shipment_courier_services').'</span>
</div>
    ');
    }
}
