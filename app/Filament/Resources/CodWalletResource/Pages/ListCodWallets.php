<?php

namespace App\Filament\Resources\CodWalletResource\Pages;

use App\Filament\Resources\CodWalletResource;
use Filament\Resources\Pages\ListRecords;
use Illuminate\Contracts\Support\Htmlable;
use Illuminate\Support\HtmlString;

class ListCodWallets extends ListRecords
{
    protected static string $resource = CodWalletResource::class;

    protected function getHeaderActions(): array
    {
        return [
        ];
    }

    public function getTitle(): string|Htmlable
    {
        return new HtmlString('
        <div class="flex items-center">

<lord-icon
    src="https://cdn.lordicon.com/alwzvvks.json"
    trigger="hover"
    colors="primary:#242424,secondary:#8930e8"
    style="width:50px;height:50px">
</lord-icon>
        <span>'.__('translation.cod_wallet').'</span>
</div>
    ');
    }
}
