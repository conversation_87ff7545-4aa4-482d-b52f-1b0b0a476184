<?php

namespace App\Filament\Resources;

use App\Filament\Resources\WebhookResource\Pages;
use App\Models\Webhook;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Http;
use ValentinMorice\FilamentJsonColumn\JsonColumn;

function executeWebhook(Webhook $record): void
{
    try {
        // Send a POST request to the webhook's URL with its payload
        $response = Http::post($record->url, $record->payload);
        if ($response->successful()) {
            // Update the status if the request is successful
            $record->update(['status' => 'success', 'error_message' => null]);
            \Filament\Notifications\Notification::make()
                ->title(__('translation.webhook_reexecuted_successfully'))
                ->success()
                ->send();
        } else {

            // Handle non-successful responses
            $record->update(['status' => 'failed', 'error_message' => $response->body()]);
            \Filament\Notifications\Notification::make()
                ->title(__('translation.failed_to_re_execute_webhook'))
                ->body('Error: '.$response->body())
                ->danger()
                ->send();
        }
    } catch (\Exception $e) {
        // Handle exceptions
        $record->update(['status' => 'failed', 'error_message' => $e->getMessage()]);
        \Filament\Notifications\Notification::make()
            ->title(__('translation.error_re_executing_webhook'))
            ->body('Error: '.$e->getMessage())
            ->danger()
            ->send();
    }
}
class WebhookResource extends Resource
{
    protected static ?string $model = Webhook::class;

    protected static ?string $navigationIcon = 'bnxnryzv.json';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                JsonColumn::make('payload')
                    ->columnSpanFull(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->modifyQueryUsing(fn (Builder $query) => $query->orderBy('created_at', 'desc')->where('status', '=', 'pending')->orwhere('status', '=', 'failed'))
            ->columns([
                Tables\Columns\TextColumn::make('id')
                    ->label(__('translation.id')),
                Tables\Columns\TextColumn::make('url')
                    ->label(__('translation.url')),
                Tables\Columns\TextColumn::make('status')->badge()
                    ->label(__('translation.status')),
                Tables\Columns\TextColumn::make('error_message1'),
                Tables\Columns\TextColumn::make('created_at')
                    ->label(__('translation.created_at')),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                // Keep your single-row 'reexecuteWebhook' action here if you like.
            ])
            // -------------------------------------------
            // 2. Define a Bulk Action for re-executing
            // -------------------------------------------
            ->bulkActions([
                Tables\Actions\BulkAction::make('bulkExecute')
                    ->label('Execute All')
                    ->icon('heroicon-o-play')
                    ->action(function (Collection $records) {
                        foreach ($records as $recordId) {
                            if ($recordId->status !== 'success') {
                                executeWebhook($recordId);
                            }
                        }

                        return redirect()->route('filament.admin.resources.webhooks.index')
                            ->with('success', 'Webhooks executed successfully.');
                    }),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListWebhooks::route('/'),
        ];
    }

    public static function canCreate(): bool
    {
        return false;
    }

    public static function getNavigationLabel(): string
    {
        //
        return __('translation.webhooks');

    }

    public static function getLabel(): ?string
    {

        return __('translation.webhooks');
    }
}
