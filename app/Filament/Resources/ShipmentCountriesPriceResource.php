<?php

namespace App\Filament\Resources;

use App\Filament\Resources\ShipmentCountriesPriceResource\Pages;
use App\Models\ShipmentCountriesPrice;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;

class ShipmentCountriesPriceResource extends Resource
{
    protected static ?string $model = ShipmentCountriesPrice::class;

    protected static ?string $navigationIcon = 'ysqeagpz.json';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Select::make('country_id')
                    ->relationship('country', 'name')
                    ->required(),
                Forms\Components\TextInput::make('initial_price')
                    ->required()
                    ->numeric(),
                Forms\Components\TextInput::make('extra_weight_price')
                    ->numeric(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('country.translatedName')
                    ->label(__('translation.country'))
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('initial_price')
                    ->label(__('translation.initial_price'))
                    ->formatStateUsing(fn ($state) => number_format($state / 100, 2))
                    ->sortable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->label(__('translation.created_at'))
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->label(__('translation.updated_at'))
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('extra_weight_price')
                    ->label(__('translation.extra_weight_price'))
                    ->formatStateUsing(fn ($state) => number_format($state / 100, 2))
                    ->sortable(),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListShipmentCountriesPrices::route('/'),
        ];
    }

    public static function getNavigationLabel(): string
    {
        //
        return __('translation.shipment_countries_prices');

    }

    public static function getLabel(): ?string
    {

        return __('translation.shipment_countries_prices');
    }
}
