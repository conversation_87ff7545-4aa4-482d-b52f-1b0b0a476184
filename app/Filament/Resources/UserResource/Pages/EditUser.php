<?php

namespace App\Filament\Resources\UserResource\Pages;

use App\Filament\Resources\UserResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditUser extends EditRecord
{
    protected static string $resource = UserResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\Action::make('orders')
                ->label(__('translation.Orders'))
                ->icon('heroicon-o-document-text')
                ->url(fn (): string => route('filament.admin.resources.orders.index', [
                    'tableFilters' => [
                        'user_related' => ['value' => (int) ($this->record->id ?? 0)],
                    ],
                ]))
                ->openUrlInNewTab(),
        ];
    }
}
