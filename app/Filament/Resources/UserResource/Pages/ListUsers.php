<?php

namespace App\Filament\Resources\UserResource\Pages;

use App\Filament\Resources\UserResource;
use Filament\Resources\Pages\ListRecords;
use Illuminate\Contracts\Support\Htmlable;
use Illuminate\Support\HtmlString;

class ListUsers extends ListRecords
{
    protected static string $resource = UserResource::class;

    protected function getHeaderActions(): array
    {
        return [
        ];
    }

    public function getTitle(): string|Htmlable
    {
        return new HtmlString('
        <div class="flex items-center">


<lord-icon
    src="https://cdn.lordicon.com/rigpimbi.json"
    trigger="hover"
    colors="primary:#6c16c7,secondary:#000000"
    style="width:50px;height:50px">
</lord-icon>
   <span>'.__('translation.users').'</span>
</div>
    ');
    }
}
