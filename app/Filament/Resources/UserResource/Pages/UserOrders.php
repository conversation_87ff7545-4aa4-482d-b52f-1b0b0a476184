<?php

namespace App\Filament\Resources\UserResource\Pages;

use App\Enums\CourierIdentifierEnum;
use App\Enums\OrderStatusEnum;
use App\Filament\Exports\OrderExporter;
use App\Filament\Resources\UserResource;
use App\Filament\Resources\UserResource\Widgets\OrdersOverview;
use App\Models\Order;
use App\Models\User;
use Filament\Pages\Concerns\ExposesTableToWidgets;
use Filament\Resources\Pages\ListRecords;
use Filament\Tables;
use Filament\Tables\Actions\ExportBulkAction;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Table;
use Webbingbrasil\FilamentAdvancedFilter\Filters\DateFilter;

class UserOrders extends ListRecords
{
    use ExposesTableToWidgets;

    protected static string $resource = UserResource::class;

    public ?int $userId = null;

    public function getTitle(): string
    {
        return __('translation.shipments').' '.$this->getUser()->first_name.' '.$this->getUser()->last_name;
    }

    public function getUser(): ?User
    {
        return User::findOrFail($this->getOrderUserId());
    }

    public function getOrderUserId(): ?int
    {
        $record = request()->route('record');

        if (is_numeric($record)) {
            return (int) $record;
        }

        $referer = request()->server('HTTP_REFERER', '');

        if (empty($referer)) {
            return null;
        }

        $url = parse_url($referer);

        if (! is_array($url) || ! isset($url['path'])) {
            return null;
        }

        $segments = explode('/', $url['path']);

        return isset($segments[3]) && is_numeric($segments[3]) ? (int) $segments[3] : null;
    }

    protected function getHeaderWidgets(): array
    {
        return [
            OrdersOverview::class,
        ];
    }

    public function table(Table $table): Table
    {
        return $table
            ->query(Order::shipmentsByUser($this->getOrderUserId()))
            ->columns([
                Tables\Columns\TextColumn::make('order_number')
                    ->label(__('translation.order_number'))
                    ->searchable(),
                Tables\Columns\TextColumn::make('status')
                    ->formatStateUsing(fn ($state) => trans("translation.order_status.$state"))
                    ->label(__('translation.status')),
                Tables\Columns\TextColumn::make('updated_at')
                    ->label(__('translation.status_changed_at'))
                    ->dateTime(),
                Tables\Columns\TextColumn::make('shipment_reference')
                    ->label(__('translation.shipment_reference')),
                Tables\Columns\TextColumn::make('shipment_company')
                    ->label(__('translation.shipment_company')),
                Tables\Columns\TextColumn::make('shipment_approved_cost')
                    ->label(__('translation.shipment_cost'))
                    ->formatStateUsing(fn ($state) => number_format($state / 100, 2)),
                Tables\Columns\TextColumn::make('order_grand_total')
                    ->label(__('translation.order_grand_total'))
                    ->formatStateUsing(fn ($state) => number_format($state, 2))
                    ->sortable(),
                Tables\Columns\TextColumn::make('payment_method')
                    ->label(__('translation.payment_method'))
                    ->searchable(isIndividual: true),

            ])
            ->paginated([10, 25, 50])
            ->recordUrl(null)
            ->actions([

            ])
            ->bulkActions([
                ExportBulkAction::make()
                    ->label(__('translation.export_shipments'))
                    ->exporter(OrderExporter::class),
            ])
            ->filters([
                DateFilter::make('updated_at')->translateLabel()->label('translation.updated_at'),
                SelectFilter::make('shipment_company')
                    ->label(__('translation.shipment_company'))
                    ->options(
                        collect(CourierIdentifierEnum::cases())->mapWithKeys(fn ($case) => [
                            $case->value => __("translation.{$case->value}"),
                        ])->toArray()
                    ),
                SelectFilter::make('status')
                    ->label(__('translation.status'))
                    ->options(
                        collect(OrderStatusEnum::cases())->mapWithKeys(fn ($case) => [
                            $case->value => __("translation.order_status.{$case->value}"),
                        ])->toArray()
                    )
                    ->multiple(),
                SelectFilter::make('shipment_credentials_type')
                    ->label(__('translation.shipment_credentials_type'))
                    ->options([
                        'application' => __('translation.application_pricing'),
                        'user' => __('translation.user_pricing'),
                    ]),
            ]);
    }
}
