<?php

namespace App\Filament\Resources\UserResource\Widgets;

use App\Filament\Resources\UserResource\Pages\UserOrders;
use Filament\Widgets\Concerns\InteractsWithPageTable;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;

class OrdersOverview extends BaseWidget
{
    use InteractsWithPageTable;

    protected function getTablePage(): string
    {
        return UserOrders::class;
    }

    protected function getCards(): array
    {
        return [
            Stat::make(__('translation.shipments_numbers'), $this->getPageTableQuery()->count()),
            Stat::make(__('translation.shipments_costs'),
                number_format(
                    (float) $this->getPageTableQuery()
                        ->get()
                        ->sum(fn ($order): float => (float) ($order->shipment_approved_cost ?? 0)) / 100, 2
                )
            ),

            Stat::make(__('translation.shipments_order_grand_total'), number_format((float) $this->getPageTableQuery()->sum('order_grand_total') / 100, 2)),
        ];
    }
}
