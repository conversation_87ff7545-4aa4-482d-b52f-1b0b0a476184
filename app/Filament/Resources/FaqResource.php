<?php

namespace App\Filament\Resources;

use App\Filament\Resources\FaqResource\Pages;
use App\Models\Faq;
use Filament\Forms;
use Filament\Forms\Components\Select;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;

class FaqResource extends Resource
{
    protected static ?string $model = Faq::class;

    protected static ?string $navigationIcon = 'znkzapxw.json';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Select::make('section')
                    ->translateLabel()
                    ->label('translation.faq_section')
                    ->required()
                    ->columnSpanFull()
                    ->options([
                        'services' => __('translation.faq_services'),
                        'shipment' => __('translation.faq_shipment'),
                        'link' => __('translation.faq_link'),
                    ]),
                Forms\Components\TextInput::make('question')
                    ->translateLabel()
                    ->label('translation.question')
                    ->required()
                    ->columnSpanFull(),
                Forms\Components\Textarea::make('answer')
                    ->translateLabel()
                    ->label('translation.answer')
                    ->required()
                    ->rows(8)
                    ->columnSpanFull(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('question')
                    ->translateLabel()
                    ->label('translation.question')
                    ->sortable(),
                Tables\Columns\TextColumn::make('answer')
                    ->translateLabel()
                    ->label('translation.answer')
                    ->sortable(),
                //                Tables\Columns\TextColumn::make('created_at')
                //                    ->dateTime()
                //                    ->sortable()
                //                    ->toggleable(isToggledHiddenByDefault: true),
                //                Tables\Columns\TextColumn::make('updated_at')
                //                    ->dateTime()
                //                    ->sortable()
                //                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ManageFaqs::route('/'),
        ];
    }

    public static function getNavigationLabel(): string
    {
        //
        return __('translation.faqs');

    }

    public static function getLabel(): ?string
    {

        return __('translation.faqs');
    }
}
