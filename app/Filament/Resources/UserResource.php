<?php

namespace App\Filament\Resources;

use App\Filament\Resources\UserResource\Pages;
use App\Models\User;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Rmsramos\Activitylog\RelationManagers\ActivitylogRelationManager;
use Spatie\Permission\Models\Role;
use STS\FilamentImpersonate\Tables\Actions\Impersonate;

class UserResource extends Resource
{
    protected static ?string $model = User::class;

    protected static ?string $navigationIcon = 'rigpimbi.json';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('first_name')
                    ->label(__('translation.first_name'))
                    ->required()
                    ->maxLength(191),
                Forms\Components\TextInput::make('last_name')
                    ->label(__('translation.last_name'))
                    ->required()
                    ->maxLength(191),
                Forms\Components\TextInput::make('email')
                    ->label(__('translation.email'))
                    ->email()
                    ->required()
                    ->maxLength(191),
                Forms\Components\TextInput::make('role')
                    ->label(__('translation.role'))
                    ->required(),
                Forms\Components\DateTimePicker::make('email_verified_at')
                    ->label(__('translation.email_verified_at')),

                Forms\Components\DateTimePicker::make('phone_verified_at')
                    ->label(__('translation.phone_verified_at')),
                Forms\Components\DatePicker::make('dob')
                    ->label(__('translation.dob'))
                    ->required(),
                Forms\Components\Textarea::make('avatar')
                    ->label(__('translation.avatar'))
                    ->columnSpanFull(),
                Forms\Components\TextInput::make('phone')
                    ->label(__('translation.phone'))
                    ->tel()
                    ->maxLength(191),
                Forms\Components\Select::make('roles')
                    ->relationship('roles', 'name')
                    ->options(
                        Role::query()
                            ->where('id', '!=', 1)
                            ->where('name', '!=', 'super_admin')
                            ->pluck('name', 'id')
                            ->toArray()
                    )
                    ->multiple()
                    ->preload()
                    ->searchable(),
                Forms\Components\TextInput::make('rate_cost')
                    ->label(__('translation.rate_cost'))
                    ->numeric()
                    ->minValue(0)
                    ->required()
                    ->default(100)
                    ->helperText(__('translation.rate_cost_helper')),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->modifyQueryUsing(fn (Builder $query): Builder => User::query()->supervisedBy(auth()->user()))
            ->columns([
                Tables\Columns\ImageColumn::make('avatar')
                    ->label(__('translation.avatar_table_label'))
                    ->circular(),
                Tables\Columns\TextColumn::make('id')
                    ->label(__('translation.user'))
                    ->numeric()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: false),
                Tables\Columns\TextColumn::make('first_name')
                    ->label(__('translation.first_name'))
                    ->copyable()
                    ->searchable()
                    ->toggleable(isToggledHiddenByDefault: false),
                Tables\Columns\TextColumn::make('last_name')
                    ->label(__('translation.last_name'))
                    ->toggleable(isToggledHiddenByDefault: false)
                    ->copyable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('email')
                    ->label(__('translation.email'))
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->copyable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('role')
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->label(__('translation.role')),
                Tables\Columns\TextColumn::make('email_verified_at')
                    ->label(__('translation.email_verified_at'))
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->dateTime()
                    ->sortable(),
                Tables\Columns\TextColumn::make('phone_verified_at')
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->label(__('translation.phone_verified_at'))
                    ->dateTime()
                    ->sortable(),
                Tables\Columns\TextColumn::make('dob')
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->label(__('translation.dob'))
                    ->date()
                    ->sortable(),
                Tables\Columns\TextColumn::make('phone')
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->label(__('translation.phone'))
                    ->copyable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('has_correct_balance')
                    ->label('Correct Balance')
                    ->badge()
                    ->color(function (bool $state): string {
                        return $state === true ? 'primary' : 'danger';
                    })
                    ->formatStateUsing(fn ($state) => $state ? 'Yes' : 'No'),
                //                Tables\Columns\TextColumn::make('rate_cost')
                //                    ->label(__('translation.rate_cost'))
                //                    ->formatStateUsing(fn ($state) => number_format($state / 100, 2).' '.__('translation.sar')),
                //                Tables\Columns\TextColumn::make('shipments_costs')
                //                    ->label(__('translation.shipments_costs'))
                //                    ->formatStateUsing(fn ($state) => number_format($state / 100, 2)),
                //                Tables\Columns\TextColumn::make('shipments_order_grand_total')
                //                    ->label(__('translation.shipments_order_grand_total'))
                //                    ->formatStateUsing(fn ($state) => number_format($state / 100, 2)),
                Tables\Columns\TextColumn::make('created_at')
                    ->label(__('translation.created_at'))
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->label(__('translation.updated_at'))
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\TernaryFilter::make('has_correct_balance')
                    ->label('Correct Balance')
                    ->placeholder('All')
                    ->trueLabel('Yes')
                    ->falseLabel('No')
                    ->queries(
                        true: function (Builder $query): Builder {
                            /** @var Builder<\App\Models\User> $query */
                            return $query->hasCorrectBalance(true);
                        },
                        false: function (Builder $query): Builder {
                            /** @var Builder<\App\Models\User> $query */
                            return $query->hasCorrectBalance(false);
                        },
                    ),
            ])
            ->defaultSort('id', 'desc')
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\Action::make('transactions')
                    ->label(__('translation.balance'))
                    ->icon('heroicon-o-banknotes')
                    ->url(fn (User $record) => route('filament.admin.resources.balances.index', [
                        'tableFilters[user_id][value]' => $record->id,
                    ]))
                    ->openUrlInNewTab(),
                Tables\Actions\Action::make('Orders')
                    ->label(__('translation.shipments'))
                    ->icon('heroicon-o-document-text')
                    ->url(fn (User $record) => UserResource::getUrl('orders', ['record' => $record->id]))
                    ->openUrlInNewTab(),
                Impersonate::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                ]),
            ]);
    }

    /**
     * @return Builder<User>
     */
    public static function getEloquentQuery(): Builder
    {
        return User::query()->supervisedBy(auth()->user());
    }

    public static function getRelations(): array
    {
        return [
            ActivitylogRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListUsers::route('/'),
            'create' => Pages\CreateUser::route('/create'),
            'edit' => Pages\EditUser::route('/{record}/edit'),
            'orders' => Pages\UserOrders::route('/{record}/orders'),
        ];
    }

    public static function getNavigationLabel(): string
    {
        //
        return __('translation.users');

    }

    public static function getLabel(): ?string
    {

        return __('translation.users');
    }
}
