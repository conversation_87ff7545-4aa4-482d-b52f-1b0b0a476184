<?php

namespace App\Filament\Resources\ShipmentCourierServiceResource\Widgets;

use App\Filament\Resources\ShipmentCourierServiceResource\Pages\ShipmentCourierServicesOrders;
use App\Models\Order;
use Filament\Widgets\Concerns\InteractsWithPageTable;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;

class ShipmentCourierServiceOrdersOverview extends BaseWidget
{
    use InteractsWithPageTable;

    protected function getTablePage(): string
    {
        return ShipmentCourierServicesOrders::class;
    }

    protected function getCards(): array
    {

        return [
            Stat::make(__('translation.shipments_numbers'), $this->getPageTableQuery()->count()),
            Stat::make(__('translation.shipments_costs'),
                number_format(
                    (float) $this->getPageTableQuery()
                        ->get()
                        ->sum(fn ($order) => $order instanceof Order ? $order->shipment_approved_cost : 0) / 100, 2
                )
            ),
            Stat::make(__('translation.shipments_charges'),
                number_format(
                    (float) $this->getPageTableQuery()
                        ->get()
                        ->sum(fn ($order) => $order instanceof Order ? $order->base_shipment_approved_cost : 0) / 100, 2
                )
            ),
            Stat::make(__('translation.profits'),
                number_format(
                    (float) $this->getPageTableQuery()
                        ->get()
                        ->sum(fn ($order) => $order instanceof Order ? ($order->shipment_approved_cost - $order->base_shipment_approved_cost) : 0) / 100, 2
                )
            ),
            Stat::make(__('translation.shipments_order_grand_total'), number_format((float) $this->getPageTableQuery()->sum('order_grand_total') / 100, 2)),
        ];
    }
}
