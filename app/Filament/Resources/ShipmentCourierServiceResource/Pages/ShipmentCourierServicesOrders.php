<?php

namespace App\Filament\Resources\ShipmentCourierServiceResource\Pages;

use App\Enums\OrderStatusEnum;
use App\Filament\Exports\OrderExporter;
use App\Filament\Resources\OrderResource;
use App\Filament\Resources\ShipmentCourierServiceResource;
use App\Filament\Resources\ShipmentCourierServiceResource\Widgets\ShipmentCourierServiceOrdersOverview;
use App\Models\Order;
use App\Models\User;
use Filament\Pages\Concerns\ExposesTableToWidgets;
use Filament\Resources\Components\Tab;
use Filament\Resources\Pages\ListRecords;
use Filament\Tables;
use Filament\Tables\Actions\ExportBulkAction;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Webbingbrasil\FilamentAdvancedFilter\Filters\DateFilter;

class ShipmentCourierServicesOrders extends ListRecords
{
    use ExposesTableToWidgets;

    protected static string $resource = ShipmentCourierServiceResource::class;

    public function getTitle(): string
    {
        return __('translation.shipments').' '.$this->getShipmentCourierServiceId();
    }

    public function getShipmentCourierServiceId(): ?string
    {
        $record = request()->route('record');

        if (is_string($record) && ! empty($record)) {
            return $record;
        }
        $referer = request()->server('HTTP_REFERER', '');

        if (empty($referer)) {
            return null;
        }

        $url = parse_url($referer);

        if (! is_array($url) || ! isset($url['path'])) {
            return null;
        }

        $segments = explode('/', $url['path']);

        return isset($segments[3]) ? (string) $segments[3] : null;
    }

    protected function getHeaderWidgets(): array
    {
        return [
            ShipmentCourierServiceOrdersOverview::class,
        ];
    }

    public function table(Table $table): Table
    {
        return $table
            ->query(Order::shipmentsByCompany($this->getShipmentCourierServiceId()))
            ->columns([
                Tables\Columns\TextColumn::make('order_number')
                    ->copyable()
                    ->label(__('translation.order_number'))
                    ->searchable(),
                Tables\Columns\TextColumn::make('status')
                    ->formatStateUsing(fn ($state) => trans("translation.order_status.$state"))
                    ->label(__('translation.status')),
                Tables\Columns\TextColumn::make('updated_at')
                    ->label(__('translation.status_changed_at'))
                    ->dateTime(),
                Tables\Columns\TextColumn::make('shipment_reference')
                    ->copyable()
                    ->searchable()
                    ->label(__('translation.shipment_reference')),
                Tables\Columns\TextColumn::make('shipment_approved_cost')
                    ->label(__('translation.shipment_cost'))
                    ->formatStateUsing(fn ($state) => number_format($state / 100, 2)),
                Tables\Columns\TextColumn::make('order_grand_total')
                    ->label(__('translation.order_grand_total'))
                    ->formatStateUsing(fn ($state) => number_format($state, 2)),
                Tables\Columns\TextColumn::make('payment_method')
                    ->label(__('translation.payment_method')),
                Tables\Columns\TextColumn::make('delivred_at')
                    ->label(__('translation.delivred_at'))
                    ->dateTime(),

            ])
            ->paginated([10, 25, 50])
            ->actions([
                Tables\Actions\ViewAction::make()
                    ->url(fn (Order $record) => OrderResource::getUrl('view', ['record' => $record->id])),
            ])
            ->bulkActions([
                ExportBulkAction::make()
                    ->exporter(OrderExporter::class),
            ])
            ->filters([
                DateFilter::make('updated_at')->translateLabel()->label('translation.updated_at'),
                SelectFilter::make('status')
                    ->label(__('translation.status'))
                    ->options(
                        collect(OrderStatusEnum::cases())->mapWithKeys(fn ($case) => [
                            $case->value => __("translation.order_status.{$case->value}"),
                        ])->toArray()
                    ),
                Tables\Filters\SelectFilter::make('payment_method')
                    ->label(__('translation.payment_method'))
                    ->options(fn () => Order::query()
                        ->select('payment_method')
                        ->distinct()
                        ->pluck('payment_method', 'payment_method')
                        ->filter()
                        ->toArray()
                    )
                    ->placeholder('الكل'),
                Tables\Filters\SelectFilter::make('user_id')
                    ->label(__('translation.user'))
                    ->options(function () {
                        return User::query()
                            ->where(function ($query) {
                                $query->whereHas('merchants')
                                    ->orWhereHas('warehouses');
                            })
                            ->whereNotNull('first_name')
                            ->whereNotNull('last_name')
                            ->get()
                            ->mapWithKeys(function ($user) {
                                return [$user->id => $user->full_name];
                            })
                            ->toArray();
                    })
                    ->query(function (Builder $query, array $data): Builder {
                        return $query->when(
                            $data['value'],
                            fn (Builder $query, $userId): Builder => $query->where(function ($q) use ($userId) {
                                $q->whereHas('merchant', fn ($subQuery) => $subQuery->where('user_id', $userId))
                                    ->orWhereHas('warehouse', fn ($subQuery) => $subQuery->where('user_id', $userId));
                            })
                        );
                    })
                    ->placeholder('الكل'),
            ]);
    }

    public function getTabs(): array
    {
        return [
            'user' => Tab::make(__('translation.user_contract'))
                ->modifyQueryUsing(fn (Builder $query) => $query->where('shipment_credentials_type', 'user')),
            'application' => Tab::make(__('translation.application_contract'))
                ->modifyQueryUsing(fn (Builder $query) => $query->where('shipment_credentials_type', 'application')),
            'other' => Tab::make(__('translation.other'))
                ->modifyQueryUsing(fn (Builder $query) => $query->whereNull('shipment_credentials_type')),
            'all' => Tab::make(__('translation.all')),

        ];
    }
}
