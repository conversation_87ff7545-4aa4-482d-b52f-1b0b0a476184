<?php

namespace App\Filament\Resources\ShipmentCourierServiceResource\Pages;

use App\Filament\Resources\ShipmentCourierServiceResource;
use Filament\Resources\Pages\ListRecords;
use Illuminate\Contracts\Support\Htmlable;
use Illuminate\Support\HtmlString;

class ListShipmentCourierServices extends ListRecords
{
    protected static string $resource = ShipmentCourierServiceResource::class;

    public function getTitle(): string|Htmlable
    {
        return new HtmlString('
        <div class="flex items-center">

<lord-icon
    src="https://cdn.lordicon.com/sqsdtvdh.json"
    trigger="loop"
    state="loop-cycle"
    colors="primary:#242424,secondary:#8930e8"
    style="width:50px;height:50px">
</lord-icon>        <span>'.__('translation.shipment_courier_services').'</span>
</div>
    ');
    }
}
