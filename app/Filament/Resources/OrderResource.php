<?php

namespace App\Filament\Resources;

use App\Enums\CourierIdentifierEnum;
use App\Enums\OrderStatusEnum;
use App\Filament\Resources\OrderResource\Pages;
use App\Filament\Resources\OrderResource\RelationManagers\HistoriesRelationManager;
use App\Models\Order;
use App\Models\User;
use App\Models\WalletTransaction;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Actions\Action;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Filters\TernaryFilter;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use pxlrbt\FilamentExcel\Actions\Tables\ExportBulkAction;
use Rmsramos\Activitylog\Actions\ActivityLogTimelineTableAction;
use Rmsramos\Activitylog\RelationManagers\ActivitylogRelationManager;

class OrderResource extends Resource
{
    protected static ?string $model = Order::class;

    protected static ?string $navigationIcon = 'nwwurnnq.json';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('order_number')
                    ->label(__('translation.order_number'))
                    ->required()
                    ->maxLength(191),
                Forms\Components\TextInput::make('status')
                    ->label(__('translation.status'))
                    ->required()
                    ->maxLength(191),
                Forms\Components\DateTimePicker::make('date')
                    ->label(__('translation.date'))
                    ->required(),
                Forms\Components\TextInput::make('order_grand_total')
                    ->label(__('translation.order_grand_total'))
                    ->required()
                    ->numeric()
                    ->default(0),
                Forms\Components\TextInput::make('currency')
                    ->label(__('translation.currency'))
                    ->required()
                    ->maxLength(191)
                    ->default('SAR'),
                Forms\Components\TextInput::make('description')
                    ->label(__('translation.description'))
                    ->maxLength(191),
                Forms\Components\TextInput::make('payment_method')
                    ->label(__('translation.payment_method'))
                    ->required()
                    ->maxLength(191),
                Forms\Components\TextInput::make('receiver_first_name')
                    ->label(__('translation.receiver_first_name'))
                    ->required()
                    ->maxLength(191),
                Forms\Components\TextInput::make('receiver_last_name')
                    ->label(__('translation.receiver_last_name'))
                    ->required()
                    ->maxLength(191),
                Forms\Components\TextInput::make('receiver_phone')
                    ->label(__('translation.receiver_phone'))
                    ->tel()
                    ->required()
                    ->maxLength(191),
                Forms\Components\TextInput::make('receiver_email')
                    ->label(__('translation.receiver_email'))
                    ->email()
                    ->maxLength(191),
                Forms\Components\TextInput::make('receiver_country')
                    ->label(__('translation.receiver_country'))
                    ->required()
                    ->maxLength(191),
                Forms\Components\Select::make('receiver_country_id')
                    ->label(__('translation.receiver_country'))
                    ->relationship(
                        name: 'receiverCountry',
                        titleAttribute: 'name_ar',
                        modifyQueryUsing: fn ($query) => $query->whereNotNull('name_ar')
                    )
                    ->searchable()
                    ->preload()
                    ->required(),
                Forms\Components\TextInput::make('receiver_country_code')
                    ->label(__('translation.receiver_country_code'))
                    ->required()
                    ->maxLength(191),
                Forms\Components\TextInput::make('receiver_city')
                    ->label(__('translation.receiver_city'))
                    ->required()
                    ->maxLength(191),
                Forms\Components\Select::make('receiver_city_id')
                    ->label(__('translation.receiver_city'))
                    ->relationship(
                        name: 'receiverCity',
                        titleAttribute: 'name_ar',
                        modifyQueryUsing: fn ($query) => $query->whereNotNull('name_ar')
                    )
                    ->searchable()
                    ->preload()
                    ->required(),
                Forms\Components\Textarea::make('receiver_address_line')
                    ->label(__('translation.receiver_address_line'))
                    ->required()
                    ->columnSpanFull(),
                Forms\Components\TextInput::make('receiver_street_name')
                    ->label(__('translation.receiver_street_name'))
                    ->maxLength(191),
                Forms\Components\TextInput::make('receiver_block')
                    ->label(__('translation.receiver_block'))
                    ->maxLength(191),
                Forms\Components\TextInput::make('receiver_postal_code')
                    ->label(__('translation.receiver_postal_code'))
                    ->maxLength(191),
                Forms\Components\TextInput::make('receiver_latitude')
                    ->label(__('translation.receiver_latitude'))
                    ->numeric(),
                Forms\Components\TextInput::make('receiver_longitude')
                    ->label(__('translation.receiver_longitude'))
                    ->numeric(),
                Forms\Components\TextInput::make('shipper_name')
                    ->label(__('translation.shipper_name'))
                    ->required()
                    ->maxLength(191),
                Forms\Components\TextInput::make('shipper_email')
                    ->label(__('translation.shipper_email'))
                    ->email()
                    ->maxLength(191),
                Forms\Components\TextInput::make('shipper_phone')
                    ->label(__('translation.shipper_phone'))
                    ->tel()
                    ->maxLength(191),
                Forms\Components\TextInput::make('shipper_city')
                    ->label(__('translation.shipper_city'))
                    ->required()
                    ->maxLength(191),
                Forms\Components\TextInput::make('shipper_address_line')
                    ->label(__('translation.shipper_address_line'))
                    ->required()
                    ->maxLength(191),
                Forms\Components\TextInput::make('shipper_latitude')
                    ->label(__('translation.shipper_latitude'))
                    ->numeric(),
                Forms\Components\TextInput::make('shipper_longitude')
                    ->label(__('translation.shipper_longitude'))
                    ->numeric(),
                Forms\Components\TextInput::make('shipment_company')
                    ->label(__('translation.shipment_company'))
                    ->required()
                    ->maxLength(191),
                Forms\Components\TextInput::make('shipment_tracking_link')
                    ->label(__('translation.shipment_tracking_link'))
                    ->maxLength(191),
                Forms\Components\TextInput::make('shipment_reference')
                    ->label(__('translation.shipment_reference'))
                    ->maxLength(191),
                Forms\Components\TextInput::make('shipment_logo')
                    ->label(__('translation.shipment_logo'))
                    ->maxLength(191),
                Forms\Components\TextInput::make('shipment_total_weight')
                    ->label('shipment_total_weight')
                    ->required()
                    ->numeric()
                    ->default(0),
                Forms\Components\Select::make('merchant_id')
                    ->label(__('translation.merchant_id'))
                    ->relationship('merchant', 'name'),
                Forms\Components\TextInput::make('merchant_email')
                    ->label(__('translation.merchant_email'))
                    ->email()
                    ->maxLength(191),
                Forms\Components\TextInput::make('merchant_phone')
                    ->label(__('translation.merchant_phone'))
                    ->tel()
                    ->maxLength(191),
                Forms\Components\TextInput::make('tax')
                    ->required()
                    ->numeric()
                    ->default(0),
                Forms\Components\TextInput::make('external_id')
                    ->label(__('translation.external_id'))
                    ->maxLength(191),
                Forms\Components\TextInput::make('webhook_id')
                    ->label(__('translation.webhook_id'))
                    ->numeric(),
                Forms\Components\Select::make('warehouse_id')
                    ->label(__('translation.warehouse_id'))
                    ->relationship('warehouse', 'name'),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('id')
                    ->label(__('translation.id'))
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->searchable(isIndividual: true),
                Tables\Columns\TextColumn::make('order_number')
                    ->label(__('translation.order_number'))
                    ->toggleable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('status')
                    ->toggleable()
                    ->formatStateUsing(fn ($state) => trans("translation.order_status.$state"))
                    ->label(__('translation.status'))->sortable(),

                Tables\Columns\TextColumn::make('date')
                    ->label(__('translation.date'))
                    ->toggleable()
                    ->dateTime()
                    ->sortable(),
                Tables\Columns\TextColumn::make('order_grand_total')
                    ->label(__('translation.order_grand_total'))
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('currency')
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->label(__('translation.currency'))
                    ->searchable(),
                Tables\Columns\TextColumn::make('description')
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->label(__('translation.description'))
                    ->searchable(),
                Tables\Columns\TextColumn::make('payment_method')
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->label(__('translation.payment_method'))
                    ->searchable(),
                Tables\Columns\TextColumn::make('receiver_first_name')
                    ->toggleable()
                    ->label(__('translation.receiver_first_name'))
                    ->searchable(),
                Tables\Columns\TextColumn::make('receiver_last_name')
                    ->toggleable()
                    ->label(__('translation.receiver_last_name'))
                    ->searchable(),
                Tables\Columns\TextColumn::make('receiverCity.name_ar')
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->label(__('translation.receiver_city')),
                Tables\Columns\TextColumn::make('shipper_name')
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->label(__('translation.shipper_name'))
                    ->searchable(),
                Tables\Columns\TextColumn::make('shipper_email')
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->label(__('translation.shipper_email'))
                    ->searchable(),
                Tables\Columns\TextColumn::make('shipper_phone')
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->label(__('translation.shipper_phone'))
                    ->searchable(),
                Tables\Columns\TextColumn::make('shipment_company')
                    ->toggleable()
                    ->label(__('translation.shipment_company'))
                    ->formatStateUsing(fn ($state) => trans("translation.$state"))
                    ->searchable(),
                Tables\Columns\TextColumn::make('shipment_tracking_link')
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->label(__('translation.shipment_tracking_link'))
                    ->searchable(),
                Tables\Columns\TextColumn::make('shipment_reference')
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->label(__('translation.shipment_reference'))
                    ->searchable(),
                Tables\Columns\TextColumn::make('shipment_logo')
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->label(__('translation.shipment_logo'))
                    ->searchable(),
                Tables\Columns\TextColumn::make('shipment_total_weight')
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->label(__('translation.shipment_total_weight'))
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('tax')
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->label(__('translation.tax'))
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('external_id')
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->label(__('translation.external_id'))
                    ->searchable(),
                Tables\Columns\TextColumn::make('webhook_id')
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->label(__('translation.webhook_id'))
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('warehouse.name')
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->label(__('translation.warehouse_name'))
                    ->numeric()
                    ->sortable(),

            ])
            ->defaultSort('id', 'desc')
            ->filters([
                SelectFilter::make('user_related')
                    ->label('Merchant or Warehouse')
                    ->options(
                        User::all()->mapWithKeys(function ($user) {
                            return [$user->id => $user->first_name.' '.$user->last_name];
                        })->toArray()
                    )
                    ->query(function (Builder $query, array $data): Builder {
                        if (! isset($data['value'])) {
                            return $query;
                        }

                        return $query->where(function ($q) use ($data) {
                            $q->whereHas('merchant', function ($mq) use ($data) {
                                $mq->where('user_id', $data['value']);
                            })->orWhereHas('warehouse', function ($wq) use ($data) {
                                $wq->where('user_id', $data['value']);
                            });
                        });
                    }),
                TernaryFilter::make('receiver_city_id')
                    ->label(__('translation.receiver_city'))
                    ->placeholder(__('translation.all'))
                    ->trueLabel(__('translation.receiver_city_null'))
                    ->falseLabel(__('translation.receiver_city_not_null'))
                    ->queries(
                        true: fn ($query) => $query->whereNull('receiver_city_id'),
                        false: fn ($query) => $query->whereNotNull('receiver_city_id'),
                        blank: fn ($query) => $query,
                    ),
                SelectFilter::make('status')
                    ->label(__('translation.status'))
                    ->options(
                        collect(OrderStatusEnum::cases())->mapWithKeys(fn ($case) => [
                            $case->value => __("translation.order_status.{$case->value}"),
                        ])->toArray()
                    ),
                SelectFilter::make('shipment_company')
                    ->label(__('translation.shipment_company'))
                    ->options(
                        collect(CourierIdentifierEnum::cases())->mapWithKeys(fn ($case) => [
                            $case->value => __("translation.{$case->value}"),
                        ])->toArray()
                    ),
                TernaryFilter::make('has_wallet_transactions')
                    ->label(__('translation.wallet_transactions_filter'))
                    ->placeholder(__('translation.all_orders'))
                    ->trueLabel(__('translation.has_wallet_transactions'))
                    ->falseLabel(__('translation.no_wallet_transactions'))
                    ->queries(
                        true: fn ($query) => $query->whereHas('walletTransactions'),
                        false: fn ($query) => $query->whereDoesntHave('walletTransactions')
                            ->where('status', '!=', 'pending')
                            ->whereNotNull('shipment_reference'),
                        blank: fn ($query) => $query,
                    ),

            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                ActivityLogTimelineTableAction::make('Activities'),
                Action::make('create_wallet_transaction')
                    ->label(__('translation.create_wallet_transaction'))
                    ->icon('heroicon-o-credit-card')
                    ->color('success')
                    ->visible(fn (Order $record) => $record->walletTransactions()->count() === 0)
                    ->action(function (Order $record) {
                        // Get the user associated with the order (merchant or warehouse user)
                        $userId = $record->merchant ? $record->merchant->user_id : $record->warehouse?->user_id;

                        if (! $userId) {
                            Notification::make()
                                ->title(__('translation.no_user_found_for_order'))
                                ->danger()
                                ->send();

                            return;
                        }

                        // Create a new WalletTransaction for this order
                        $walletTransaction = WalletTransaction::create([
                            'user_id' => $userId,
                            'order_id' => $record->id,
                            'amount' => $record->order_grand_total,
                            'type' => 'credit',
                            'description' => 'Wallet transaction for order '.$record->order_number,
                        ]);

                        Notification::make()
                            ->title(__('translation.wallet_transaction_created_successfully'))
                            ->success()
                            ->send();
                    }),
                //                Tables\Actions\Action::make('accept')
                //                    ->label('Accept Order')
                //                    ->icon('heroicon-o-check-circle')
                //                    ->color('success')
                //                    ->requiresConfirmation()
                //                    ->visible(fn (Order $record) => $record->status === OrderStatusEnum::PENDING_SHIPMENT->value)
                //                    ->action(function (Order $record) {
                //                        $shipmentService = new ShipmentService;
                //
                //                        /** @var Order $order */
                //                        $order = Order::findOrFail($record->id);
                //                        $response = $shipmentService->createShipment(order: $order, createdWithGlobalConfig: $order->createdWithGlobalConfig(), courier: CourierIdentifierEnum::ARAMEX->value);
                //
                //                        if ($response['error']) {
                //                            Notification::make()
                //                                ->title('Failed to create shipment: '.$response['message'])
                //                                ->danger()
                //                                ->send();
                //
                //                            return;
                //                        }
                //                    }),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    ExportBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            HistoriesRelationManager::class,
            ActivitylogRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListOrders::route('/'),
            'create' => Pages\CreateOrder::route('/create'),
            'view' => Pages\ViewOrder::route('/{record}'),
            'edit' => Pages\EditOrder::route('/{record}/edit'),
        ];
    }

    public static function getNavigationLabel(): string
    {
        //
        return __('translation.Orders');

    }

    public static function getLabel(): ?string
    {

        return __('translation.Orders');
    }
}
