<?php

namespace App\Filament\Resources\FaqResource\Pages;

use App\Filament\Resources\FaqResource;
use Filament\Actions;
use Filament\Resources\Pages\ManageRecords;
use Illuminate\Contracts\Support\Htmlable;
use Illuminate\Support\HtmlString;

class ManageFaqs extends ManageRecords
{
    protected static string $resource = FaqResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }

    public function getTitle(): string|Htmlable
    {
        return new HtmlString('
        <div class="flex items-center">

<lord-icon
    src="https://cdn.lordicon.com/znkzapxw.json"
    trigger="hover"
    colors="primary:#242424,secondary:#8930e8"
    style="width:50px;height:50px">
</lord-icon>
        <span>'.__('translation.faqs').'</span>
</div>
    ');
    }
}
