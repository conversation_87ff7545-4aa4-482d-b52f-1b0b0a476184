<?php

namespace App\Filament\Resources;

use App\Enums\CostCodTypeEnum;
use App\Filament\Resources\ShipmentCourierServiceResource\Pages;
use App\Models\ShipmentCourierService;
use Filament\Forms;
use Filament\Forms\Components\Section;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;

class ShipmentCourierServiceResource extends Resource
{
    protected static ?string $model = ShipmentCourierService::class;

    protected static ?string $navigationIcon = 'sqsdtvdh.json';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make(__('translation.public_pricing'))
                    ->schema([
                        Forms\Components\TextInput::make('base_price')
                            ->label(__('translation.base_price'))
                            ->required()
                            ->numeric(),
                        Forms\Components\TextInput::make('extra_weight_from')
                            ->label(__('translation.extra_weight_from'))
                            ->required()
                            ->numeric(),
                        Forms\Components\TextInput::make('additional_weight_cost')
                            ->label(__('translation.additional_weight_cost'))
                            ->required()
                            ->numeric(),
                        Forms\Components\TextInput::make('cash_on_delivery_cost')
                            ->label(__('translation.cash_on_delivery_cost'))
                            ->required()
                            ->numeric(),
                        Forms\Components\TextInput::make('volumetric_divisor')
                            ->label(__('translation.volumetric_divisor'))
                            ->required()
                            ->numeric(),
                        Forms\Components\TextInput::make('base_volumetric_divisor')
                            ->label(__('translation.base_volumetric_divisor'))
                            ->required()
                            ->numeric(),
                    ])
                    ->columns(2),

                Section::make(__('translation.cost_pricing'))
                    ->schema([
                        Forms\Components\TextInput::make('cost_base_price')
                            ->label(__('translation.cost_cost_base_price'))
                            ->numeric(),
                        Forms\Components\TextInput::make('cost_extra_weight_from')
                            ->label(__('translation.cost_extra_weight_from'))
                            ->numeric(),
                        Forms\Components\TextInput::make('cost_additional_weight_cost')
                            ->label(__('translation.cost_additional_weight_cost'))
                            ->numeric(),
                        Forms\Components\TextInput::make('cost_cash_on_delivery_cost')
                            ->label(__('translation.cost_cash_on_delivery_cost'))
                            ->numeric(),
                        Forms\Components\TextInput::make('cost_fuel')
                            ->label(__('translation.cost_fuel'))
                            ->numeric(),
                        Forms\Components\Select::make('cost_cod_type')
                            ->label(__('translation.cost_cod_type'))
                            ->required()
                            ->options(
                                collect(CostCodTypeEnum::cases())->mapWithKeys(fn ($case) => [
                                    $case->value => __("translation.cost_cod_type_options.{$case->value}"),
                                ])->toArray()
                            ),
                    ])
                    ->columns(2),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('identifier')
                    ->searchable(),
                Tables\Columns\TextColumn::make('courier_name')
                    ->label(__('translation.courier_name')),
                Tables\Columns\TextColumn::make('identifier')
                    ->label(__('translation.identifier')),
                Tables\Columns\TextColumn::make('service_name')
                    ->label(__('translation.service_name')),
                //                Tables\Columns\ImageColumn::make('logo'),
                Tables\Columns\TextColumn::make('base_price')
                    ->label(__('translation.base_price'))
                    ->formatStateUsing(fn ($state) => number_format($state / 100, 2)),
                Tables\Columns\TextColumn::make('additional_weight_cost')
                    ->label(__('translation.additional_weight_cost'))
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->formatStateUsing(fn ($state) => number_format($state / 100, 2)),
                Tables\Columns\TextColumn::make('cash_on_delivery_cost')
                    ->label(__('translation.cash_on_delivery_cost'))
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->formatStateUsing(fn ($state) => number_format($state / 100, 2)),
                Tables\Columns\TextColumn::make('extra_weight_from')
                    ->label(__('translation.extra_weight_from'))
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->formatStateUsing(fn ($state) => number_format($state / 100, 2)),
                Tables\Columns\TextColumn::make('cost_base_price')
                    ->label(__('translation.cost_base_price'))
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->numeric()
                    ->formatStateUsing(fn ($state) => number_format($state / 100, 2))
                    ->sortable(),
                Tables\Columns\TextColumn::make('cost_extra_weight_from')
                    ->numeric()
                    ->label(__('translation.cost_extra_weight_from'))
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->formatStateUsing(fn ($state) => number_format($state / 100, 2))
                    ->sortable(),
                Tables\Columns\TextColumn::make('cost_additional_weight_cost')
                    ->label(__('translation.cost_additional_weight_cost'))
                    ->numeric()
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->formatStateUsing(fn ($state) => number_format($state / 100, 2))
                    ->sortable(),
                Tables\Columns\TextColumn::make('cost_cash_on_delivery_cost')
                    ->label(__('translation.cost_cash_on_delivery_cost'))
                    ->numeric()
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->formatStateUsing(fn ($state) => number_format($state / 100, 2))
                    ->sortable(),
                Tables\Columns\TextColumn::make('volumetric_divisor')
                    ->label(__('translation.volumetric_divisor'))
                    ->numeric()
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->sortable(),
                Tables\Columns\TextColumn::make('base_volumetric_divisor')
                    ->label(__('translation.base_volumetric_divisor'))
                    ->numeric()
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->sortable(),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\Action::make('Orders')
                    ->label(__('translation.shipments'))
                    ->icon('heroicon-o-document-text')
                    ->url(fn (ShipmentCourierService $record) => ShipmentCourierServiceResource::getUrl('orders', ['record' => $record->identifier]))
                    ->openUrlInNewTab(),
            ])
            ->headerActions([])
            ->bulkActions([]);
    }

    public static function getRelations(): array
    {
        return [];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListShipmentCourierServices::route('/'),
            'orders' => Pages\ShipmentCourierServicesOrders::route('/{record}/orders'),
        ];
    }

    public static function getNavigationLabel(): string
    {
        //
        return __('translation.shipment_courier_services');

    }

    public static function getLabel(): ?string
    {

        return __('translation.shipment_courier_services');
    }
}
