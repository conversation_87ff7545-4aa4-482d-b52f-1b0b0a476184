<?php

namespace App\Filament\Resources\UserShipmentPriceResource\Pages;

use App\Filament\Resources\UserShipmentPriceResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListUserShipmentPrices extends ListRecords
{
    protected static string $resource = UserShipmentPriceResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
