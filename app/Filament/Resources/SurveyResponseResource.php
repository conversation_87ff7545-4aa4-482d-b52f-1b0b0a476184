<?php

namespace App\Filament\Resources;

use App\Filament\Resources\SurveyResponseResource\Pages;
use App\Models\SurveyResponse;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Filters\QueryBuilder;
use Filament\Tables\Filters\QueryBuilder\Constraints\DateConstraint;
use Filament\Tables\Table;

class SurveyResponseResource extends Resource
{
    protected static ?string $model = SurveyResponse::class;

    protected static ?string $navigationGroup = 'home';

    protected static ?string $navigationIcon = 'vtmfbfkv.json';

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('user_full_name')
                    ->label(__('translation.username'))
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('has_ecommerce_store')
                    ->label(__('translation.has_ecommerce_store'))
                    ->formatStateUsing(fn ($state) => trans('translation.'.$state)),
                Tables\Columns\TextColumn::make('business_industry')
                    ->label(__('translation.business_industry'))
                    ->formatStateUsing(fn ($state) => trans('translation.survey.industries.'.$state))
                    ->searchable(),
                Tables\Columns\TextColumn::make('monthly_orders')
                    ->label(__('translation.monthly_orders'))
                    ->formatStateUsing(fn ($state) => trans('translation.survey.orderOptions.'.$state))
                    ->sortable(),
                Tables\Columns\TextColumn::make('submitted_at')
                    ->label(__('translation.submitted_at'))
                    ->dateTime()
                    ->sortable(),
            ])
            ->filters([
                QueryBuilder::make()
                    ->constraints([
                        DateConstraint::make('submitted_at'),
                    ]),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListSurveyResponses::route('/'),
        ];
    }

    public static function getNavigationLabel(): string
    {
        //
        return __('translation.survey_responses');

    }

    public static function getLabel(): ?string
    {

        return __('translation.survey_responses');
    }
}
