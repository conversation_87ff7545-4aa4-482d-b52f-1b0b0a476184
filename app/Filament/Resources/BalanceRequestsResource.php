<?php

namespace App\Filament\Resources;

use App\Enums\TransactionPaymentMethodEnum;
use App\Enums\TransactionStatusEnum;
use App\Filament\Resources\BalanceRequestsResource\Pages;
use App\Models\Invoice;
use App\Models\Transaction;
use App\Models\User;
use App\Models\WalletTransaction;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Actions\Action;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Table;
use Illuminate\Contracts\View\View;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\DB;
use Storage;

class BalanceRequestsResource extends Resource
{
    protected static ?string $model = Transaction::class;

    protected static ?string $navigationIcon = 'alwzvvks.json';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('status')
                    ->required()
                    ->maxLength(191)
                    ->default('pending'),
                Forms\Components\Textarea::make('response')
                    ->columnSpanFull(),
                Forms\Components\TextInput::make('user_id')
                    ->required()
                    ->numeric(),
                Forms\Components\TextInput::make('amount')
                    ->numeric(),
                Forms\Components\TextInput::make('payment_method')
                    ->required()
                    ->maxLength(191),
                Forms\Components\TextInput::make('transfer_proof')
                    ->maxLength(191),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->modifyQueryUsing(function (Builder $query): Builder {
                return $query->where('payment_method', TransactionPaymentMethodEnum::BANK->value)
                    ->whereIn('status', [TransactionStatusEnum::PENDING->value, TransactionStatusEnum::REFUSED->value]);
            })
            ->columns([
                Tables\Columns\TextColumn::make('user.id')
                    ->label(__('translation.user'))
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('status')
                    ->label(__('translation.status'))
                    ->searchable(),
                Tables\Columns\TextColumn::make('user.full_name')
                    ->label(__('translation.name'))
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('amount')
                    ->label(__('translation.amount'))
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('payment_method')
                    ->label(__('translation.payment_method'))
                    ->searchable(),
                Tables\Columns\ImageColumn::make('transfer_proof')
                    ->label(__('translation.transfer_proof'))
                    ->searchable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->defaultSort('id', 'desc')
            ->filters([
                SelectFilter::make('user')
                    ->label(__('translation.user'))
                    ->relationship('user', 'first_name'),
            ])
            ->actions([
                Action::make('view_image')
                    ->label(__('translation.view_proof'))
                    ->modalContent(fn (Transaction $record): View => view(
                        'filament.resources.transaction-resource.modals.view-image',
                        ['record' => $record]
                    ))
                    ->visible(fn (Transaction $record): bool => $record->payment_method === TransactionPaymentMethodEnum::BANK->value)
                    ->modalSubmitAction(false)
                    ->modalCancelAction(false),
                Tables\Actions\Action::make('download_proof')
                    ->label(__('translation.download_proof'))
                    ->icon('heroicon-o-document-arrow-down')
                    ->color('primary')
                    ->action(fn (Transaction $record) => Storage::disk('public')->download(
                        $record->transfer_proof,
                        'proof.'.pathinfo($record->transfer_proof, PATHINFO_EXTENSION)
                    ))
                    ->visible(fn (Transaction $record): bool => $record->payment_method === TransactionPaymentMethodEnum::BANK->value &&
                    $record->payment_method !== null),
                Action::make('accept_payment')
                    ->label(__('translation.accept_payment'))
                    ->color('success')
                    ->requiresConfirmation()
                    ->modalHeading('Confirm Payment Acceptance')
                    ->modalDescription('Are you sure you want to accept this payment?')
                    ->action(function (Transaction $record) {
                        // Use a transaction to ensure atomicity
                        // 1. Update the transaction status
                        $record->update(['status' => 'success']);
                        $user = User::withoutGlobalScopes()->findOrFail($record->user_id);
                        // 2. Create the wallet transaction
                        WalletTransaction::create([
                            'user_id' => $user->id,
                            'amount' => $record->amount * 100,
                            'type' => 'credit',
                            'transaction_id' => $record->id,
                        ]);
                        Invoice::create([
                            'wallet_transaction_id' => $user->walletTransactions()->latest()->first()->id,
                            'identifier' => Invoice::latest()->first()->id ?? 1000,
                        ]);
                        $user->increment('wallet_balance', $record->amount * 100);
                        DB::commit();
                        // 3. Show success notification.
                        Notification::make()
                            ->title('Payment accepted successfully!')
                            ->success()
                            ->send();

                    })
                    ->visible(fn (Transaction $record): bool => $record->payment_method === TransactionPaymentMethodEnum::BANK->value && $record->status === TransactionStatusEnum::PENDING->value),
                Action::make('refuse_payment')
                    ->label(__('translation.refuse_payment'))
                    ->color('danger')
                    ->requiresConfirmation()
                    ->modalHeading(__('translation.refuse_payment_modal_heading'))
                    ->modalDescription(__('translation.refuse_payment_modal_description'))
                    ->action(function (Transaction $record) {
                        $record->update(['status' => TransactionStatusEnum::REFUSED->value]);

                        Notification::make()
                            ->title('Payment refused.')
                            ->danger()
                            ->send();
                    })
                    ->visible(fn (Transaction $record): bool => $record->payment_method === TransactionPaymentMethodEnum::BANK->value && $record->status === TransactionStatusEnum::PENDING->value),
            ])
            ->bulkActions([]);
    }

    public static function getNavigationBadge(): ?string
    {
        return static::getModel()::where('payment_method', TransactionPaymentMethodEnum::BANK->value)
            ->where('status', TransactionStatusEnum::PENDING->value)->count();
    }

    public static function getNavigationBadgeColor(): ?string
    {
        return 'warning';
    }

    public static function getNavigationLabel(): string
    {
        return __('translation.balance_recharge_requests');
    }

    public static function getLabel(): ?string
    {
        return __('translation.balance_recharge_requests');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListBalanceRequests::route('/'),
        ];
    }
}
