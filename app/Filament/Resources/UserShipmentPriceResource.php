<?php

namespace App\Filament\Resources;

use App\Filament\Resources\UserShipmentPriceResource\Pages;
use App\Models\UserShipmentPrice;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;

class UserShipmentPriceResource extends Resource
{
    protected static ?string $model = UserShipmentPrice::class;

    protected static ?string $navigationIcon = 'heroicon-o-currency-dollar';

    protected static ?string $navigationGroup = 'Shipment Management';

    protected static ?int $navigationSort = 3;

    public static function getModelLabel(): string
    {
        return __('translation.user_shipment_price');
    }

    public static function getPluralModelLabel(): string
    {
        return __('translation.user_shipment_prices');
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Select::make('user_id')
                    ->relationship('user', 'email')
                    ->searchable()
                    ->preload()
                    ->required()
                    ->label(__('translation.user')),

                Forms\Components\Select::make('shipment_courier_service_id')
                    ->relationship('shipmentCourierService', 'courier_name')
                    ->searchable()
                    ->preload()
                    ->required()
                    ->label(__('translation.shipment_courier_service')),

                Forms\Components\TextInput::make('base_price')
                    ->required()
                    ->label(__('translation.base_price')),

                Forms\Components\TextInput::make('extra_weight_from')
                    ->label(__('translation.extra_weight_from'))
                    ->helperText(__('translation.extra_weight_from_helper')),

                Forms\Components\TextInput::make('additional_weight_cost')
                    ->numeric()
                    ->label(__('translation.additional_weight_cost')),

                Forms\Components\TextInput::make('cash_on_delivery_cost')
                    ->numeric()
                    ->label(__('translation.cash_on_delivery_cost')),

                Forms\Components\TextInput::make('distance_cost')
                    ->numeric()
                    ->label(__('translation.distance_cost')),
                Forms\Components\TextInput::make('volumetric_divisor')
                    ->numeric()
                    ->label(__('translation.volumetric_divisor')),

                Forms\Components\Toggle::make('is_active')
                    ->label(__('translation.is_active'))
                    ->default(true),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('user.email')
                    ->label(__('translation.user'))
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('user.full_name')
                    ->label(__('translation.user_name'))
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('shipmentCourierService.courier_name')
                    ->label(__('translation.courier_service'))
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('base_price')
                    ->label(__('translation.base_price'))
                    ->sortable(),

                Tables\Columns\TextColumn::make('extra_weight_from')
                    ->label(__('translation.extra_weight_from'))
                    ->sortable(),

                Tables\Columns\TextColumn::make('additional_weight_cost')
                    ->label(__('translation.additional_weight_cost'))
                    ->sortable(),

                Tables\Columns\IconColumn::make('is_active')
                    ->label(__('translation.is_active'))
                    ->boolean()
                    ->sortable(),

                Tables\Columns\TextColumn::make('created_at')
                    ->label(__('translation.created_at'))
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\TextColumn::make('updated_at')
                    ->label(__('translation.updated_at'))
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('user')
                    ->relationship('user', 'email')
                    ->label(__('translation.filter_by_user')),

                Tables\Filters\SelectFilter::make('shipment_courier_service')
                    ->relationship('shipmentCourierService', 'courier_name')
                    ->label(__('translation.filter_by_courier_service')),

                Tables\Filters\TernaryFilter::make('is_active')
                    ->label(__('translation.active_status')),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('created_at', 'desc');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListUserShipmentPrices::route('/'),
        ];
    }

    /**
     * @return Builder<UserShipmentPrice>
     */
    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->with(['user', 'shipmentCourierService']);
    }
}
