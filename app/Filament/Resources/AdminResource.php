<?php

namespace App\Filament\Resources;

use App\Filament\Resources\AdminResource\Pages;
use App\Models\Admin;
use App\Models\User;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Support\Collection;

class AdminResource extends Resource
{
    protected static ?string $model = Admin::class;

    protected static ?string $navigationIcon = 'jeuxydnh.json';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('first_name')
                    ->label(__('translation.first_name'))
                    ->required()
                    ->maxLength(191),
                Forms\Components\TextInput::make('last_name')
                    ->label(__('translation.last_name'))
                    ->required()
                    ->maxLength(191),
                Forms\Components\TextInput::make('email')
                    ->label(__('translation.email'))
                    ->email()
                    ->required()
                    ->maxLength(191),
                Forms\Components\Select::make('merchantUsers')
                    ->label(__('translation.merchants'))
                    ->relationship('merchantUsers', 'first_name')
                    ->options(function (): Collection {
                        $alreadyAssigned = \DB::table('admin_clients')->pluck('user_id');

                        return User::query()
                            ->where('role', 'merchant')
                            ->whereNotIn('id', $alreadyAssigned)
                            ->get()
                            ->pluck('full_name', 'id');
                    })
                    ->getOptionLabelFromRecordUsing(
                        fn ($record) => $record->full_name
                    )
                    ->multiple()
                    ->preload(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('id')
                    ->label(__('translation.user'))
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('first_name')
                    ->label(__('translation.first_name'))
                    ->searchable(),
                Tables\Columns\TextColumn::make('last_name')
                    ->label(__('translation.last_name'))
                    ->searchable(),

            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\Action::make('Orders')
                    ->label(__('translation.shipments'))
                    ->icon('heroicon-o-document-text')
                    ->url(fn (Admin $record) => AdminResource::getUrl('orders', ['record' => $record->id]))
                    ->openUrlInNewTab(),
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getNavigationLabel(): string
    {
        return __('translation.commercials');
    }

    public static function getLabel(): ?string
    {

        return __('translation.commercials');
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListAdmins::route('/'),
            'edit' => Pages\EditAdmin::route('/{record}/edit'),
            'orders' => Pages\AdminOrders::route('/{record}/orders'),
        ];
    }
}
