<?php

namespace App\Filament\Resources\OrderResource\Pages;

use App\Filament\Resources\OrderResource;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\ListRecords;
use Illuminate\Contracts\Support\Htmlable;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\HtmlString;
use Termwind\Enums\Color;

class ListOrders extends ListRecords
{
    protected static string $resource = OrderResource::class;

    protected function getHeaderActions(): array
    {
        return [
            \Filament\Actions\Action::make('update_null_receiver_city_id')
                ->color(Color::GRAY)
                ->label(__('translation.update_null_receiver_city_id'))
                ->requiresConfirmation()
                ->action(function () {
                    // Run the artisan command with the --only-null option
                    Artisan::call('app:orders:update-receiver-city-id', [
                        '--only-null' => true,
                    ]);

                    Notification::make()
                        ->title('update_null_receiver_city_id_updated')
                        ->success()
                        ->send();
                }),
        ];
    }

    public function getTitle(): string|Htmlable
    {
        return new HtmlString('
        <div class="flex items-center">

<lord-icon
    src="https://cdn.lordicon.com/nwwurnnq.json"
    trigger="hover"
    colors="primary:#8930e8,secondary:#242424"
    style="width:50px;height:50px">
</lord-icon>
        <span>'.__('translation.Orders').'</span>
</div>
    ');
    }
}
