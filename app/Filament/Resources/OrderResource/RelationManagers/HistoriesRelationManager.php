<?php

namespace App\Filament\Resources\OrderResource\RelationManagers;

use App\Enums\OrderHistoryEnum;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;

class HistoriesRelationManager extends RelationManager
{
    protected static string $relationship = 'histories';

    public static function getTitle(\Illuminate\Database\Eloquent\Model $ownerRecord, string $pageClass): string
    {
        return __('translation.order_histories');
    }

    public static function getModelLabel(): string
    {
        return __('translation.order_histories');
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->modifyQueryUsing(fn (Builder $query) => $query->orderBy('action_time', 'desc'))
            ->recordTitleAttribute('order')
            ->columns([
                Tables\Columns\TextColumn::make('action_time')
                    ->translateLabel()
                    ->label('translation.date')
                    ->dateTime('Y-m-d H:m:s'),
                Tables\Columns\TextColumn::make('shipment_id')
                    ->translateLabel()
                    ->label('translation.shipping_id'),
                Tables\Columns\TextColumn::make('description')
                    ->translateLabel()
                    ->label('translation.description'),
                Tables\Columns\TextColumn::make('event_type')
                    ->label(__('translation.status'))
                    ->badge()
                    ->formatStateUsing(fn ($state) => __("translation.order_status.{$state}"))
                    ->color(fn ($state) => OrderHistoryEnum::tryFrom($state)?->color() ?? 'gray'),
            ])
            ->defaultSort('id', 'desc')
            ->filters([
                //
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make(),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }
}
