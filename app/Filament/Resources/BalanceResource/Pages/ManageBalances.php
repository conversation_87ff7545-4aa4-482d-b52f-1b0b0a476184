<?php

namespace App\Filament\Resources\BalanceResource\Pages;

use App\Filament\Resources\BalanceResource;
use App\Filament\Resources\BalanceResource\BalanceOverview;
use Filament\Pages\Concerns\ExposesTableToWidgets;
use Filament\Resources\Pages\ManageRecords;

class ManageBalances extends ManageRecords
{
    use ExposesTableToWidgets;

    protected static string $resource = BalanceResource::class;

    protected function getHeaderActions(): array
    {
        return [
        ];
    }

    protected function getHeaderWidgets(): array
    {
        return [
            BalanceOverview::class,
        ];
    }

    public function getTitle(): string
    {
        return __('translation.balance');
    }
}
