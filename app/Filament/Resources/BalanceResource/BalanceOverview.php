<?php

namespace App\Filament\Resources\BalanceResource;

use App\Filament\Resources\BalanceResource\Pages\ManageBalances;
use App\Models\Order;
use App\Models\User;
use App\Models\WalletTransaction;
use Filament\Widgets\Concerns\InteractsWithPageTable;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;

class BalanceOverview extends BaseWidget
{
    use InteractsWithPageTable;

    protected function getTablePage(): string
    {
        return ManageBalances::class;
    }

    protected function getCards(): array
    {
        $userId = $this->tableFilters['user_id']['value'] ?? null;

        $filteredQuery = fn ($type) => WalletTransaction::where('type', $type)
            ->when($userId, fn ($q) => $q->where('user_id', $userId));

        $sumDebit = $filteredQuery('debit')->sum('amount') / 100;
        $sumCharge = $filteredQuery('credit')->sum('amount') / 100;
        $sumCancel = $filteredQuery('cancel')->sum('amount') / 100;
        $sumChargeCancel = $sumCharge + $sumCancel;
        $difference = $sumChargeCancel - $sumDebit;

        $walletBalance = null;
        if ($userId) {
            /** @var User $user */
            $user = User::findOrFail($userId);
            $walletBalance = $user->wallet_balance;
        }

        // Calculate total shipment cost
        $shipmentCostQuery = Order::query()
            ->when($userId, function ($query) use ($userId) {
                $query->whereHas('merchant', fn ($q) => $q->where('user_id', $userId))
                    ->orWhereHas('warehouse', fn ($q) => $q->where('user_id', $userId));
            });
        $totalShipmentCost = $shipmentCostQuery->sum('shipment_cost') / 100;

        return [
            Stat::make(
                __('translation.balance'),
                number_format((float) ($walletBalance ?? 0) / 100, 2)
            ),
            Stat::make(
                __('translation.total_debit'),
                number_format($sumDebit, 2)
            ),
            Stat::make(
                __('translation.difference_charge_debit'),
                number_format($sumCharge, 2)
            ),
            Stat::make(
                __('translation.difference_charge_cancel'),
                number_format($sumCancel, 2)
            ),
            Stat::make(
                __('translation.total_charge_cancel'),
                number_format($sumChargeCancel, 2)
            ),
            Stat::make(
                __('translation.difference_charge_cancel_debit'),
                number_format($difference, 2)
            ),
            Stat::make(
                __('translation.total_shipment_cost'),
                number_format($totalShipmentCost, 2)
            ),
        ];
    }
}
