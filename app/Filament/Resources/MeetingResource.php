<?php

namespace App\Filament\Resources;

use App\Filament\Resources\MeetingResource\Pages;
use App\Models\Meeting;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;

class MeetingResource extends Resource
{
    protected static ?string $model = Meeting::class;

    protected static ?string $navigationIcon = 'jeuxydnh.json';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Select::make('status')
                    ->required()
                    ->options([
                        'pending' => 'pending',
                        'accepted' => 'accepted',
                        'rejected' => 'rejected',
                        'finished' => 'finished',
                    ])
                    ->default('pending'),
                Forms\Components\RichEditor::make('admin_note')
                    ->label('Admin Notes')
                    ->nullable()
                    ->columnSpanFull(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('user.id')
                    ->label(__('translation.user'))
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('user.first_name')
                    ->label(__('translation.username'))
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('meeting_date')
                    ->label(__('translation.meeting_date'))
                    ->date()
                    ->sortable(),
                Tables\Columns\TextColumn::make('meeting_time')->label(__('translation.meeting_time')),
                Tables\Columns\TextColumn::make('status')
                    ->searchable()->label(__('translation.status')),
                Tables\Columns\TextColumn::make('notes')
                    ->label(__('translation.notes'))
                    ->limit(50),
                Tables\Columns\TextColumn::make('admin_note')
                    ->label('Admin Notes')
                    ->limit(50),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListMeetings::route('/'),
            'view' => Pages\ViewMeeting::route('/{record}'),
            'edit' => Pages\EditMeeting::route('/{record}/edit'),
        ];
    }

    public static function getNavigationLabel(): string
    {
        return __('translation.meetings');
    }

    public static function getLabel(): ?string
    {
        return __('translation.meetings');
    }
}
