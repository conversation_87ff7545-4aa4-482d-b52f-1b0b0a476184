<?php

namespace App\Filament\Resources;

use App\Enums\SalesChannelEnum;
use App\Filament\Resources\MerchantResource\Pages;
use App\Models\Merchant;
use App\Services\ShopifyAuthService;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;

class MerchantResource extends Resource
{
    protected static ?string $model = Merchant::class;

    protected static ?string $navigationIcon = 'eyvkykfo.json';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('name')
                    ->label(__('translation.name'))
                    ->required()
                    ->maxLength(191),
                //                Forms\Components\TextInput::make('merchant_id')
                //                    ->numeric(),
                Forms\Components\TextInput::make('domain')
                    ->label(__('translation.domain'))
                    ->maxLength(191),
                //                Forms\Components\Textarea::make('authorization')
                //                    ->columnSpanFull(),
                Forms\Components\Textarea::make('access_token')
                    ->label(__('translation.access_token'))
                    ->columnSpanFull()->rows(3),
                Forms\Components\Textarea::make('authorization')
                    ->label('authorization')
                    ->columnSpanFull()->rows(3),
                Forms\Components\Textarea::make('api_key')
                    ->label(__('translation.api_key'))
                    ->columnSpanFull()->rows(3),
                Forms\Components\Textarea::make('api_secret_key')
                    ->label(__('translation.api_secret_key'))
                    ->columnSpanFull()->rows(3),
                Forms\Components\Textarea::make('refresh_token')
                    ->label(__('translation.refresh_token'))
                    ->columnSpanFull()->rows(3),
                Forms\Components\DatePicker::make('expires_in')
                    ->label(__('translation.expires_in')),
                //                Forms\Components\Textarea::make('metadata')
                //                    ->columnSpanFull(),
                //                Forms\Components\TextInput::make('type')
                //                    ->required()
                //                    ->maxLength(191),,
                //                Forms\Components\Select::make('user_id')
                //                    ->relationship('user', 'id')
                //                    ->required(),
                Forms\Components\TextInput::make('webhook_url')
                    ->label(__('translation.webhook_url')),
                Forms\Components\TextInput::make('trigger_url')
                    ->label(__('translation.trigger_url'))
                    ->visible(fn (Merchant $merchant) => $merchant->type === SalesChannelEnum::ZID->value),
                //                Forms\Components\TextInput::make('warehouse_id')
                //                    ->numeric(),

                Forms\Components\Toggle::make('active')
                    ->label(__('translation.active'))
                    ->required(),
                Forms\Components\Toggle::make('webhook_is_linked')
                    ->label(__('translation.webhook_is_linked'))
                    ->required(),
            ]);
    }

    public static function getNavigationBadgeColor(): ?string
    {
        return 'warning';
    }

    public static function table(Table $table): Table
    {
        Tables\Columns\TextColumn::make('expires_in')
            ->label(__('translation.expires_in'))
            ->numeric()
            ->sortable();

        return $table
            ->columns([
                Tables\Columns\TextColumn::make('user.id')
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->label(__('translation.user'))
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('name')
                    ->label(__('translation.name'))
                    ->searchable(),
                //                Tables\Columns\TextColumn::make('merchant_id')
                //                    ->label(__('translation.merchant_id'))
                //                    ->numeric()
                //                    ->sortable(),
                Tables\Columns\TextColumn::make('domain')
                    ->label(__('translation.domain'))
                    ->searchable(),
                Tables\Columns\TextColumn::make('type')
                    ->label(__('translation.type'))
                    ->searchable(),
                Tables\Columns\IconColumn::make('active')
                    ->label(__('translation.active'))
                    ->boolean(),
                Tables\Columns\IconColumn::make('webhook_is_linked')
                    ->label(__('translation.webhook_is_linked'))
                    ->boolean(),
                Tables\Columns\TextColumn::make('user.full_name')
                    ->label(__('translation.user'))
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->label(__('translation.created_at'))
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->label(__('translation.updated_at'))
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('warehouse.name')
                    ->label(__('translation.warehouse'))
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('warehouse.address')
                    ->label(__('translation.address_details'))
                    ->toggleable()
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('warehouse.sender_phone')
                    ->label(__('translation.phone_number'))
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->sortable(),
            ])
            ->defaultSort('id', 'desc')
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\Action::make('setupWebhook')
                    ->label(__('translation.setup_webhook'))
                    ->icon('heroicon-m-link')
                    ->visible(fn (Merchant $record) => $record->type === 'shopify' &&
                    $record->webhook_is_linked == false)
                    ->disabled(fn (Merchant $record) => empty($record->access_token) || empty($record->api_key) || empty($record->api_secret_key))
                    ->requiresConfirmation()
                    ->action(function (Merchant $record) {
                        if (Merchant::where('access_token', $record->access_token)->where('id', '!=', $record->id)->exists()) {
                            Notification::make()
                                ->title(__('translation.access_token_exist'))
                                ->danger()
                                ->send();

                            return;
                        }
                        if (Merchant::where('api_key', $record->api_key)->where('id', '!=', $record->id)->exists()) {
                            Notification::make()
                                ->title(__('translation.api_key_exist'))
                                ->danger()
                                ->send();

                            return;
                        }
                        if (Merchant::where('api_secret_key', $record->api_secret_key)->where('id', '!=', $record->id)->exists()) {
                            Notification::make()
                                ->title(__('translation.api_secret_key_exist'))
                                ->danger()
                                ->send();

                            return;
                        }
                        // Call webhook creation logic
                        (new ShopifyAuthService($record))->createWebhooksForMerchant();
                        Notification::make()
                            ->title(__('translation.linked'))
                            ->success()
                            ->send();
                    }),
            ])
            ->bulkActions([]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListMerchants::route('/'),
            'create' => Pages\CreateMerchant::route('/create'),
            'view' => Pages\ViewMerchant::route('/{record}'),
            'edit' => Pages\EditMerchant::route('/{record}/edit'),
        ];
    }

    public static function getNavigationLabel(): string
    {
        //
        return __('translation.Sales_Channels');

    }

    public static function getLabel(): ?string
    {
        return __('translation.Sales_Channels');
    }
}
