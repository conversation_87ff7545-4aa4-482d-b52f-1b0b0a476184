<?php

namespace App\Filament\Resources;

use App\Filament\Resources\CityResource\Pages;
use App\Models\City;
use App\Models\Country;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;

class CityResource extends Resource
{
    protected static ?string $model = City::class;

    protected static ?string $navigationIcon = 'lbwmaecq.json';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('name')
                    ->label(__('translation.name_en'))
                    ->maxLength(191),
                Forms\Components\TextInput::make('name_ar')
                    ->label(__('translation.name_ar'))
                    ->maxLength(191),
                Forms\Components\Select::make('country_id')
                    ->label(__('translation.country'))
                    ->options(Country::all()->pluck('translatedName', 'id'))
                    ->required()
                    ->reactive()
                    ->label(__('translation.country')),
                Forms\Components\Repeater::make('cityTranslations')
                    ->label(__('translation.city_translations'))
                    ->relationship()
                    ->schema([
                        Forms\Components\Grid::make(2)
                            ->schema([
                                Forms\Components\TextInput::make('value')
                                    ->label(__('translation.city_value'))
                                    ->required(),
                                Forms\Components\Select::make('source')
                                    ->label(__('translation.city_source'))
                                    ->options([
                                        'name_aramex' => __('translation.name_aramex'),
                                        'name_thabit' => __('translation.name_thabit'),
                                        'name_salla' => __('translation.name_salla'),
                                        'name_zid' => __('translation.name_zid'),
                                        'name_spl' => __('translation.name_spl'),
                                    ])
                                    ->required(),
                            ]),
                    ])
                    ->columnSpanFull()
                    ->maxItems(7),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name_ar')
                    ->label(__('translation.name_ar'))
                    ->translateLabel()
                    ->searchable(),
                Tables\Columns\TextColumn::make('name')
                    ->label(__('translation.name_en'))
                    ->translateLabel()
                    ->searchable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->label(__('translation.created_at'))
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->label(__('translation.updated_at'))
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListCities::route('/'),
            'create' => Pages\CreateCity::route('/create'),
            'edit' => Pages\EditCity::route('/{record}/edit'),
        ];
    }

    public static function getNavigationLabel(): string
    {
        //
        return __('translation.cities');

    }

    public static function getLabel(): ?string
    {

        return __('translation.cities');
    }
}
