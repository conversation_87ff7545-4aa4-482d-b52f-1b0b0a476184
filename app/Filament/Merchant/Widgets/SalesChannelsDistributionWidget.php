<?php

namespace App\Filament\Merchant\Widgets;

use App\Dto\SalesDistribution;
use App\Models\Order;
use Filament\Widgets\Widget;
use Illuminate\Support\Facades\DB;

class SalesChannelsDistributionWidget extends Widget
{
    protected static ?int $sort = 4;

    public function getHeading(): ?string
    {
        return __('translation.widgets.distribution_by_sales_channels');
    }

    protected static string $view = 'filament.merchant.widgets.sales-channels-distribution-widget';

    /**
     * @return array<string,string>
     */
    public function getData(int $filter = 30): array
    {
        // Calculate the date range based on the filter
        $startDate = now()->subDays(30);
        $endDate = now();

        // Query to get the distribution of sales channels within the date range
        $totalOrders = Order::whereBetween('date', [$startDate->startOfDay(), $endDate->endOfDay()])->count();

        $salesChannels = Order::leftJoin('merchants', 'orders.merchant_id', '=', 'merchants.id')
            ->select(DB::raw('COALESCE(merchants.name, "طلبات بدوية") as merchant_name'), DB::raw('COUNT(*) as count'))
            ->whereBetween('orders.date', [$startDate->startOfDay(), $endDate->endOfDay()])
            ->groupBy('orders.merchant_id', 'merchant_name')
            ->orderByDesc('count')
            ->get()
            ->map(fn ($channel) => new SalesDistribution(
                merchantName: $channel['merchant_name'],
                count: (int) $channel['count'],
                percentage: $totalOrders > 0
                    ? round(($channel['count'] / $totalOrders) * 100, 1)
                    : 0
            ));

        return [
            'salesChannels' => $salesChannels,
        ];
    }
}
