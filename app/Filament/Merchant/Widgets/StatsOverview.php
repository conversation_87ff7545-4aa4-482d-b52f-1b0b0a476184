<?php

namespace App\Filament\Merchant\Widgets;

use App\Enums\OrderStatusEnum;
use App\Models\Order;
use Carbon\Carbon;
use Filament\Widgets\Concerns\InteractsWithPageFilters;
use Filament\Widgets\StatsOverviewWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;

class StatsOverview extends StatsOverviewWidget
{
    use InteractsWithPageFilters;

    protected static ?int $sort = 6;

    protected function getHeading(): ?string
    {
        return __('translation.number_of_orders');
    }

    protected function getStats(): array
    {
        // Retrieve the date range from filters
        $startDate = ! is_null($this->filters['startDate'] ?? null)
            ? Carbon::parse($this->filters['startDate'])->startOfDay()
            : now()->subDays(30)->startOfDay();

        $endDate = ! is_null($this->filters['endDate'] ?? null)
            ? Carbon::parse($this->filters['endDate'])->endOfDay()
            : now()->endOfDay();

        // Count orders by their current status
        $ordersTotal = Order::count();

        $pendingOrdersCount = Order::where('status', OrderStatusEnum::PENDING)->count();

        $canceledOrdersCount = Order::where('status', OrderStatusEnum::CANCELED)->count();

        $returnedOrdersCount = Order::where('status', OrderStatusEnum::RETURNED)->count();

        $currentlyShippingOrdersCount = Order::where('status', OrderStatusEnum::CURRENTLY_SHIPPING)->count();

        $awaitingPickupOrdersCount = Order::where('status', OrderStatusEnum::AWAITING_PICKUP)->count();

        $shippedOrdersCount = Order::where('status', OrderStatusEnum::DELIVERED)->count();

        return [
            Stat::make('pending_orders', $pendingOrdersCount)
                ->label(__('translation.pending_orders'))
                ->color('info')
                ->description(__('translation.show_more'))
                ->url(route('filament.merchant.resources.orders.index', ['activeTab' => OrderStatusEnum::PENDING->value]))
                ->descriptionIcon('heroicon-m-arrow-left')
                ->icon('heroicon-m-arrow-trending-up'),

            Stat::make('currently_shipping', $currentlyShippingOrdersCount)
                ->label(__('translation.currently_shipping'))
                ->color('info')
                ->description(__('translation.show_more'))
                ->url(route('filament.merchant.resources.orders.index', ['activeTab' => OrderStatusEnum::CURRENTLY_SHIPPING->value]))
                ->descriptionIcon('heroicon-m-arrow-left')
                ->icon('heroicon-m-circle-stack'),

            Stat::make('shipment_on_hold', $awaitingPickupOrdersCount)
                ->label(__('translation.shipment_on_hold'))
                ->color('info')
                ->description(__('translation.show_more'))
                ->url(route('filament.merchant.resources.orders.index', ['activeTab' => OrderStatusEnum::SHIPMENT_ON_HOLD->value]))
                ->descriptionIcon('heroicon-m-arrow-left')
                ->icon('heroicon-m-arrow-right-start-on-rectangle'),

            Stat::make('delivered', $shippedOrdersCount)
                ->label(__('translation.delivered'))
                ->color('info')
                ->description(__('translation.show_more'))
                ->url(route('filament.merchant.resources.orders.index', ['activeTab' => OrderStatusEnum::DELIVERED->value]))
                ->descriptionIcon('heroicon-m-arrow-left')
                ->icon('heroicon-m-pencil-square'),

            Stat::make('returned', $returnedOrdersCount)
                ->label(__('translation.returned'))
                ->color('info')
                ->description(__('translation.show_more'))
                ->url(route('filament.merchant.resources.orders.index', ['activeTab' => OrderStatusEnum::RETURNED->value]))
                ->descriptionIcon('heroicon-m-arrow-left')
                ->icon('heroicon-m-minus-circle'),

            Stat::make('canceled_orders', $canceledOrdersCount)
                ->label(__('translation.canceled_orders'))
                ->color('info')
                ->description(__('translation.show_more'))
                ->url(route('filament.merchant.resources.orders.index', ['activeTab' => OrderStatusEnum::CANCELED->value]))
                ->descriptionIcon('heroicon-m-arrow-left')
                ->icon('heroicon-m-cube-transparent'),

            Stat::make('all_orders', $ordersTotal)
                ->label(__('translation.all_orders'))
                ->color('info')
                ->description(__('translation.show_more'))
                ->url(route('filament.merchant.resources.orders.index', ['activeTab' => 'all_orders']))
                ->descriptionIcon('heroicon-m-arrow-left')
                ->icon('heroicon-m-bolt'),
        ];
    }

    public function getColumns(): int
    {
        return 4;
    }
}
