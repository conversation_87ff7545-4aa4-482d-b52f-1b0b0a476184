<?php

namespace App\Filament\Merchant\Widgets;

use App\Dto\ShippingCompanyDistribution;
use App\Models\Order;
use Filament\Widgets\Widget;
use Illuminate\Support\Facades\DB;

class ShippingCompaniesDistributionWidget extends Widget
{
    protected static ?int $sort = 5;

    public function getHeading(): ?string
    {
        return __('translation.widgets.shipping_companies_distribution_rate');
    }

    protected static string $view = 'filament.merchant.widgets.shipping-companies-distribution-widget';

    /**
     * @return string[]
     */
    public function getData(): array
    {
        // Total Orders Query
        $totalOrders = Order::whereNotNull('shipment_reference')->count();

        // Shipping Companies Query
        $shippingCompanies = Order::select('shipment_company', DB::raw('COUNT(*) as count'))
            ->whereNotNull('shipment_reference')
            ->groupBy('shipment_company')
            ->orderByDesc('count')
            ->limit(5)
            ->get()->map(fn ($company) => new ShippingCompanyDistribution(
                shipmentCompany: __('translation.'.$company['shipment_company']),
                percentage: round(($company['count'] / $totalOrders) * 100, 1)));

        return [
            'shippingCompanies' => $shippingCompanies,
        ];
    }

    public static function getDefaultColumnSpan(): int
    {
        return 3;
    }
}
