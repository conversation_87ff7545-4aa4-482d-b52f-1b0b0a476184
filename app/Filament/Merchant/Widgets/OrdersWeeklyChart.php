<?php

namespace App\Filament\Merchant\Widgets;

use App\Models\Order;
use Filament\Widgets\ChartWidget;

class OrdersWeekly<PERSON>hart extends ChartWidget
{
    protected static ?int $sort = 8;

    public function getHeading(): ?string
    {
        return __('translation.widgets.weekly_orders_distribution');
    }

    protected function getData(): array
    {
        // Query to get the count of orders grouped by weekday
        $ordersData = Order::selectRaw('DAYOFWEEK(date) as day_of_week, COUNT(*) as count')
            ->whereBetween('date', [now()->startOfWeek(), now()->endOfWeek()])
            ->groupBy('day_of_week')
            ->orderBy('day_of_week')
            ->get();

        // Map the weekdays to Arabic labels
        $weekdays = getWeekDays();

        // Initialize data array with zeros
        $data = array_fill(1, 7, 0);

        // Populate the data array based on query results
        foreach ($ordersData as $order) {
            $data[$order['day_of_week']] = $order['count'];
        }

        return [
            'datasets' => [
                [
                    'label' => __('translation.order_distribution_weekly'),
                    'data' => array_values($data), // Use the counts in the correct order
                ],
            ],
            'labels' => array_values($weekdays), // Arabic labels
        ];
    }

    protected function getType(): string
    {
        return 'bar';
    }
}
