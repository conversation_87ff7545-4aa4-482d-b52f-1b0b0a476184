<?php

namespace App\Filament\Merchant\Widgets;

use App\Dto\DestinationSummary;
use App\Models\City;
use App\Models\Order;
use Filament\Widgets\Widget;
use Illuminate\Support\Facades\DB;

class TopDestinationsWidget extends Widget
{
    protected static ?int $sort = 7;

    public function getHeading(): ?string
    {
        return __('translation.widgets.top_destinations');
    }

    protected static string $view = 'filament.merchant.widgets.top-destinations-widget';

    /**
     * @return string[]
     */
    public function getData(): array
    {
        // Total Orders Query
        $totalOrders = Order::count();

        // Get top 5 destinations
        $topDestinations = Order::select('receiver_city_id', DB::raw('COUNT(*) as count'))
            ->whereNotNull('receiver_city_id')
            ->where('receiver_city_id', '!=', '')
            ->groupBy('receiver_city_id')
            ->orderByDesc('count')
            ->limit(5)
            ->get();

        $cityNames = City::whereIn('id', $topDestinations->pluck('receiver_city_id'))->get()
            ->mapWithKeys(function ($city) {
                return [$city->id => $city->translated_name];
            });
        // Get the count of all other destinations
        $otherDestinationsCount = Order::whereNotNull('receiver_city_id')
            ->where('receiver_city_id', '!=', '')
            ->whereNotIn('receiver_city_id', $topDestinations->pluck('receiver_city_id'))
            ->count();

        // Map the top destinations
        $destinations = $topDestinations->map(function ($row) use ($totalOrders, $cityNames) {
            return new DestinationSummary(
                receiver_city: $cityNames[$row['receiver_city_id']],
                count: $row['count'],
                percentage: round(($row['count'] / $totalOrders) * 100, 1)
            );
        });

        // Add the "Others" category if there are remaining destinations
        if ($otherDestinationsCount > 0) {
            $destinations->push(new DestinationSummary(
                receiver_city: __('translation.other_destinations'),
                count: $otherDestinationsCount,
                percentage: round(($otherDestinationsCount / $totalOrders) * 100, 1)
            ));
        }

        return [
            'destinations' => $destinations,
        ];
    }

    public static function getDefaultColumnSpan(): int
    {
        return 4; // 1/3 of the page
    }
}
