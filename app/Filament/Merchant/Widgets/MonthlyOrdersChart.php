<?php

namespace App\Filament\Merchant\Widgets;

use App\Models\Order;
use Carbon\Carbon;
use Filament\Widgets\ChartWidget;

class MonthlyOrdersChart extends ChartWidget
{
    protected static ?int $sort = 3;

    public function getHeading(): ?string
    {
        return __('translation.widgets.daily_orders_distribution');
    }

    protected function getData(): array
    {
        Carbon::setLocale(app()->getLocale());
        // Calculate the date range for the last 11 days
        $endDate = now(); // Today
        $startDate = now()->subDays(10); // 10 days before today

        // Query to get the count of orders grouped by day, limited to the last 11 days
        $ordersData = Order::selectRaw('DATE(date) as day, COUNT(*) as count')
            ->whereBetween('date', [$startDate->startOfDay(), $endDate->endOfDay()]) // Filter by the last 11 days
            ->groupBy('day')
            ->orderBy('day')
            ->get();

        // Initialize data array with zeros for the last 11 days
        $data = [];
        $labels = [];

        for ($date = $startDate; $date <= $endDate; $date->addDay()) {
            $formattedDate = $date->format('Y-m-d');
            $labels[] = $date->translatedFormat('d M'); // e.g., '21 Dec'
            $data[$formattedDate] = 0; // Default value
        }

        // Populate the data array based on query results
        foreach ($ordersData as $order) {
            $data[$order['day']] = $order['count'];
        }

        return [
            'datasets' => [
                [
                    'label' => __('translation.widgets.daily_orders_distribution'),
                    'data' => array_values($data), // Use the counts in the correct order
                ],
            ],
            'labels' => $labels, // Labels for the last 11 days
        ];
    }

    protected function getType(): string
    {
        return 'bar';
    }
}
