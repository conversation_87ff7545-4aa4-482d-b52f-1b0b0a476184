<?php

namespace App\Filament\Merchant\Widgets;

use App\Enums\OrderStatusEnum;
use App\Enums\PaymentMethodEnum;
use App\Models\Order;
use Carbon\Carbon;
use Filament\Widgets\Concerns\InteractsWithPageFilters;
use Filament\Widgets\StatsOverviewWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;

class GlobalOverview extends StatsOverviewWidget
{
    use InteractsWithPageFilters;

    protected static ?int $sort = 2;

    protected function getHeading(): ?string
    {
        return __('translation.global_overview');
    }

    protected function getStats(): array
    {
        $startDate = ! is_null($this->filters['startDate'] ?? null)
            ? Carbon::parse($this->filters['startDate'])->startOfDay()
            : now()->subDays(30)->startOfDay();

        $endDate = ! is_null($this->filters['endDate'] ?? null)
            ? Carbon::parse($this->filters['endDate'])->endOfDay()
            : now()->endOfDay();

        $pendingOrdersTotal = Order::whereHas('histories', fn ($query) => $query->where('event_type', OrderStatusEnum::PENDING)
            ->whereBetween('action_time', [$startDate, $endDate])
        )->count();

        $shippedOrdersCount = Order::whereHas('histories', fn ($query) => $query->where('event_type', OrderStatusEnum::DELIVERED)
            ->whereBetween('action_time', [$startDate, $endDate])
        )->count();

        $ordersTotalSum = Order::whereHas('histories', fn ($query) => $query->whereBetween('action_time', [$startDate, $endDate])
        )->sum('order_grand_total');

        $ordersShippedTotalSum = Order::whereHas('histories', fn ($query) => $query->where('event_type', OrderStatusEnum::DELIVERED)
            ->whereBetween('action_time', [$startDate, $endDate])
        )->sum('order_grand_total');

        $ordersCodCount = Order::whereHas('histories', fn ($query) => $query->where('event_type', OrderStatusEnum::PENDING)
            ->whereBetween('action_time', [$startDate, $endDate])
        )->where('payment_method', PaymentMethodEnum::COD)
            ->count();

        $ordersCodeTotal = Order::whereHas('histories', fn ($query) => $query->whereBetween('action_time', [$startDate, $endDate])
        )->where('payment_method', PaymentMethodEnum::COD)
            ->sum('order_grand_total');

        $orderCodeTotalTreeqPrice = Order::whereHas('histories', fn ($query) => $query->where('event_type', OrderStatusEnum::DELIVERED)
            ->whereBetween('action_time', [$startDate, $endDate])
        )->where('payment_method', PaymentMethodEnum::COD)
            ->withApplication()
            ->sum('order_grand_total');

        $orderCodeTotalNotTreeqPrice = Order::whereHas('histories', fn ($query) => $query->where('event_type', OrderStatusEnum::DELIVERED)
            ->whereBetween('action_time', [$startDate, $endDate])
        )->where('payment_method', PaymentMethodEnum::COD)
            ->withoutApplication()
            ->sum('order_grand_total');

        return [
            Stat::make('new_orders', $pendingOrdersTotal)
                ->label(__('translation.new_orders'))
                ->color('info')
                ->description(__('translation.show_more'))
                ->url(route('filament.merchant.resources.orders.index', ['activeTab' => 'pending']))
                ->descriptionIcon('heroicon-m-arrow-left')
                ->icon('heroicon-m-circle-stack'),

            Stat::make('waiting_payment', $ordersCodCount)
                ->label(__('translation.waiting_payment'))
                ->color('info')
                ->description(__('translation.show_more'))
                ->url(route('filament.merchant.resources.orders.index', ['activeTab' => 'pending']))
                ->descriptionIcon('heroicon-m-arrow-left')
                ->icon('heroicon-m-arrow-right-start-on-rectangle'),

            Stat::make('shipped', $shippedOrdersCount)
                ->label(__('translation.shipped'))
                ->color('info')
                ->description(__('translation.show_more'))
                ->url(route('filament.merchant.resources.orders.index', ['activeTab' => 'pending']))
                ->descriptionIcon('heroicon-m-arrow-left')
                ->icon('heroicon-m-pencil-square'),
            Stat::make('total_price', number_format($ordersTotalSum / 100, 2))
                ->label(__('translation.total_price'))
                ->color('info')
                ->description(__('translation.show_more'))
                ->url(route('filament.merchant.resources.orders.index', ['activeTab' => 'pending']))
                ->descriptionIcon('heroicon-m-arrow-left')
                ->icon('heroicon-m-minus-circle'),

            Stat::make('total_price_shipment', number_format($ordersCodeTotal / 100, 2))
                ->label(__('translation.total_price_shipment'))
                ->color('info')
                ->description(__('translation.show_more'))
                ->url(route('filament.merchant.resources.orders.index', ['tableSearch' => 'cod', 'activeTab' => 'all_orders']))
                ->descriptionIcon('heroicon-m-arrow-left')
                ->icon('heroicon-m-cube-transparent'),

            Stat::make('total_price_shipped', number_format($ordersShippedTotalSum / 100, 2))
                ->label(__('translation.total_price_shipped'))
                ->color('info')
                ->description(__('translation.show_more'))
                ->url(route('filament.merchant.resources.orders.index', ['activeTab' => 'pending']))
                ->descriptionIcon('heroicon-m-arrow-left')
                ->icon('heroicon-m-bolt'),

            Stat::make('total_price_shipment_treeq', number_format($orderCodeTotalTreeqPrice / 100, 2))
                ->label(__('translation.total_price_shipment_treeq'))
                ->color('info')
                ->description(__('translation.show_more'))
                ->url(route('filament.merchant.resources.orders.index', [
                    'tableSearch' => 'cod',
                    'activeTab' => 'delivered',
                    'tableFilters' => [
                        'shipment_credentials_type' => [
                            'value' => 'application',
                        ],
                    ],
                ]))
                ->descriptionIcon('heroicon-m-arrow-left')
                ->icon('heroicon-m-cube-transparent'),

            Stat::make('total_price_shipment_other', number_format($orderCodeTotalNotTreeqPrice / 100, 2))
                ->label(__('translation.total_price_shipment_other'))
                ->color('info')
                ->description(__('translation.show_more'))
                ->url(route('filament.merchant.resources.orders.index', [
                    'tableSearch' => 'cod',
                    'activeTab' => 'delivered',
                    'tableFilters' => [
                        'shipment_credentials_type' => [
                            'value' => 'not_application',
                        ],
                    ],
                ]))
                ->descriptionIcon('heroicon-m-arrow-left')
                ->icon('heroicon-m-cube-transparent'),
        ];
    }

    public function getColumns(): int
    {
        return 3;
    }
}
