<?php

namespace App\Filament\Merchant\Resources;

use App\Filament\Merchant\Resources\SalesChannelsResource\Pages;
use App\Models\SalesChannel;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;

class SalesChannelsResource extends Resource
{
    protected static ?string $model = SalesChannel::class;

    protected static ?string $navigationIcon = 'jeuxydnh.json';

    protected static ?string $navigationGroup = 'home';

    protected static bool $shouldRegisterNavigation = false;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                //
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                //
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListSalesChannels::route('/'),
            'create' => Pages\CreateSalesChannels::route('/create'),
            'edit' => Pages\EditSalesChannels::route('/{record}/edit'),
        ];
    }
}
