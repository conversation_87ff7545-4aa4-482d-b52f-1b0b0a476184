<?php

namespace App\Filament\Merchant\Resources\SallaMerchantResource\Pages;

use App\Enums\SalesChannelEnum;
use App\Filament\Merchant\Resources\SallaMerchantResource;
use App\Models\Warehouse;
use Filament\Forms\Components\Actions;
use Filament\Forms\Components\Component;
use Filament\Forms\Components\Hidden;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Components\View;
use Filament\Forms\Components\Wizard\Step;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\CreateRecord;

class CreateSallaMerchant extends CreateRecord
{
    use CreateRecord\Concerns\HasWizard;

    protected static string $resource = SallaMerchantResource::class;

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        $data['webhook_url'] = url('/api/webhook/salla/'.uniqid());

        return $data;
    }

    public function testConnectionAction(): void
    {
        Notification::make()
            ->title('Connection Successfull')
            ->success()
            ->send();
    }

    /**
     * @return array<int, Step>
     */
    protected function getSteps(): array
    {
        return [
            self::getInstructionsStep(),
            self::getCredentialsStep(),
        ];
    }

    protected static function getInstructionsStep(): Step
    {
        return Step::make('Instructions')->label(__('translation.instructions'))->translateLabel()
            ->schema([
                View::make('filament.merchant.pages.partials.order-information'),
            ]);
    }

    protected static function getCredentialsStep(): Step
    {
        return Step::make('Credentials')->label(__('translation.account_info'))->translateLabel()
            ->schema(array_merge(
                self::getCredentialsFields(),
                [self::getTestConnectionButton()]
            ));
    }

    /**
     * @return array<int, Component>
     */
    protected static function getCredentialsFields(): array
    {
        return [
            TextInput::make('name')
                ->label(__('translation.merchant_name'))
                ->translateLabel()
                ->required()
                ->maxLength(191),

            TextInput::make('domain')
                ->label(__('translation.domain'))
                ->maxLength(191),
            Hidden::make('type')->default(SalesChannelEnum::SALLA->value),
            Hidden::make('user_id')
                ->default(auth()->id()),

            Toggle::make('active')
                ->label(__('translation.active'))
                ->required(),
            TextInput::make('webhook_url')
                ->label(__('translation.webhook_url'))
                ->disabled(),
            Select::make('warehouse_id')
                ->nullable()
                ->label(__('translation.main_warehouse'))
                ->options(Warehouse::pluck('name', 'id')),
        ];
    }

    protected static function getTestConnectionButton(): Actions
    {
        return Actions::make([
            Actions\Action::make('Test Connection')
                ->label(__('translation.test_connection'))
                ->action('testConnectionAction'),
        ]);
    }
}
