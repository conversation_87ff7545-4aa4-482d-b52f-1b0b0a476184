<?php

declare(strict_types=1);

namespace App\Filament\Merchant\Resources;

use App\Enums\CourierIdentifierEnum;
use App\Enums\OrderStatusEnum;
use App\Enums\ReturnTypeEnum;
use App\Models\Box;
use App\Models\City;
use App\Models\Country;
use App\Models\Order;
use App\Models\Product;
use App\Models\User;
use App\Models\Warehouse;
use App\Services\InvoiceService;
use App\Services\ShipmentService;
use AymanAlhattami\FilamentContextMenu\Columns\ContextMenuTextColumn;
use AymanAlhattami\FilamentContextMenu\Traits\PageHasContextMenu;
use Filament\Forms;
use Filament\Forms\Components\Repeater;
use Filament\Forms\Components\Select;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Actions\Action;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use pxlrbt\FilamentExcel\Actions\Tables\ExportBulkAction;
use Vicmans\FilamentNumberInput\NumberInput;
use Webbingbrasil\FilamentAdvancedFilter\Filters\DateFilter;
use Webbingbrasil\FilamentAdvancedFilter\Filters\TextFilter;
use Ysfkaya\FilamentPhoneInput\Forms\PhoneInput;

class OrderResource extends Resource
{
    use PageHasContextMenu;

    /**
     * @var string[]
     */
    protected $queryString = [
        'tableSortColumn',
        'tableSortDirection',
        'tableSearchQuery' => ['except' => ''],
        'tableFilters',
    ];

    protected static ?string $model = Order::class;

    protected static ?string $navigationIcon = 'qofqbxez.json';

    protected static ?string $navigationGroup = 'home';

    protected static ?int $navigationSort = 2;

    public static string $currentTab = OrderStatusEnum::PENDING->value;

    public static function form(Form $form): Form
    {
        return $form->schema(static::getWizardSchema());
    }

    public static function table(Table $table): Table
    {
        self::getCurrentTab();

        return $table->paginated([10, 20, 50])
            ->deferLoading()
            ->modifyQueryUsing(function (Builder $query) {
                // Eager load relationships to prevent N+1 queries
                return $query->with([
                    'receiverCity:id,name,name_ar',
                    'receiverCountry:id,name,name_ar',
                    'warehouse:id,name',
                    'latestOrderHistory:id,event_type',
                ])
                    ->orderBy('date', 'desc');
            })
            ->checkIfRecordIsSelectableUsing(fn ($record) => ($record->error === __('translation.order_errors.no_error')))
            ->columns([
                Tables\Columns\TextColumn::make('order_number')->toggleable()->searchable(isIndividual: true)->translateLabel()->label('translation.order_number')->sortable()->copyable()->copyMessageDuration(1500)
                    ->extraAttributes(['style' => 'max-width:50px;'])->wrap(),
                TextColumn::make('error')->label(__('translation.errors'))->sortable()->badge()
                    ->color(function (string $state): string {
                        return $state !== __('translation.order_errors.no_error') ? 'danger' : 'primary';
                    }),
                Tables\Columns\TextColumn::make('shipment_company')->toggleable()
                    ->label(__('translation.shipment_company'))
                    ->hidden(fn () => self::$currentTab === OrderStatusEnum::PENDING->value)
                    ->formatStateUsing(fn ($state) => '<img src="'.asset('build/images/'.$state.'-big.png').'" alt="Company" width="50" height="70" style="margin-right: 5px;">'.$state)->html(),
                ContextMenuTextColumn::make('shipment_reference')->toggleable()->copyable()->searchable(isIndividual: true)->translateLabel()->label('translation.shipment_reference')->sortable()
                    ->extraAttributes(['class' => 'shipment_reference'])
                    ->contextMenuActions(fn (Order $record) => [
                        Action::make('shipment_tracking_link')
                            ->label(__('translation.open_shipment_tracking_link'))
                            ->translateLabel()
                            ->url($record->shipment_tracking_link)
                            ->link()
                            ->openUrlInNewTab()
                            ->icon('heroicon-o-link'),
                    ])
                    ->visible(self::$currentTab !== OrderStatusEnum::PENDING->value),
                Tables\Columns\TextColumn::make('return_type')->searchable(isIndividual: true)
                    ->label('translation.return_type.return_type')->translateLabel()
                    ->formatStateUsing(function ($state, $record) {

                        $value = $record->return_type;

                        if ($value === ReturnTypeEnum::REVERSE_SHIPMENT->value) {
                            return trans('translation.return_type.'.ReturnTypeEnum::REVERSE_SHIPMENT->value);
                        }
                        if ($value === ReturnTypeEnum::SHIPMENT_COMPANY->value) {
                            return trans('translation.return_type.'.ReturnTypeEnum::SHIPMENT_COMPANY->value);
                        }
                    })
                    ->badge()
                    ->visible(self::$currentTab === OrderStatusEnum::RETURNED->value)
                    ->color(fn (string $state): string => match ($state) {
                        ReturnTypeEnum::REVERSE_SHIPMENT->value => 'primary',
                        ReturnTypeEnum::SHIPMENT_COMPANY->value => 'warning',
                    }),
                Tables\Columns\TextColumn::make('latestOrderHistory.event_type')
                    ->visible(self::$currentTab === OrderStatusEnum::RETURNED->value)
                    ->label('translation.status')->translateLabel()
                    ->formatStateUsing(fn ($state) => trans("translation.order_status.$state"))
                    ->searchable(isIndividual: true),
                Tables\Columns\TextColumn::make('shipper_name')->toggleable()->searchable(isIndividual: true)->label(__('translation.store'))->sortable(),
                Tables\Columns\TextColumn::make('receiver_phone')->toggleable()->searchable(isIndividual: true)->label(__('translation.receiver_phone'))->sortable(),
                Tables\Columns\TextColumn::make('receiverCity.name')->extraAttributes(['style' => 'max-width:50px;'])->toggleable(isToggledHiddenByDefault: true)->searchable(isIndividual: true)->label(__('translation.city'))->sortable(),
                Tables\Columns\TextColumn::make('ReceiverCountry.name')->extraAttributes([
                    'style' => 'max-width:70px;
  overflow: hidden;
  text-decoration: none;
  text-overflow: ellipsis;
  white-space: nowrap;',
                ])->extraAttributes(['style' => 'max-width:50px;'])->toggleable(isToggledHiddenByDefault: true)->searchable(isIndividual: true)->label(__('translation.receiver_country'))->sortable(),
                Tables\Columns\TextColumn::make('date')->toggleable()->searchable(isIndividual: true)->label('translation.order_date')->translateLabel()->dateTime('Y-m-d')->sortable()
                    ->extraAttributes([
                        'style' => 'max-width:150px;',
                    ])->wrap(),
                Tables\Columns\TextColumn::make('order_time')->toggleable()->label('translation.order_time')->translateLabel()
                    ->extraAttributes([
                        'style' => 'max-width:100px;',
                    ])->wrap(),
                Tables\Columns\TextColumn::make('receiver_full_name')->extraAttributes(['style' => 'max-width:50px;'])->toggleable()->label('translation.receiver_first_name')->translateLabel()->numeric()
                    ->searchable([
                        'receiver_first_name', 'receiver_last_name',
                    ], isIndividual: true),
                Tables\Columns\TextColumn::make('receiver_address_line')->toggleable()->searchable(isIndividual: true)->label('translation.receiver_address_line')->translateLabel()->numeric()->sortable()
                    ->extraAttributes([
                        'style' => 'max-width:170px;
  overflow: hidden;
  text-decoration: none;
  text-overflow: ellipsis;
  white-space: nowrap;',
                    ]),
                Tables\Columns\TextColumn::make('order_grand_total')->toggleable()->searchable(isIndividual: true)->label('translation.order_grand_total')->translateLabel()->numeric()->sortable(),
                Tables\Columns\TextColumn::make('payment_method')->toggleable()->label(__('translation.payment_method'))->searchable(isIndividual: true),

            ])
            ->filters([
                Tables\Filters\SelectFilter::make('status')
                    ->label('translation.status')->translateLabel()
                    ->options([
                        'pending' => trans('translation.pending_orders'),
                        'awaiting_pickup' => trans('translation.awaiting_pickup'),
                        'currently_shipping' => trans('translation.currently_shipping'),
                        'shipment_on_hold' => trans('translation.shipment_on_hold'),
                        'returned' => trans('translation.returned'),
                        'canceled_orders' => trans('translation.canceled_orders'),
                        'delivered' => trans('translation.delivered'),
                    ])
                    ->placeholder('الكل'),
                TextFilter::make('order_number')->label('translation.order_number')->translateLabel(),
                TextFilter::make('receiver_city')->label('translation.receiver_city')->translateLabel(),
                TextFilter::make('receiver_name')->label('translation.receiver_first_name')->translateLabel(),
                DateFilter::make('date')->translateLabel()->label('translation.date'),
                TextFilter::make('order_grand_total')->translateLabel()->label('translation.order_grand_total'),
                SelectFilter::make('shipment_credentials_type')
                    ->label(__('translation.shipment_credentials_type'))
                    ->options([
                        'application' => __('translation.with_application'),
                        'not_application' => __('translation.without_application'),
                    ])
                    ->query(function ($query, $data) {
                        if ($data['value'] === 'application') {
                            return $query->withApplication();
                        }
                        if ($data['value'] === 'not_application') {
                            return $query->withoutApplication();
                        }
                    }),
            ])
            ->recordAction(null)
//            ->headerActions(self::getHeaderActions())
            ->selectable()
            ->headerActionsPosition(Tables\Actions\HeaderActionsPosition::Bottom)
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make()
                    ->mutateFormDataUsing(function (array $data, $record): array {
                        if ($data['warehouse_id']) {
                            $record->setWarehouse((int) $data['warehouse_id']);

                        }

                        return $data;
                    }),
            ])
            ->bulkActions(self::getHeaderActions());
    }

    public static function getRelations(): array
    {
        return [];
    }

    public static function getPages(): array
    {
        return [
            'index' => OrderResource\Pages\ListOrders::route('/'),
        ];
    }

    public static function getNavigationLabel(): string
    {
        return __('translation.shipments');
    }

    public static function getLabel(): ?string
    {
        return __('translation.order');
    }

    /**
     * @return array<mixed>
     */
    protected static function getWizardSchema(?Order $order = null): array
    {
        return [
            Forms\Components\Wizard::make([
                Forms\Components\Wizard\Step::make(__('translation.receiver_info'))
                    ->schema([
                        Forms\Components\Hidden::make('status')
                            ->label(__('translation.status'))
                            ->default($order?->status ?? OrderStatusEnum::PENDING->value)
                            ->dehydratedWhenHidden(true)
                            ->required(),
                        Forms\Components\Grid::make(2)
                            ->schema([
                                Forms\Components\TextInput::make('receiver_first_name')
                                    ->label(__('translation.receiver_first_name'))
                                    ->default($order?->receiver_first_name)
                                    ->required()
                                    ->maxLength(191),
                                Forms\Components\TextInput::make('receiver_last_name')
                                    ->label(__('translation.receiver_last_name'))
                                    ->default($order?->receiver_last_name)
                                    ->required()
                                    ->maxLength(191),
                            ]),
                        Forms\Components\Grid::make(2)
                            ->schema([
                                PhoneInput::make('receiver_phone')
                                    ->label(__('translation.mobile_number'))
                                    ->initialCountry('sa')
                                    ->default($order?->receiver_phone)
                                    ->required(),
                                Forms\Components\TextInput::make('receiver_email')
                                    ->label(__('translation.email'))
                                    ->default($order?->receiver_email)
                                    ->email()
                                    ->maxLength(191),
                                Forms\Components\TextInput::make('receiver_country')
                                    ->label(__('translation.receiver_country_from_sale_channel'))
                                    ->default($order?->receiver_country)
                                    ->visible(fn ($record) => $record->receiver_country_id === null)
                                    ->maxLength(191)
                                    ->readOnly()
                                    ->disabled(),
                                Forms\Components\TextInput::make('receiver_city')
                                    ->label(__('translation.receiver_city_from_sale_channel'))
                                    ->default($order?->receiver_city)
                                    ->visible(fn ($record) => $record->receiver_city_id === null)
                                    ->maxLength(191)
                                    ->readOnly()
                                    ->disabled(),
                            ]),
                        Forms\Components\TextInput::make('receiver_address_line')
                            ->label(__('translation.address_details'))
                            ->default($order?->receiver_address_line)
                            ->required(),
                        Forms\Components\Grid::make(2)
                            ->schema([
                                Forms\Components\Select::make('receiver_country_id')
                                    ->label(__('translation.country'))
                                    ->options(fn () => Country::query()
                                        ->select('id', 'name_ar', 'name')
                                        ->get()
                                        ->mapWithKeys(fn ($country) => [$country->id => $country->translatedName])
                                        ->toArray()
                                    )
                                    ->required()
                                    ->reactive()->afterStateUpdated(function (callable $set) {
                                        $set('receiver_city_id', null);
                                    }),

                                Forms\Components\Select::make('receiver_city_id')
                                    ->label(__('translation.city'))
                                    ->required()
                                    ->searchable()
                                    ->reactive()
                                    ->options(function (callable $get) {
                                        $countryId = $get('receiver_country_id');
                                        if ($countryId) {
                                            return City::query()
                                                ->select('id', 'name_ar', 'name')
                                                ->where('country_id', $countryId)
                                                ->get()
                                                ->filter(fn ($city) => filled($city->translatedName))
                                                ->mapWithKeys(fn ($city) => [$city->id => $city->translatedName])
                                                ->toArray();
                                        }

                                        return [];
                                    })
                                    ->default($order?->receiver_city_id),
                            ]),
                        Forms\Components\Grid::make(2)
                            ->schema([
                                Forms\Components\TextInput::make('receiver_street_name')
                                    ->label(__('translation.street_name'))
                                    ->default($order?->receiver_street_name)
                                    ->maxLength(191),
                                Forms\Components\TextInput::make('receiver_block')
                                    ->label(__('translation.building_name_or_number'))
                                    ->default($order?->receiver_block)
                                    ->maxLength(191),
                            ]),
                        Forms\Components\Grid::make(2)
                            ->schema([
                                Forms\Components\TextInput::make('receiver_postal_code')
                                    ->label(__('translation.postal_code'))
                                    ->default($order?->receiver_postal_code)
                                    ->maxLength(191),
                            ]),
                    ]),
                Forms\Components\Wizard\Step::make(__('translation.order_info'))
                    ->schema([
                        Forms\Components\Grid::make(2)->schema([
                            Forms\Components\TextInput::make('order_number')
                                ->label(__('translation.order_numberr'))
                                ->default($order?->order_number)
                                ->required()
                                ->reactive()
                                ->hintAction(Forms\Components\Actions\Action::make('Generate')
                                    ->label(__('translation.generate'))
                                    ->color('primary')
                                    ->icon('heroicon-m-clipboard')
                                    ->action(function (callable $set) {
                                        $generatedOrderNumber = now()->timestamp.rand(1000, 9999);
                                        $set('order_number', $generatedOrderNumber);
                                    })
                                )
                                ->maxLength(191),
                            Forms\Components\DateTimePicker::make('date')
                                ->label(__('translation.order_date'))
                                ->default($order?->date ?? now())
                                ->disabled()
                                ->dehydrated(false)
                                ->live()
                                ->reactive()
                                ->required(),
                        ]),

                        Forms\Components\Grid::make(2)->schema([
                            Forms\Components\Select::make('payment_method')
                                ->label(__('translation.payment_method'))
                                ->required()
                                ->default($order?->payment_method)
                                ->options([
                                    'paid' => trans('translation.paid'),
                                    'cod' => trans('translation.cod'),
                                ]),
                            Forms\Components\TextInput::make('order_grand_total')
                                ->label(__('translation.order_value'))
                                ->required()
                                ->default($order?->order_grand_total)
                                ->numeric(),
                            Forms\Components\TextInput::make('shipment_total_weight')
                                ->label(__('translation.shipment_total_weight'))
                                ->disabled()
                                ->readOnly()
                                ->formatStateUsing(fn ($state) => $state !== null ? ceil($state / 1000) : null),
                        ]),
                        Forms\Components\TextInput::make('description')->label(__('translation.description'))
                            ->maxLength(191),
                        Forms\Components\Repeater::make('items')
                            ->relationship('items')
                            ->label(__('translation.products'))
                            ->schema([
                                Forms\Components\Select::make('product_id')
                                    ->label(__('translation.product_name'))
                                    ->default($order?->items->first()?->product_id)
                                    ->options(fn () => Product::query()
                                        ->select('id', 'name')
                                        ->pluck('name', 'id')
                                        ->toArray()
                                    )
                                    ->searchable()
                                    ->dehydrated(false)
                                    ->reactive()
                                    ->afterStateUpdated(function (callable $set, $state) {
                                        /** @var Product|null $product */
                                        $product = Product::select('id', 'sku', 'price', 'name', 'total_weight')
                                            ->find($state);
                                        if ($product) {
                                            $set('sku', $product->sku);
                                            $set('price', $product->price);
                                            $set('name', $product->name);
                                            $set('weight', $product->total_weight);
                                            $set('total_price', $product->price * 1.15);
                                        }
                                    }),
                                Forms\Components\TextInput::make('sku')
                                    ->label(__('translation.sku')),
                                Forms\Components\TextInput::make('weight')
                                    ->label(__('translation.weight'))
                                    ->numeric(),
                                Forms\Components\TextInput::make('name')
                                    ->label(__('translation.product.name'))
                                    ->maxLength(191),
                                Forms\Components\TextInput::make('quantity')
                                    ->label(__('translation.quantity'))
                                    ->default(1)
                                    ->required()
                                    ->numeric()
                                    ->reactive()
                                    ->afterStateUpdated(function (callable $set, $get) {
                                        $price = floatval($get('price'));
                                        $quantity = intval($get('quantity'));
                                        if ($price && $quantity) {
                                            $set('total_price', ($price * 1.5) * $quantity);
                                        }
                                    }),
                                Forms\Components\TextInput::make('price')
                                    ->label(__('translation.price'))
                                    ->reactive()
                                    ->afterStateUpdated(function (callable $set, $get) {
                                        $price = floatval($get('price'));
                                        $quantity = intval($get('quantity'));
                                        if ($price && $quantity) {
                                            $set('total_price', ($price * 1.15) * $quantity);
                                        }
                                    })
                                    ->formatStateUsing(fn ($state) => number_format($state / 100, 2))
                                    ->dehydrateStateUsing(fn ($state) => (int) (floatval(str_replace(',', '', (string) $state)) * 100)),
                                Forms\Components\TextInput::make('total_price')
                                    ->label(__('translation.total_with_tax'))
                                    ->formatStateUsing(fn ($state) => number_format($state / 100, 2))
                                    ->dehydrateStateUsing(fn (string $state) => (int) (floatval(str_replace(',', '', $state)) * 100))
                                    ->readOnly(),

                            ])
                            ->columns(6)
                            ->collapsible()
                            ->createItemButtonLabel(__('translation.add_new_row')),

                        Repeater::make('boxes')
                            ->relationship('boxes')
                            ->label(__('translation.boxes'))
                            ->schema([
                                Select::make('box_id')
                                    ->label(__('translation.box_type'))
                                    ->default($order?->boxes->first()?->type)
                                    ->options(fn () => Box::query()
                                        ->select('id', 'type')
                                        ->pluck('type', 'id')
                                        ->toArray()
                                    )
                                    ->reactive()
                                    ->afterStateUpdated(function ($state, callable $set) {
                                        if ($state) {
                                            $box = Box::select('id', 'length', 'width', 'height')
                                                ->find($state);
                                            if ($box) {
                                                $set('length', $box->length);
                                                $set('width', $box->width);
                                                $set('height', $box->height);
                                            }
                                        }
                                    }),
                                NumberInput::make('length')
                                    ->label(__('translation.length'))
                                    ->minValue(1)
                                    // ->afterStateUpdated(fn ($state, callable $set) => $set('length', max(1, (int) $state)))
                                    ->default($order?->boxes->first()?->length ?? 10)
                                    ->required(),
                                NumberInput::make('width')
                                    ->label(__('translation.width'))
                                    ->minValue(1)
                                    // ->afterStateUpdated(fn ($state, callable $set) => $set('width', max(1, (int) $state)))
                                    ->default($order?->boxes->first()?->width ?? 10)
                                    ->required(),
                                NumberInput::make('height')
                                    ->label(__('translation.height'))
                                    ->minValue(1)
                                    // ->afterStateUpdated(fn ($state, callable $set) => $set('height', max(1, (int) $state)))
                                    ->default($order?->boxes->first()?->height ?? 10)
                                    ->required(),
                                NumberInput::make('weight')
                                    ->minValue(1)
                                    ->label(__('translation.weight'))
                                    ->hint(__('translation.units.kg'))
                                    ->required(),
                            ])
                            ->columns(5),
                        Forms\Components\Select::make('warehouse_id')
                            ->required()
                            ->label('translation.warehouse_name')
                            ->translateLabel()
                            ->relationship(name: 'warehouse', titleAttribute: 'name',
                                modifyQueryUsing: fn (\Illuminate\Contracts\Database\Eloquent\Builder $query) => $query->where('status', 1)),
                    ]),
            ])->columnSpanFull(),
        ];
    }

    private static function getCurrentTab(): void
    {
        self::$currentTab = OrderStatusEnum::PENDING->value;

        // Check if activeTab is in the current request query parameters
        if (request()->has('activeTab')) {
            self::$currentTab = request()->query('activeTab');

            return;
        }

        // Fallback to HTTP_REFERER if activeTab is not in current request
        // Use a try-catch block to handle any issues with HTTP_REFERER parsing
        try {
            $referer = request()->header('Referer');

            // If not found in headers, try server variables as fallback
            if (empty($referer) && request()->server('HTTP_REFERER')) {
                $referer = request()->server('HTTP_REFERER');
            }

            // Try to get the full URL including query parameters
            $fullUrl = request()->fullUrl();

            if (! empty($referer)) {
                // Parse the URL into components
                $url = parse_url($referer);

                // Check if the URL has query parameters
                if (is_array($url) && isset($url['query'])) {
                    // Parse the query string into an array
                    parse_str($url['query'], $query);

                    // Check if activeTab exists in the query parameters
                    if (is_array($query) && isset($query['activeTab'])) {
                        self::$currentTab = $query['activeTab'];

                        return;
                    }
                } else {

                    // Try to use the current request's URL as a fallback
                    $currentUrl = request()->fullUrl();
                    $currentUrlParsed = parse_url($currentUrl);

                    if (is_array($currentUrlParsed) && isset($currentUrlParsed['query'])) {
                        parse_str($currentUrlParsed['query'], $currentQuery);

                        if (is_array($currentQuery) && isset($currentQuery['activeTab'])) {
                            self::$currentTab = $currentQuery['activeTab'];

                            return;
                        }
                    }
                }
            }
        } catch (\Exception $e) {
            // Log the error but continue with default tab
            \Illuminate\Support\Facades\Log::warning('Error parsing referer for activeTab: '.$e->getMessage());
        }

        // Default to PENDING if no activeTab is found
        self::$currentTab = OrderStatusEnum::PENDING->value;
    }

    protected static function getHeaderActions(): array
    {
        self::getCurrentTab();

        return [
            Tables\Actions\BulkActionGroup::make([
                Tables\Actions\BulkAction::make('shipment_with_treek_rates')
                    ->label(__('translation.shipment_with_treek_rates'))
                    ->icon('heroicon-m-rocket-launch')
                    ->modalHeading(__('translation.shipment_with_treek_rates'))
                    ->disabled(self::$currentTab === 'all_orders')
                    ->modal(function (Collection $selectedRecords) {
                        return $selectedRecords->count() > 1;
                    })
                    ->action(function (Collection $selectedRecords) {
                        if ($selectedRecords->count() === 1) {
                            $selectedOrderId = $selectedRecords->first()->id;

                            return redirect()->route('filament.merchant.pages.price-calculator', [
                                'order_id' => $selectedOrderId,
                            ]);
                        }
                        if ($selectedRecords->count() > 50) {
                            Notification::make()
                                ->title(__('translation.select_max_50_orders'))
                                ->danger()
                                ->send();

                            return null;
                        }
                    })
                    ->modalSubmitAction(false)
                    ->modalCancelAction(false)
                    ->modalWidth('4xl')
                    ->modalContent(function (Collection $selectedRecords) {
                        foreach ($selectedRecords as $selectedRecord) {
                            $selectedRecord->selected_shipment_company = CourierIdentifierEnum::ARAMEX->value;
                            $selectedRecord->save();
                        }

                        return view('components.shipment-treek-rates-modal', [
                            'orders' => $selectedRecords,
                        ]);
                    }),
                Tables\Actions\BulkAction::make('shipment_with_my_rates')
                    ->label(__('translation.shipment_my_rates'))
                    ->icon('heroicon-m-rocket-launch')
                    ->disabled(self::$currentTab === 'all_orders')
                    ->modalHeading(__('translation.custom_shipping_rates'))
                    ->modalWidth('7xl')
                    ->modalCancelAction(false)
                    ->modalSubmitAction(false)
                    ->modalHidden(function (Collection $selectedRecords) {
                        $preventModal = false;
                        if ($selectedRecords->count() > 50) {
                            Notification::make()
                                ->title(__('translation.select_max_50_orders'))
                                ->danger()
                                ->send();
                            $preventModal = true;
                        }
                        if (Auth::user()->shipments()->count() < 1) {
                            Notification::make()
                                ->title(__('translation.must_relate_shipping_cs_first'))
                                ->danger()
                                ->send();
                            $preventModal = true;
                        }

                        return $preventModal;
                    })
                    ->modalContent(function (Collection $selectedRecords) {
                        $ordersCount = $selectedRecords->count();
                        foreach ($selectedRecords as $selectedRecord) {
                            $selectedRecord->selected_shipment_company = CourierIdentifierEnum::ARAMEX->value;
                            $selectedRecord->save();
                        }

                        return view('components.shipment-custom-rates-modal', [
                            'orders' => $selectedRecords,
                            'ordersCount' => $ordersCount,
                        ]);
                    }),
            ])
                ->hidden(self::$currentTab !== OrderStatusEnum::PENDING->value)
                ->icon('heroicon-m-rocket-launch')
                ->color('primary')
                ->button()
                ->label(__('translation.create_shipment')),
            Tables\Actions\BulkActionGroup::make([
                Tables\Actions\BulkAction::make('reverse_shipment_with_treek_rates')
                    ->label(__('translation.reverse_shipment_with_treek_rates'))
                    ->icon('heroicon-m-rocket-launch')
                    ->disabled(self::$currentTab === 'all_orders')
                    ->modalHidden(function (Collection $selectedRecords) {
                        if ($selectedRecords->count() > 1) {
                            Notification::make()
                                ->title(__('translation.select_max_1_orders'))
                                ->danger()
                                ->send();

                            return true;
                        }
                    })
                    ->modalWidth('7xl')
                    ->modalSubmitAction(false)
                    ->modalCancelAction(false)
                    ->modalContent(function (Collection $selectedRecords) {
                        foreach ($selectedRecords as $selectedRecord) {
                            $selectedRecord->selected_shipment_company = CourierIdentifierEnum::ARAMEX->value;
                            $selectedRecord->save();
                        }

                        return view('components.reverse-shipment-treek-rates-modal', [
                            'orders' => $selectedRecords,
                        ]);
                    }),
                Tables\Actions\BulkAction::make('reverse_shipment_my_rates')
                    ->label(__('translation.reverse_shipment_my_rates'))
                    ->disabled(self::$currentTab === 'all_orders')
                    ->modalHeading(__('translation.custom_shipping_rates'))
                    ->action(function (Collection $selectedRecords, array $data) {
                        if ($selectedRecords->count() > 50) {
                            Notification::make()
                                ->title(__('translation.select_max_50_orders'))
                                ->danger()
                                ->send();

                            return;
                        }
                        $shipmentService = new ShipmentService;
                        /** @var User $user */
                        $user = Auth()->user();

                        try {
                            foreach ($selectedRecords as $record) {
                                /** @var Order $order */
                                $order = Order::findOrFail($record->id);
                                $shipmentService->createReverseShipment(order: $order, createdWithGlobalConfig: false, courier: $order->shipment_company);

                                // Save the applied rate deduction to the order for historical tracking
                                $order->update(['applied_rate_cost' => $user->rate_cost]);

                                // Only process wallet transactions if shipment was successful
                                $user->decreaseWalletBalance($user->rate_cost);
                                $user->save();
                                // Record wallet transaction
                                $user->walletTransactions()->create([
                                    'type' => 'debit',
                                    'amount' => $user->rate_cost,
                                    'description' => 'رسوم شحنة'.$order->shipment_company,
                                    'order_id' => $order->id,
                                ]);
                            }

                            // Show success notification if all shipments were created successfully
                            Notification::make()
                                ->title(__('translation.reverse_shipment_created_successfully'))
                                ->success()
                                ->send();

                        } catch (\Exception $e) {
                            // Show error notification and return to close the modal
                            Notification::make()
                                ->title(__('translation.failed_reverse_shipment_creation', ['message' => $e->getMessage()]))
                                ->danger()
                                ->send();

                            return;
                        }
                    })
                    ->modalWidth('7xl')
                    ->modalContent(function (Collection $selectedRecords) {
                        foreach ($selectedRecords as $selectedRecord) {
                            $selectedRecord->selected_shipment_company = CourierIdentifierEnum::ARAMEX->value;
                            $selectedRecord->save();
                        }

                        return view('components.reverse-shipment-custom-rates-modal', [
                            'orders' => $selectedRecords,
                        ]);
                    }),
            ])
                ->visible(self::$currentTab === OrderStatusEnum::DELIVERED->value)
                ->icon('heroicon-m-rocket-launch')
                ->color('primary')
                ->button()
                ->label(__('translation.create_shipment')),
            Tables\Actions\BulkActionGroup::make([
                Tables\Actions\BulkAction::make('label')
                    ->disabled(self::$currentTab === OrderStatusEnum::PENDING->value || self::$currentTab === 'all_orders')
                    ->label(__('translation.shipment_awb'))
                    ->icon('heroicon-o-printer')
                    ->modal()
                    ->modalActions([])
                    ->modalFooterActions([])
                    ->modalWidth('6xl')
                    ->modalSubmitAction(false)
                    ->modalCancelAction(false)
                    ->modalContent(function (Collection $selectedRecords) {
                        $shipmentService = new ShipmentService;
                        $mergedPdfPath = $shipmentService->printLabels(orders: $selectedRecords);

                        return view('components.pdf-viewer', [
                            'url' => Storage::disk('public')->url($mergedPdfPath),
                        ]);
                    }),
                Tables\Actions\BulkAction::make('proforma_invoice')
                    ->disabled(self::$currentTab === OrderStatusEnum::PENDING->value || self::$currentTab === 'all_orders')
                    ->label(__('translation.proforma_invoice'))
                    ->icon('heroicon-o-document-text')
                    ->modal()
                    ->modalActions([])
                    ->modalFooterActions([])
                    ->modalWidth('6xl')
                    ->modalSubmitAction(false)
                    ->modalCancelAction(false)
                    ->modalContent(function (Collection $selectedRecords) {
                        $invoiceService = new InvoiceService;
                        try {
                            $mergedPdfPath = $invoiceService->generateProformaInvoice($selectedRecords);

                            return view('components.pdf-viewer', [
                                'url' => Storage::disk('public')->url($mergedPdfPath),
                            ]);
                        } catch (\Exception $e) {
                            Notification::make()
                                ->title(__('translation.failed_to_generate_invoice', ['message' => $e->getMessage()]))
                                ->danger()
                                ->send();

                            return null;
                        }
                    }),
            ])
                ->icon('heroicon-o-inbox-stack')
                ->color('gray')
                ->button()
                ->label(__('translation.print')),
            Tables\Actions\BulkActionGroup::make([
                Tables\Actions\BulkAction::make('editOrder')
                    ->disabled(self::$currentTab !== OrderStatusEnum::PENDING->value || self::$currentTab === 'all_orders')
                    ->form(function (Collection $selectedRecords) {
                        $order = $selectedRecords->first();

                        return static::getWizardSchema($order);
                    })
                    ->modalWidth('5xl')
                    ->label(__('translation.edit_order'))
                    ->modalSubmitAction(false)
                    ->modalCancelAction(false)
                    ->icon('heroicon-o-pencil-square')
                    ->action(function (Collection $selectedRecords, array $data) {
                        /** @var Order $order */
                        $order = $selectedRecords->first();

                        // Update the order with new data
                        $order->update([
                            'order_number' => $data['order_number'],
                            'date' => $data['date'],
                            'merchant_id' => $data['merchant_id'],
                            'receiver_name' => $data['receiver_name'],
                            'receiver_address' => $data['receiver_address'],
                            'order_grand_total' => $data['order_grand_total'],
                        ]);

                        Notification::make()
                            ->title(__('translation.order_updated'))
                            ->success()
                            ->send();
                    }),
                Tables\Actions\BulkAction::make('cancelOrder')
                    ->disabled(self::$currentTab !== OrderStatusEnum::PENDING->value && self::$currentTab !== OrderStatusEnum::AWAITING_PICKUP->value || self::$currentTab === 'all_orders')
                    ->requiresConfirmation()
                    ->label(__('translation.cancel_order'))
                    ->icon('heroicon-o-lock-closed')
                    ->action(function (Collection $selectedRecords, array $data) {

                        /** @var Order $order */
                        foreach ($selectedRecords as $order) {
                            $order->cancelOrder();
                        }

                        Notification::make()
                            ->title(__('translation.order_updated'))
                            ->success()
                            ->send();
                    }),
                Tables\Actions\BulkAction::make('mark_as_delivered')
                    ->disabled(self::$currentTab !== OrderStatusEnum::PENDING->value || self::$currentTab === 'all_orders')
                    ->requiresConfirmation()
                    ->label(__('translation.mark_as_delivered'))
                    ->icon('heroicon-o-check-circle')
                    ->action(function (Collection $selectedRecords, array $data) {
                        /** @var Order $order */
                        foreach ($selectedRecords as $order) {
                            $order->markDelivered();
                        }

                        Notification::make()
                            ->title(__('translation.order_delivered'))
                            ->success()
                            ->send();
                    }),
                Tables\Actions\BulkAction::make('showOrder')
                    ->disabled(self::$currentTab === 'all_orders')
                    ->form(function (Collection $selectedRecords) {
                        $order = $selectedRecords->first();

                        return static::getWizardSchema($order);
                    })
                    ->modalWidth('5xl')
                    ->label(__('translation.view_order'))
                    ->icon('heroicon-o-eye')
                    ->modalSubmitAction(false)
                    ->modalCancelAction(false)
                    ->action(function (Collection $selectedRecords, array $data) {
                        /** @var Order $order */
                        $order = $selectedRecords->first();

                        // Update the order with new data
                        $order->update([
                            'order_number' => $data['order_number'],
                            'date' => $data['date'],
                            'merchant_id' => $data['merchant_id'],
                            'receiver_name' => $data['receiver_name'],
                            'receiver_address' => $data['receiver_address'],
                            'order_grand_total' => $data['order_grand_total'],
                        ]);

                        Notification::make()
                            ->title(__('translation.order_updated'))
                            ->success()
                            ->send();
                    }),
                Tables\Actions\BulkAction::make('reassignOrder')
                    ->disabled(self::$currentTab !== OrderStatusEnum::PENDING->value && self::$currentTab !== OrderStatusEnum::CANCELED->value && self::$currentTab !== OrderStatusEnum::AWAITING_PICKUP->value || self::$currentTab === 'all_orders')
                    ->label(__('translation.reassign_order'))
                    ->icon('heroicon-o-printer')
                    ->requiresConfirmation()
                    ->form([
                        Forms\Components\Select::make('warehouse')
                            ->label(__('translation.warehouse'))
                            ->options(Warehouse::where('status', 1)->pluck('name', 'id'))
                            ->required(),
                    ])
                    ->action(function (Collection $selectedRecords, array $data) {
                        foreach ($selectedRecords as $record) {
                            /** @var Order $order */
                            $order = Order::findOrFail($record->id);

                            $order->update([
                                'warehouse_id' => $data['warehouse'],
                            ]);
                            $order->setWarehouse((int) $data['warehouse']);
                            $order->resetOrder();
                            $order->save();
                        }

                        Notification::make()
                            ->title(__('translation.order_reassigned_successfully'))
                            ->success()
                            ->send();
                    }),
                Tables\Actions\BulkAction::make('duplicateOrder')
                    ->label(__('translation.duplicate_order'))
                    ->icon('heroicon-o-pencil-square')
                    ->requiresConfirmation()
                    ->action(function (Collection $selectedRecords, array $data) {
                        if (count($selectedRecords) > 1) {
                            Notification::make()
                                ->title(__('translation.order_duplicate_error'))
                                ->danger()
                                ->send();

                            return;
                        }
                        /** @var Order $order */
                        $order = Order::findOrFail($selectedRecords->first()->id);
                        $order->duplicate();

                        Notification::make()
                            ->title(__('translation.order_duplicated_successfully'))
                            ->success()
                            ->send();
                    }),

            ])
                ->icon('heroicon-o-banknotes')
                ->color('gray')
                ->button()
                ->label(__('translation.Orders')),
            Tables\Actions\BulkActionGroup::make([
                Tables\Actions\BulkAction::make('showTable')
                    ->translateLabel()
                    ->disabled(self::$currentTab === 'all_orders')
                    ->label('translation.show_order_histories')
                    ->modalHeading(__('translation.order_histories'))
                    ->modalSubmitAction(false)
                    ->modalCancelAction(false)
                    ->modalWidth('7xl')
                    ->modalContent(function (Collection $selectedRecords) {
                        $selectedOrderId = $selectedRecords->first()->id;

                        return view('components.table-modal', [
                            'orderId' => $selectedOrderId,
                        ]);
                    }),
                Tables\Actions\BulkAction::make('editOrder')
                    ->form(function (Collection $selectedRecords) {
                        $order = $selectedRecords->first();

                        return static::getWizardSchema($order);
                    })
                    ->modalWidth('5xl')
                    ->label(__('translation.view_order_contents'))
                    ->modalSubmitAction(false)
                    ->modalCancelAction(false)
                    ->icon('heroicon-o-eye')
                    ->action(function (Collection $selectedRecords, array $data) {
                        /** @var Order $order */
                        $order = $selectedRecords->first();

                        // Update the order with new data
                        $order->update([
                            'order_number' => $data['order_number'],
                            'date' => $data['date'],
                            'merchant_id' => $data['merchant_id'],
                            'receiver_name' => $data['receiver_name'],
                            'receiver_address' => $data['receiver_address'],
                            'order_grand_total' => $data['order_grand_total'],
                        ]);

                        Notification::make()
                            ->title(__('translation.order_updated'))
                            ->success()
                            ->send();
                    }),
            ])
                ->icon('heroicon-o-banknotes')
                ->color('gray')
                ->button()
                ->label(__('translation.tools')),
            ExportBulkAction::make()
                ->color('gray'),
        ];
    }
}
