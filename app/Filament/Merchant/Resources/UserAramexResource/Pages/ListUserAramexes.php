<?php

namespace App\Filament\Merchant\Resources\UserAramexResource\Pages;

use App\Filament\Merchant\Resources\UserAramexResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListUserAramexes extends ListRecords
{
    protected static string $resource = UserAramexResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
