<?php

namespace App\Filament\Merchant\Resources;

use App\Filament\Merchant\Resources\WalletTransactionResource\Pages;
use App\Models\WalletTransaction;
use Barryvdh\Snappy\Facades\SnappyPdf;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use pxlrbt\FilamentExcel\Actions\Tables\ExportBulkAction;
use SimpleSoftwareIO\QrCode\Facades\QrCode;
use Symfony\Component\HttpFoundation\StreamedResponse;
use Webbingbrasil\FilamentAdvancedFilter\Filters\DateFilter;

class WalletTransactionResource extends Resource
{
    protected static ?string $model = WalletTransaction::class;

    protected static ?string $navigationIcon = 'pfefuxbw.json';

    protected static ?string $navigationGroup = 'home';

    protected static ?int $navigationSort = 11;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('type')
                    ->label(__('translation.type'))
                    ->required(),
                Forms\Components\TextInput::make('amount')
                    ->label(__('translation.amount'))
                    ->required()
                    ->numeric(),
                Forms\Components\TextInput::make('description')
                    ->label(__('translation.description'))
                    ->maxLength(191),
                Forms\Components\TextInput::make('order_id')
                    ->label(__('translation.order_number'))
                    ->required()
                    ->numeric(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->modifyQueryUsing(function (Builder $query) {
                return $query->orderBy('created_at', 'desc');
            })
            ->recordClasses(function (WalletTransaction $record) {
                return $record->type === 'debit' ? 'bg-red-50 hover:bg-red-100' : 'bg-green-50 hover:bg-green-100';
            })
            ->columns([
                Tables\Columns\TextColumn::make('type')
                    ->label(__('translation.type'))
                    ->formatStateUsing(fn ($state) => trans("translation.$state")),
                Tables\Columns\TextColumn::make('amount')
                    ->label(__('translation.amount'))
                    ->sortable()
                    ->formatStateUsing(fn (WalletTransaction $record) => $record->type === 'debit' ? $record->amount.' -' : $record->amount),
                Tables\Columns\TextColumn::make('description')
                    ->label(__('translation.description'))
                    ->searchable(),
                Tables\Columns\TextColumn::make('order.order_number')
                    ->label(__('translation.order_number'))
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('order.shipment_reference')
                    ->label(__('translation.shipment_reference'))
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->label(__('translation.created_at'))
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->label(__('translation.updated_at'))
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('order.id')
                    ->label(__('translation.order_id'))
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->searchable(),
                Tables\Columns\TextColumn::make('id')
                    ->label(__('translation.id'))
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->searchable(),
            ])
            ->filters([
                DateFilter::make('created_at')->translateLabel()->label('translation.created_at'),
                SelectFilter::make('sum_amount_per_order')
                    ->label(__('Sum of Debit Minus Cancel for Order'))
                    ->options([
                        'non_zero' => __('Exclude orders where sum = 0'),
                        'zero' => __('Only orders where sum = 0'),
                    ])
                    ->query(function (Builder $query, array $data) {
                        $value = $data['value'] ?? 'all';
                        if ($value === 'non_zero') {
                            $orderIds = WalletTransaction::select('order_id')
                                ->groupBy('order_id')
                                ->havingRaw('SUM(CASE WHEN type = ? THEN amount ELSE 0 END) - SUM(CASE WHEN type = ? THEN amount ELSE 0 END) != 0', ['debit', 'cancel'])
                                ->pluck('order_id');
                            $query->whereIn('order_id', $orderIds);
                        } elseif ($value === 'zero') {
                            $orderIds = WalletTransaction::select('order_id')
                                ->groupBy('order_id')
                                ->havingRaw('SUM(CASE WHEN type = ? THEN amount ELSE 0 END) - SUM(CASE WHEN type = ? THEN amount ELSE 0 END) = 0', ['debit', 'cancel'])
                                ->pluck('order_id');
                            $query->whereIn('order_id', $orderIds);
                        }

                        return $query;
                    }),
            ])
            ->headerActions([
                ExportBulkAction::make(),
            ])
            ->actions([
                Tables\Actions\Action::make('print_invoice')
                    ->label(__('translation.print'))
                    ->icon('heroicon-o-printer')
                    ->visible(fn ($record) => $record->type === 'credit')
                    ->action(fn ($record) => self::generateInvoice($record)),
            ])
            ->bulkActions([]);
    }

    public static function getRelations(): array
    {
        return [];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListWalletTransactions::route('/'),
        ];
    }

    public static function getNavigationLabel(): string
    {
        return __('translation.wallet_transactions');
    }

    public static function generateInvoice(WalletTransaction $record): StreamedResponse
    {
        // توليد بيانات QR
        $qrData = self::generateZatcaQrBase64(
            'شركة مسار تريك لتقنية المعلومات',                 // 1. اسم البائع
            '311250710800003',            // 2. الرقم الضريبي
            now()->toIso8601String(),     // 3. التاريخ
            number_format($record->amount, 2, '.', ''), // 4. الإجمالي
            number_format(($record->amount * 15) / 100, 2, '.', '') // 5. الضريبة
        );
        if (empty($qrData)) {
            $jsonMessage = json_encode([
                'error' => 'Invalid QR content',
                'qr_content' => $qrData,
                'user_id' => auth()->id(),
            ], JSON_UNESCAPED_UNICODE);
            if ($jsonMessage === false) {
                throw new \Exception('JSON encode failed: '.json_last_error_msg());
            }
            throw new \Exception($jsonMessage);
        }

        $qrCode = QrCode::format('png')->size(200)->generate($qrData);

        // @phpstan-ignore-next-line
        $qrImage = base64_encode($qrCode);

        // تمرير البيانات إلى الـ View
        $pdf = SnappyPdf::loadView('invoices.cod_wallet', [
            'wallet' => $record,
            'qrImage' => $qrImage,
        ]);

        return response()->streamDownload(
            fn () => print ($pdf->output()),
            "invoice_cod_wallet_{$record->id}.pdf"
        );
    }

    private static function generateZatcaQrBase64(string $sellerName, string $vatNumber, string $timestamp, string $invoiceTotal, string $vatTotal): string
    {
        $elements = [
            1 => $sellerName,
            2 => $vatNumber,
            3 => $timestamp,
            4 => $invoiceTotal,
            5 => $vatTotal,
        ];

        $tlv = '';

        foreach ($elements as $tag => $value) {
            $length = strlen($value);
            $tlv .= chr($tag).chr($length).$value;
        }

        return base64_encode($tlv);
    }
}
