<?php

namespace App\Filament\Merchant\Resources\OrderResource\Pages;

use App\Enums\OrderStatusEnum;
use App\Enums\WeightUnitsEnum;
use App\Filament\Merchant\Resources\OrderResource;
use App\Imports\OrderImport;
use App\Imports\SallaOrderImport;
use App\Models\Box;
use App\Models\City;
use App\Models\Country;
use App\Models\Merchant;
use App\Models\Order;
use App\Models\Product;
use App\Models\Warehouse;
use App\Services\SalesChannelService;
use EightyNine\ExcelImport\ExcelImportAction;
use EightyNine\ExcelImport\SampleExcelExport;
use Filament\Actions\CreateAction;
use Filament\Forms\Components\Actions\Action;
use Filament\Forms\Components\DateTimePicker;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Hidden;
use Filament\Forms\Components\Repeater;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Wizard;
use Filament\Notifications\Notification;
use Filament\Resources\Components\Tab;
use Filament\Resources\Pages\ListRecords;
use Filament\Tables\Actions\BulkActionGroup;
use Illuminate\Contracts\Database\Eloquent\Builder;
use Illuminate\Contracts\Support\Htmlable;
use Illuminate\Support\HtmlString;
use Termwind\Enums\Color;
use Vicmans\FilamentNumberInput\NumberInput;
use Ysfkaya\FilamentPhoneInput\Forms\PhoneInput;

class ListOrders extends ListRecords
{
    protected static string $resource = OrderResource::class;

    protected function getHeaderActions(): array
    {
        return [
            CreateAction::make()->translateLabel()->label('translation.add_order')->mutateFormDataUsing(function (array $data): array {
                /** @var Warehouse $selectedWarehouse */
                $selectedWarehouse = Warehouse::findOrFail($data['warehouse_id']);
                $data['shipper_name'] = $selectedWarehouse->name;
                $data['shipper_email'] = $selectedWarehouse->sender_email;
                $data['shipper_phone'] = $selectedWarehouse->sender_phone;
                $data['shipper_address_line'] = $selectedWarehouse->address;
                $data['shipper_city'] = $selectedWarehouse->city?->name ?? $selectedWarehouse->city?->name_ar ?? '';
                $data['shipment_total_weight'] = 1;

                return $data;
            })->steps(self::getWizardSchema()),
            BulkActionGroup::make([

                ExcelImportAction::make('upload_orders')
                    ->label(__('translation.upload_orders'))
                    ->use(OrderImport::class)
                    ->modalHeading(__('translation.upload_orders'))
                    ->modalDescription(new HtmlString(implode('<br>', [
                        __('translation.required_activated_warehouse'),
                        __('translation.upload_orders_description'),
                    ])))
                    ->color(Color::GRAY)
                    ->icon(null)
                    ->sampleExcel(
                        sampleData: [
                        ],
                        fileName: 'sample.xlsx',
                        exportClass: SampleExcelExport::class,
                        sampleButtonLabel: 'تحميل',
                        customiseActionUsing: fn (Action $action) => $action->color('secondary')
                            ->icon('heroicon-m-clipboard')
                            ->url(\URL::asset('order-import.xlsx'))
                            ->openUrlInNewTab()
                            ->requiresConfirmation(),
                    ),
                ExcelImportAction::make('upload_orders_salla')
                    ->label(__('translation.upload_orders_salla'))
                    ->use(SallaOrderImport::class)
                    ->modalHeading(__('translation.upload_orders_salla'))
                    ->modalDescription(new HtmlString(implode('<br>', [
                        __('translation.required_activated_warehouse'),
                        __('translation.upload_orders_salla_description'),
                    ])))
                    ->color(Color::GRAY)
                    ->icon(null),
            ])
                ->label(__('translation.upload_multiple_orders'))
                ->icon('heroicon-o-arrow-up-tray')
                ->color(Color::GRAY),
            \Filament\Actions\Action::make('findOrder')
                ->color(Color::GRAY)
                ->label(__('translation.sync_order'))
                ->modalHeading(__('translation.enter_order_details'))
                ->modalSubmitActionLabel(__('translation.save'))
                ->form([
                    TextInput::make('order_number')
                        ->label(__('translation.order_number'))
                        ->required()
                        ->placeholder(__('translation.enter_order_number')),
                    Select::make('merchant_id')
                        ->label(__('translation.merchant_name'))
                        ->required()
                        ->searchable()
                        ->options(Merchant::where('active', 1)->pluck('name', 'id')->toArray()),
                ])
                ->action(function (array $data) {
                    $orderNumber = $data['order_number'];
                    $merchantId = $data['merchant_id'];
                    $merchant = Merchant::findOrFail($merchantId);
                    try {
                        $salesChannelService = new SalesChannelService($merchant);
                        $salesChannelService->synchronizeOrder($orderNumber);

                    } catch (\Throwable $e) {
                        // Notification::make()
                        //     ->title(__('translation.order_sync_failed'))
                        //     ->body($e->getMessage()) // Or show a user-friendly message
                        //     ->danger()
                        //     ->send();

                        // Optional: log the full exception
                        \Log::error('Order sync failed: '.$e->getMessage(), ['exception' => $e]);
                    }

                    // Perform any logic with the entered order number and merchant

                    // Optionally, you could redirect to the order details page:
                    // return redirect()->route('filament.merchant.resources.orders.view', ['record' => $orderNumber]);
                }),
        ];
    }

    public function getTitle(): string|Htmlable
    {
        return new HtmlString('
        <div class="flex items-center">
<lord-icon
    src="https://cdn.lordicon.com/qofqbxez.json"
    trigger="hover"
    stroke="light"
    state="hover-load"
    colors="primary:#6c16c7,secondary:#6c16c7"
    style="width:50px;height:50px">
</lord-icon>
        <span>'.__('translation.shipments').'</span>
</div>
    ');
    }

    /**
     * @return array<mixed>
     */
    protected static function getWizardSchema(?Order $order = null): array
    {
        return [
            Wizard\Step::make(__('translation.receiver_info'))
                ->schema([
                    Hidden::make('status')
                        ->label(__('translation.status'))
                        ->default($order?->status ?? OrderStatusEnum::PENDING->value)
                        ->dehydratedWhenHidden(true)
                        ->required(),
                    Grid::make(2)
                        ->schema([
                            TextInput::make('receiver_first_name')
                                ->label(__('translation.receiver_first_name'))
                                ->default($order?->receiver_first_name)
                                ->required()
                                ->maxLength(191),
                            TextInput::make('receiver_last_name')
                                ->label(__('translation.receiver_last_name'))
                                ->default($order?->receiver_last_name)
                                ->required()
                                ->maxLength(191),
                        ]),
                    Grid::make(2)
                        ->schema([
                            PhoneInput::make('receiver_phone')
                                ->label(__('translation.mobile_number'))
                                ->initialCountry('sa')
                                ->default($order?->receiver_phone)
                                ->required(),
                            TextInput::make('receiver_email')
                                ->label(__('translation.email'))
                                ->default($order?->receiver_email)
                                ->email()
                                ->maxLength(191),
                        ]),
                    TextInput::make('receiver_address_line')
                        ->label(__('translation.address_details'))
                        ->default($order?->receiver_address_line)
                        ->required(),
                    Grid::make(2)
                        ->schema([
                            Select::make('receiver_country_id')
                                ->label(__('translation.country'))
                                ->options(Country::all()->pluck('translatedName', 'id'))
                                ->required()
                                ->reactive()->afterStateUpdated(function (callable $set) {
                                    $set('receiver_city_id', null);
                                }),

                            Select::make('receiver_city_id')
                                ->label(__('translation.city'))
                                ->required()
                                ->searchable()
                                ->reactive()
                                ->options(function (callable $get) {
                                    $countryId = $get('receiver_country_id');
                                    if ($countryId) {
                                        return City::where('country_id', $countryId)
                                            ->get()
                                            ->filter(fn ($city) => filled($city->translatedName))
                                            ->mapWithKeys(fn ($city) => [$city->id => $city->translatedName])
                                            ->toArray();
                                    }

                                    return [];
                                })
                                ->default($order?->receiver_city_id),
                        ]),
                    Grid::make(2)
                        ->schema([
                            TextInput::make('receiver_street_name')
                                ->label(__('translation.street_name'))
                                ->default($order?->receiver_street_name)
                                ->maxLength(191),
                            TextInput::make('receiver_block')
                                ->label(__('translation.building_name_or_number'))
                                ->default($order?->receiver_block)
                                ->maxLength(191),
                        ]),
                    Grid::make(2)
                        ->schema([
                            TextInput::make('receiver_postal_code')
                                ->label(__('translation.postal_code'))
                                ->default($order?->receiver_postal_code)
                                ->maxLength(191),
                        ]),
                ]),
            Wizard\Step::make(__('translation.order_info'))
                ->schema([
                    Grid::make(2)->schema([
                        TextInput::make('order_number')
                            ->label(__('translation.order_numberr'))
                            ->default($order?->order_number)
                            ->unique(table: Order::class, column: 'order_number')
                            ->required()
                            ->reactive()
                            ->hintAction(Action::make('Generate')
                                ->label(__('translation.generate'))
                                ->color('primary')
                                ->icon('heroicon-m-clipboard')
                                ->action(function (callable $set) {
                                    $generatedOrderNumber = now()->timestamp.rand(1000, 9999);
                                    $set('order_number', $generatedOrderNumber);
                                })
                            )
                            ->maxLength(191),
                        DateTimePicker::make('date')
                            ->label(__('translation.order_date'))
                            ->default($order?->date ?? now())
                            ->disabled()
                            ->dehydrateStateUsing(fn ($state) => now())
                            ->dehydrated()
                            ->live()
                            ->reactive()
                            ->required(),
                    ]),

                    Grid::make(2)->schema([
                        Select::make('payment_method')
                            ->label(__('translation.payment_method'))
                            ->required()
                            ->default($order?->payment_method)
                            ->options([
                                'paid' => trans('translation.paid'),
                                'cod' => trans('translation.cod'),
                            ]),
                        TextInput::make('order_grand_total')
                            ->label(__('translation.order_value'))
                            ->required()
                            ->default($order?->order_grand_total)
                            ->numeric(),
                    ]),
                    TextInput::make('description')->label(__('translation.description'))
                        ->maxLength(191),
                    Repeater::make('items')
                        ->relationship('items')
                        ->label(__('translation.products'))
                        ->schema([
                            Select::make('product_id')
                                ->label(__('translation.product_name'))
                                ->default($order?->items->first()?->product_id)
                                ->options(Product::pluck('name', 'id')->toArray())
                                ->searchable()
                                ->dehydrated(false)
                                ->reactive()
                                ->afterStateUpdated(function (callable $set, $state) {
                                    /** @var Product|null $product */
                                    $product = Product::find($state);
                                    if ($product) {
                                        $set('sku', $product->sku);
                                        $set('price', $product->price);
                                        $set('name', $product->name);
                                        $set('weight', $product->weight);
                                        $set('weight_unit', $product->weight_unit);
                                        $set('total_price', $product->price * 1.15);
                                    }
                                }),
                            TextInput::make('sku')
                                ->label(__('translation.sku'))
                                ->maxLength(191),
                            TextInput::make('weight')
                                ->label(__('translation.weight'))
                                ->numeric(),
                            Select::make('weight_unit')
                                ->label(__('translation.weight_unit'))
                                ->options(
                                    collect(WeightUnitsEnum::cases())
                                        ->mapWithKeys(fn ($case) => [$case->value => $case->label()])
                                        ->toArray()
                                ),
                            TextInput::make('name')
                                ->label(__('translation.product.name'))
                                ->maxLength(191),
                            TextInput::make('quantity')
                                ->label(__('translation.quantity'))
                                ->default(1)
                                ->required()
                                ->numeric()
                                ->reactive()
                                ->afterStateUpdated(function (callable $set, $get) {
                                    $price = $get('price');
                                    $quantity = $get('quantity');
                                    if ($price && $quantity) {
                                        $set('total_price', ($price * 1.5) * $quantity);
                                    }
                                }),
                            TextInput::make('price')
                                ->label(__('translation.price'))
                                ->numeric()
                                ->reactive()
                                ->afterStateUpdated(function (callable $set, $get) {
                                    $price = $get('price');
                                    $quantity = $get('quantity');
                                    if ($price && $quantity) {
                                        $set('total_price', ($price * 1.15) * $quantity);
                                    }
                                })
                                ->dehydrateStateUsing(fn ($state) => (int) (floatval(str_replace(',', '', $state)) * 100)),
                            TextInput::make('total_price')
                                ->label(__('translation.total_with_tax'))
                                ->formatStateUsing(fn ($state) => number_format($state / 100, 2))
                                ->readOnly(),

                        ])
                        ->columns(6)
                        ->collapsible()
                        ->createItemButtonLabel(__('translation.add_new_row')),

                    Repeater::make('boxes')
                        ->relationship('boxes')
                        ->label(__('translation.boxes'))
                        ->schema([
                            Select::make('box_id')
                                ->label(__('translation.box_type'))
                                ->default($order?->boxes->first()?->type)
                                ->options(Box::pluck('type', 'id'))
                                ->reactive()
                                ->afterStateUpdated(function ($state, callable $set) {
                                    if ($state) {
                                        $box = Box::find($state);
                                        if ($box) {
                                            $set('length', $box->length);
                                            $set('width', $box->width);
                                            $set('height', $box->height);
                                        }
                                    }
                                }),
                            NumberInput::make('length')
                                ->label(__('translation.length'))
                                ->default($order?->boxes->first()?->length ?? 10)
                                ->minValue(1)
                                // ->afterStateUpdated(fn ($state, callable $set) => $set('length', max(1, (int) $state)))
                                ->default(10)
                                ->required(),
                            NumberInput::make('width')
                                ->label(__('translation.width'))
                                ->minValue(1)
                                // ->afterStateUpdated(fn ($state, callable $set) => $set('width', max(1, (int) $state)))
                                ->default($order?->boxes->first()?->width ?? 10)
                                ->required(),
                            NumberInput::make('height')
                                ->label(__('translation.height'))
                                ->minValue(1)
                                // ->afterStateUpdated(fn ($state, callable $set) => $set('height', max(1, (int) $state)))
                                ->default($order?->boxes->first()?->height ?? 10)
                                ->required(),
                            NumberInput::make('weight')
                                ->minValue(1)
                                ->label(__('translation.weight'))
                                ->hint(__('translation.units.kg'))
                                ->required(),
                        ])
                        ->columns(5),
                    Select::make('warehouse_id')
                        ->required()
                        ->label('translation.warehouse_name')
                        ->translateLabel()
                        ->relationship(name: 'warehouse', titleAttribute: 'name',
                            modifyQueryUsing: fn (Builder $query) => $query->where('status', 1)),

                ]),
        ];
    }

    public function getTabs(): array
    {
        return [
            OrderStatusEnum::PENDING->value => Tab::make(__('translation.pending_orders'))->query(fn ($query) => $query->where('status', OrderStatusEnum::PENDING)),
            OrderStatusEnum::AWAITING_PICKUP->value => Tab::make(__('translation.awaiting_pickup'))->query(fn ($query) => $query->where('status', OrderStatusEnum::AWAITING_PICKUP))
                ->extraAttributes([
                    'data-reload-on-activate' => 'true',
                ]),
            OrderStatusEnum::CURRENTLY_SHIPPING->value => Tab::make(__('translation.currently_shipping'))->query(fn ($query) => $query->where('status', OrderStatusEnum::CURRENTLY_SHIPPING))
                ->extraAttributes([
                    'data-reload-on-activate' => 'true',
                ]),
            OrderStatusEnum::SHIPMENT_ON_HOLD->value => Tab::make(__('translation.shipment_on_hold'))->query(fn ($query) => $query->where('status', OrderStatusEnum::SHIPMENT_ON_HOLD))
                ->extraAttributes([
                    'data-reload-on-activate' => 'true',
                ]),
            OrderStatusEnum::DELIVERED->value => Tab::make(__('translation.delivered'))->query(fn ($query) => $query->where('status', OrderStatusEnum::DELIVERED))
                ->extraAttributes([
                    'data-reload-on-activate' => 'true',
                ]),
            OrderStatusEnum::RETURNED->value => Tab::make(__('translation.returned'))->query(fn ($query) => $query->where('status', OrderStatusEnum::RETURNED))
                ->extraAttributes([
                    'data-reload-on-activate' => 'true',
                ]),
            OrderStatusEnum::CANCELED->value => Tab::make(__('translation.canceled_orders'))->query(fn ($query) => $query->where('status', OrderStatusEnum::CANCELED))
                ->extraAttributes([
                    'data-reload-on-activate' => 'true',
                ]),
            'all_orders' => Tab::make(__('translation.all_orders'))
                ->extraAttributes([
                    'data-reload-on-activate' => 'true',
                ]),
        ];
    }
}
