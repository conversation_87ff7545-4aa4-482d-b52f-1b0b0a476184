<?php

namespace App\Filament\Merchant\Resources;

use App\Enums\SalesChannelEnum;
use App\Filament\Merchant\Resources\WooCommerceMerchantResource\Pages\CreateWooCommerceMerchant;
use App\Filament\Merchant\Resources\WooCommerceMerchantResource\Pages\EditWooCommerceMerchant;
use App\Models\Merchant;
use App\Models\Warehouse;
use Filament\Forms\Components\Actions;
use Filament\Forms\Components\Component;
use Filament\Forms\Components\Hidden;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Components\View;
use Filament\Forms\Components\Wizard;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;

class WooCommerceMerchantResource extends Resource
{
    protected static ?string $model = Merchant::class;

    protected static ?string $navigationLabel = 'WooCommerce Merchants';

    protected static bool $shouldRegisterNavigation = false;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                self::getWizard(),
            ]);
    }

    protected static function getWizard(): Wizard
    {
        return Wizard::make([
            self::getInstructionsStep(),
            self::getCredentialsStep(),
        ])->columnSpanFull()->skippable();
    }

    protected static function getInstructionsStep(): Wizard\Step
    {
        return Wizard\Step::make('Instructions')
            ->label(__('translation.instructions'))
            ->schema([
                View::make('filament.merchant.pages.partials.woo-commerce-information'),
            ]);
    }

    protected static function getCredentialsStep(): Wizard\Step
    {
        return Wizard\Step::make('Credentials')
            ->label(__('translation.account_info'))
            ->schema(array_merge(
                self::getCredentialsFields()
            ));
    }

    /**
     * @return array<int, Component>
     */
    protected static function getCredentialsFields(): array
    {
        return [
            TextInput::make('name')
                ->label(__('translation.name'))
                ->required()
                ->maxLength(191),

            Hidden::make('type')->default(SalesChannelEnum::WOOCOMMERCE->value),
            Hidden::make('user_id')
                ->default(auth()->id()),
            TextInput::make('domain')
                ->label(__('translation.domain'))
                ->maxLength(191),
            Toggle::make('active')
                ->label(__('translation.active'))
                ->required(),
            TextInput::make('webhook_url')
                ->label(__('translation.webhook_url'))
                ->disabled(),
            Textarea::make('api_key')
                ->label(__('translation.api_key'))
                ->columnSpanFull()->rows(3),
            Textarea::make('api_secret_key')
                ->label(__('translation.api_secret_key'))
                ->columnSpanFull()->rows(3),
            Select::make('warehouse_id')
                ->nullable()
                ->label(__('translation.main_warehouse'))
                ->options(Warehouse::pluck('name', 'id')),
        ];
    }

    protected static function getTestConnectionButton(): Actions
    {
        return Actions::make([
            Actions\Action::make('Test Connection')
                ->action('testConnectionAction'),
        ]);
    }

    public static function table(Table $table): Table
    {
        return $table->columns([
            Tables\Columns\TextColumn::make('name')->searchable()->sortable(),
            Tables\Columns\TextColumn::make('domain')->searchable(),
            Tables\Columns\BooleanColumn::make('active'),
        ]);
    }

    /**
     * @return Builder<Merchant>
     */
    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()->where('type', SalesChannelEnum::WOOCOMMERCE->value);
    }

    public static function getPages(): array
    {
        return [
            'create' => CreateWooCommerceMerchant::route('/create'),
            'edit' => EditWooCommerceMerchant::route('/{record}/edit'),
        ];
    }
}
