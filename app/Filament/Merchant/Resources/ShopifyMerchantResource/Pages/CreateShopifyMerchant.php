<?php

namespace App\Filament\Merchant\Resources\ShopifyMerchantResource\Pages;

use App\Filament\Merchant\Resources\ShopifyMerchantResource;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\CreateRecord;

class CreateShopifyMerchant extends CreateRecord
{
    protected static string $resource = ShopifyMerchantResource::class;

    public function testConnectionAction(): void
    {
        Notification::make()
            ->title('Connection Successfull')
            ->success()
            ->send();
    }
}
