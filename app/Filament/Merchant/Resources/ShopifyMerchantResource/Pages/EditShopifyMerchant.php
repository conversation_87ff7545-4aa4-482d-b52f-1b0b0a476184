<?php

namespace App\Filament\Merchant\Resources\ShopifyMerchantResource\Pages;

use App\Filament\Merchant\Resources\ShopifyMerchantResource;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\EditRecord;

class EditShopifyMerchant extends EditRecord
{
    protected static string $resource = ShopifyMerchantResource::class;

    protected function getHeaderActions(): array
    {
        return [
        ];
    }

    public function testConnectionAction(): void
    {
        Notification::make()
            ->title('Connection Successfull')
            ->success()
            ->send();
    }
}
