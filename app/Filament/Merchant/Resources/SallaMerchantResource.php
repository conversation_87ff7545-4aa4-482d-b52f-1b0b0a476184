<?php

namespace App\Filament\Merchant\Resources;

use App\Enums\SalesChannelEnum;
use App\Filament\Merchant\Resources\SallaMerchantResource\Pages\CreateSallaMerchant;
use App\Filament\Merchant\Resources\SallaMerchantResource\Pages\EditSallaMerchant;
use App\Filament\Merchant\Resources\SallaMerchantResource\Pages\ListSallaMerchants;
use App\Models\Merchant;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;

class SallaMerchantResource extends Resource
{
    protected static ?string $model = Merchant::class;

    protected static ?string $navigationLabel = 'Salla Merchants';

    protected static bool $shouldRegisterNavigation = false;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table->columns([
            Tables\Columns\TextColumn::make('name')->searchable()->sortable(),
            Tables\Columns\TextColumn::make('domain')->searchable(),
            Tables\Columns\BooleanColumn::make('active'),
        ]);
    }

    /**
     * @return Builder<Merchant>
     */
    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()->where('type', SalesChannelEnum::SALLA->value);
    }

    public static function getPages(): array
    {
        return [
            'index' => ListSallaMerchants::route('/'),
            'create' => CreateSallaMerchant::route('/create'),
            'edit' => EditSallaMerchant::route('/{record}/edit'),
        ];
    }
}
