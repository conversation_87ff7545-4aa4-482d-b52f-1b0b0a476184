<?php

namespace App\Filament\Merchant\Resources\BoxResource\Pages;

use App\Filament\Merchant\Resources\BoxResource;
use Filament\Actions;
use Filament\Resources\Pages\ManageRecords;
use Illuminate\Contracts\Support\Htmlable;
use Illuminate\Support\HtmlString;

class ManageBoxes extends ManageRecords
{
    protected static string $resource = BoxResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make()->createAnother(false)->translateLabel()->label('translation.add_box'),
        ];
    }

    public function getTitle(): string|Htmlable
    {
        return new HtmlString('
        <div class="flex items-center">
            <lord-icon
    src="https://cdn.lordicon.com/drlfxosw.json"
    trigger="hover"
    stroke="light"
    colors="primary:#6c16c7,secondary:#6c16c7"
                style="width:50px;height:50px">
            </lord-icon>
            <span>'.__('translation.boxes').'</span>
        </div>
    ');
    }
}
