<?php

namespace App\Filament\Merchant\Resources;

use App\Filament\Merchant\Resources\UserJtResource\Pages;
use App\Models\UserShipmentCourierService;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;

class UserJtResource extends Resource
{
    protected static ?string $model = UserShipmentCourierService::class;

    protected static ?string $navigationIcon = 'jeuxydnh.json';

    protected static ?string $navigationGroup = 'home';

    protected static ?string $title = 'dzada';

    protected static bool $shouldRegisterNavigation = false;

    const int JT_ID = 5;

    public static function getLabel(): ?string
    {
        return __('translation.user_shipment_courier_service');
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Hidden::make('shipment_courier_service_id')
                    ->default(self::JT_ID),
                Forms\Components\Hidden::make('user_id')
                    ->default(fn () => auth()->id()),
                Forms\Components\TextInput::make('username')
                    ->label(__('translation.username'))
                    ->maxLength(191),
                Forms\Components\TextInput::make('password')
                    ->label(__('translation.password'))
                    ->password()
                    ->maxLength(191),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                //
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListUserJt::route('/'),
            'create' => Pages\CreateUserJt::route('/create'),
            'edit' => Pages\EditUserJt::route('/{record}/edit'),
        ];
    }

    public static function getTitle(): ?string
    {
        return 'safwen';
    }
}
