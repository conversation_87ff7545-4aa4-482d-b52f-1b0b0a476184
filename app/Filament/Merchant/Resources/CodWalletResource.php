<?php

namespace App\Filament\Merchant\Resources;

use App\Enums\CodWalletStatusEnum;
use App\Enums\CodWalletTypeEnum;
use App\Filament\Merchant\Resources\CodWalletResource\Pages;
use App\Models\CodWallet;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Table;
use Storage;
use Webbingbrasil\FilamentAdvancedFilter\Filters\DateFilter;
use Webbingbrasil\FilamentAdvancedFilter\Filters\TextFilter;

class CodWalletResource extends Resource
{
    protected static ?string $model = CodWallet::class;

    protected static ?string $navigationIcon = 'pfefuxbw.json';

    protected static ?string $navigationGroup = 'home';

    protected static ?int $navigationSort = 10;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Hidden::make('user_id')
                    ->default(fn () => auth()->id()),
                Forms\Components\TextInput::make('amount')->label(__('translation.amount'))
                    ->required()
                    ->numeric()
                    ->minValue(1)
                    ->helperText(__('translation.cod_maximum_allowed'))
                    ->afterStateUpdated(fn ($state, $set) => ($state === CodWalletTypeEnum::WALLET->value) ? $set('paid_at', now()) : $set('paid_at', null))
                    ->maxValue(function () {
                        $pickedMoneyFromBank = CodWallet::where('type', CodWalletTypeEnum::BANK->value)->where('status', 'paid')->sum('amount');
                        $pickedMoneyToWallet = CodWallet::where('type', CodWalletTypeEnum::WALLET->value)->sum('amount');
                        $lastPaymentRequest = CodWallet::where('type', 'bank')->where('status', 'in_progress')->orderBy('created_at', 'desc')->sum('amount');
                        $codTotal = CodWallet::where('type', CodWalletTypeEnum::COD->value)->sum('amount');

                        return ($codTotal - ($lastPaymentRequest + $pickedMoneyToWallet + $pickedMoneyFromBank)) / 100;
                    }),
                Forms\Components\Hidden::make('status')
                    ->default('in_progress'),
                Forms\Components\Select::make('type')->options([
                    CodWalletTypeEnum::BANK->value => __('translation.'.CodWalletTypeEnum::BANK->value),
                    CodWalletTypeEnum::WALLET->value => __('translation.'.CodWalletTypeEnum::WALLET->value),
                ])->required(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('amount')
                    ->label(__('translation.amount'))
                    ->formatStateUsing(fn ($state) => number_format($state / 100, 2))
                    ->sortable(),
                Tables\Columns\TextColumn::make('status')
                    ->label(__('translation.status'))
                    ->formatStateUsing(fn ($state) => trans('translation.'.$state))
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        CodWalletStatusEnum::INPROGRESS->value => 'warning',
                        CodWalletStatusEnum::PAID->value => 'success',
                        CodWalletStatusEnum::ACCEPTED->value => 'success',
                        default => 'secondary',
                    })
                    ->searchable(),
                Tables\Columns\TextColumn::make('type')
                    ->label(__('translation.type'))
                    ->formatStateUsing(fn ($state) => trans('translation.'.$state))
                    ->searchable(),
                Tables\Columns\TextColumn::make('paid_at')
                    ->label(__('translation.paid_at'))
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('created_at')
                    ->label(__('translation.created_at'))
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->label(__('translation.updated_at'))
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->defaultSort('created_at', 'desc')
            ->filters([
                SelectFilter::make('status')
                    ->label(__('translation.status'))
                    ->options([
                        CodWalletStatusEnum::INPROGRESS->value => __('translation.'.CodWalletStatusEnum::INPROGRESS->value),
                        CodWalletStatusEnum::PAID->value => __('translation.'.CodWalletStatusEnum::PAID->value),
                        CodWalletStatusEnum::ACCEPTED->value => __('translation.'.CodWalletStatusEnum::ACCEPTED->value),
                    ])
                    ->searchable()
                    ->preload(),
                SelectFilter::make('type')
                    ->label(__('translation.type'))
                    ->options([
                        CodWalletTypeEnum::COD->value => __('translation.'.CodWalletTypeEnum::COD->value),
                        CodWalletTypeEnum::BANK->value => __('translation.'.CodWalletTypeEnum::BANK->value),
                        CodWalletTypeEnum::WALLET->value => __('translation.'.CodWalletTypeEnum::WALLET->value),
                    ])
                    ->searchable()
                    ->preload(),
                TextFilter::make('amount')->translateLabel()->label('translation.amount'),
                DateFilter::make('created_at')->translateLabel()->label('translation.created_at'),
                DateFilter::make('paid_at')->translateLabel()->label('translation.paid_at'),
            ])
            ->actions([
                Tables\Actions\Action::make('download_invoice')
                    ->label(__('translation.download_invoice'))
                    ->icon('heroicon-o-document-arrow-down')
                    ->color('primary')
                    ->action(fn ($record) => Storage::disk('public')->download(
                        $record->bank_invoice,
                        'invoice_'.$record->paid_at->format('Ymd').'.'.pathinfo($record->bank_invoice, PATHINFO_EXTENSION)
                    ))
                    ->visible(fn ($record) => $record->status == CodWalletStatusEnum::PAID->value &&
                    $record->bank_invoice != null),
                Tables\Actions\Action::make('cod_wallet')
                    ->label(__('translation.view'))
                    ->visible(fn ($record) => $record->type == CodWalletTypeEnum::COD->value)
                    ->icon('heroicon-o-eye')
                    ->modalHeading(__('translation.cod_wallet'))
                    ->modalWidth('4xl')
                    ->modalContent(function ($record) {
                        return view('components.cod-wallet-modal', [
                            'orders' => $record->orders,
                        ]);
                    })
                    ->modalSubmitAction(false),
            ])
            ->bulkActions([]);
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ManageCodWallets::route('/'),
        ];
    }

    public static function getNavigationLabel(): string
    {
        return __('translation.cod_wallet');
    }

    public static function getLabel(): ?string
    {
        return __('translation.debit');
    }
}
