<?php

namespace App\Filament\Merchant\Resources;

use App\Enums\SalesChannelEnum;
use App\Filament\Merchant\Resources\ZidMerchantResource\Pages\CreateZidMerchant;
use App\Filament\Merchant\Resources\ZidMerchantResource\Pages\EditZidMerchant;
use App\Filament\Merchant\Resources\ZidMerchantResource\Pages\ListZidMerchants;
use App\Models\Meeting;
use App\Models\Merchant;
use App\Models\Warehouse;
use Filament\Forms\Components\Actions;
use Filament\Forms\Components\Component;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\Hidden;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\TimePicker;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Components\View;
use Filament\Forms\Components\Wizard;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;

class ZidMerchantResource extends Resource
{
    protected static ?string $model = Merchant::class;

    protected static ?string $navigationLabel = 'Zid Merchants';

    protected static bool $shouldRegisterNavigation = false;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                self::getWizard(),
            ]);
    }

    protected static function getWizard(): Wizard
    {
        return Wizard::make([
            self::getInstructionsStep(),
            self::getCredentialsStep(),
        ])->columnSpanFull()->skippable();
    }

    protected static function getInstructionsStep(): Wizard\Step
    {
        return Wizard\Step::make('Instructions')
            ->label(__('translation.instructions'))
            ->schema([
                View::make('filament.merchant.pages.partials.zid-information'),
            ]);
    }

    protected static function getCredentialsStep(): Wizard\Step
    {
        return Wizard\Step::make('Credentials')
            ->label(__('translation.credentials'))
            ->schema(array_merge(
                self::getCredentialsFields(),
                [self::getEditOnlyButton()]
            ));
    }

    /**
     * @return array<int, Component>
     */
    protected static function getCredentialsFields(): array
    {
        return [
            TextInput::make('name')
                ->label(__('translation.name'))
                ->required()
                ->maxLength(191),
            Hidden::make('type')->default(SalesChannelEnum::ZID->value),
            Hidden::make('user_id')
                ->default(auth()->id()),

            Toggle::make('active')
                ->label(__('translation.active'))
                ->required(),
            Select::make('warehouse_id')
                ->nullable()
                ->label(__('translation.main_warehouse'))
                ->options(Warehouse::pluck('name', 'id')),
        ];
    }

    public static function table(Table $table): Table
    {
        return $table->columns([
            Tables\Columns\TextColumn::make('name')->searchable()->sortable(),
            Tables\Columns\BooleanColumn::make('active'),
        ]);
    }

    /**
     * @return Builder<Merchant>
     */
    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()->where('type', SalesChannelEnum::ZID->value);
    }

    public static function getPages(): array
    {
        return [
            'index' => ListZidMerchants::route('/'),
            'create' => CreateZidMerchant::route('/create'),
            'edit' => EditZidMerchant::route('/{record}/edit'),
        ];
    }

    protected static function getEditOnlyButton(): ?Actions
    {
        return Actions::make([
            // Actions\Action::make('requestMeeting')
            //     ->visible(fn ($livewire) => $livewire->record)
            //     ->label(__('translation.zid_ask_for_meeting'))
            //     ->modalHeading(__('translation.zid_ask_for_meeting'))
            //     ->form([
            //         DatePicker::make('meeting_date')
            //             ->label(__('translation.meeting_date'))
            //             ->required(),

            //         TimePicker::make('meeting_time')
            //             ->label(__('translation.meeting_time'))
            //             ->required(),

            //         Textarea::make('notes')
            //             ->label(__('translation.notes'))
            //             ->maxLength(255)
            //             ->nullable(),
            //     ])
            //     ->action(function (array $data, $livewire) {
            //         Meeting::create([
            //             'user_id' => $livewire->record->user_id,
            //             'meeting_date' => $data['meeting_date'],
            //             'meeting_time' => $data['meeting_time'],
            //             'notes' => $data['notes'] ?? 'طلب إجتماع لربط زد',
            //             'status' => 'pending',
            //         ]);

            //         Notification::make()
            //             ->title(__('translation.meeting_requested'))
            //             ->success()
            //             ->send();
            //     }),
        ]);
    }
}
