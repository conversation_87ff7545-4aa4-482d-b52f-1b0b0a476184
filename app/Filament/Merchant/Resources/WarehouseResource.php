<?php

namespace App\Filament\Merchant\Resources;

use App\Filament\Merchant\Resources\WarehouseResource\Pages;
use App\Models\City;
use App\Models\Warehouse;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Columns\ToggleColumn;
use Filament\Tables\Table;
use Ysfkaya\FilamentPhoneInput\Forms\PhoneInput;

class WarehouseResource extends Resource
{
    protected static ?string $model = Warehouse::class;

    protected static ?string $navigationIcon = 'vulyriwq.json';

    protected static ?string $navigationGroup = 'home';

    protected static ?int $navigationSort = 7;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('name')
                    ->label(__('translation.warehouse_name'))
                    ->translateLabel()
                    ->required()
                    ->maxLength(191),
                Forms\Components\TextInput::make('sender_name')
                    ->label(__('translation.warehouse_sender_name'))
                    ->translateLabel()
                    ->required()
                    ->maxLength(191),
                Forms\Components\TextInput::make('code')
                    ->label(__('translation.warehouse_code'))
                    ->translateLabel()
                    ->maxLength(191),
                Forms\Components\Select::make('city_id')
                    ->label(__('translation.warehouse_city'))
                    ->searchable()
                    ->translateLabel()
                    ->required()
                    ->options(function () {
                        $lang = app()->getLocale();
                        $columnName = $lang === 'ar' ? 'name_ar' : 'name';

                        return City::whereNotNull($columnName)
                            ->orderByRaw("
                CASE
                    WHEN {$columnName} = 'Jeddah' THEN 0
                    WHEN {$columnName} = 'Riyadh City' THEN 1
                    WHEN {$columnName} = 'Medina' THEN 2
                    ELSE 3
                END
            ")
                            ->pluck($columnName, 'id');
                    }),

                Forms\Components\Grid::make(1)
                    ->schema([
                        Forms\Components\TextInput::make('address')
                            ->label(__('translation.warehouse_detailed_address'))
                            ->required()
                            ->translateLabel()
                            ->maxLength(191),
                    ]),
                Forms\Components\Grid::make(2)
                    ->schema([
                        Forms\Components\TextInput::make('zip_code')
                            ->label(__('translation.warehouse_zip_code'))
                            ->required()
                            ->translateLabel()
                            ->maxLength(191),

                        PhoneInput::make('sender_phone')
                            ->label(__('translation.warehouse_sender_phone'))
                            ->initialCountry('sa')
                            ->onlyCountries(['sa'])
//                            ->default($order?->receiver_phone)
                            ->required(),
                    ]),
                Forms\Components\TextInput::make('sender_email')
                    ->label(__('translation.warehouse_sender_email'))
                    ->translateLabel()
                    ->maxLength(191),
                Forms\Components\Hidden::make('user_id')
                    ->default(fn () => auth()->id()),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->label(__('translation.warehouse_name'))
                    ->translateLabel()
                    ->searchable(),
                Tables\Columns\TextColumn::make('code')
                    ->label(__('translation.warehouse_code'))
                    ->translateLabel()
                    ->searchable(),
                Tables\Columns\TextColumn::make('sender_phone')
                    ->label(__('translation.warehouse_sender_phone'))
                    ->translateLabel()
                    ->searchable(),
                Tables\Columns\TextColumn::make('city.name')
                    ->label(__('translation.warehouse_city'))
                    ->getStateUsing(fn ($record) => app()->getLocale() === 'ar' ? $record->city?->name_ar : $record->city?->name)
                    ->translateLabel()
                    ->sortable(),
                Tables\Columns\TextColumn::make('address')
                    ->label(__('translation.warehouse_address'))
                    ->translateLabel()
                    ->searchable(),
                ToggleColumn::make('status')
                    ->label(__('translation.warehouse_status'))
                    ->translateLabel()
                    ->onColor('success')
                    ->offColor('danger'),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
            ]);
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ManageWarehouses::route('/'),
        ];
    }

    public static function getNavigationLabel(): string
    {
        return __('translation.warehouses');
    }

    public static function getLabel(): ?string
    {
        return __('translation.warehouses');
    }
}
