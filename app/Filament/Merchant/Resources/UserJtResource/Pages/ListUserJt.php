<?php

namespace App\Filament\Merchant\Resources\UserJtResource\Pages;

use App\Filament\Merchant\Resources\UserJtResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListUserJt extends ListRecords
{
    protected static string $resource = UserJtResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
