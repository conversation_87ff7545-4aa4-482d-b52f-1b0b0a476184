<?php

namespace App\Filament\Merchant\Resources\ProductResource\Pages;

use App\Filament\Merchant\Resources\ProductResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use Illuminate\Contracts\Support\Htmlable;
use Illuminate\Support\HtmlString;

class ListProducts extends ListRecords
{
    protected static string $resource = ProductResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make()->translateLabel()->label('translation.add_product'),
        ];
    }

    public function getTitle(): string|Htmlable
    {
        return new HtmlString('
        <div class="flex items-center">
<lord-icon
    src="https://cdn.lordicon.com/jguexmoz.json"
    trigger="hover"
    stroke="light"
    colors="primary:#6c16c7,secondary:#6c16c7"
    style="width:50px;height:50px">
</lord-icon>
        <span>'.__('translation.products').'</span>
</div>
    ');
    }
}
