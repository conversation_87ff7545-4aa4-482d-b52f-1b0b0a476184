<?php

namespace App\Filament\Merchant\Resources\WooCommerceMerchantResource\Pages;

use App\Filament\Merchant\Resources\WooCommerceMerchantResource;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\EditRecord;

class EditWooCommerceMerchant extends EditRecord
{
    protected static string $resource = WooCommerceMerchantResource::class;

    protected function getHeaderActions(): array
    {
        return [
        ];
    }

    public function testConnectionAction(): void
    {
        Notification::make()
            ->title('Connection Successfull')
            ->success()
            ->send();
    }
}
