<?php

namespace App\Filament\Merchant\Resources\WooCommerceMerchantResource\Pages;

use App\Filament\Merchant\Resources\WooCommerceMerchantResource;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\CreateRecord;

class CreateWooCommerceMerchant extends CreateRecord
{
    protected static string $resource = WooCommerceMerchantResource::class;

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        $data['webhook_url'] = url('/api/webhook/woocommerce/'.uniqid());

        return $data;
    }

    public function testConnectionAction(): void
    {
        Notification::make()
            ->title('Connection Successfull')
            ->success()
            ->send();
    }
}
