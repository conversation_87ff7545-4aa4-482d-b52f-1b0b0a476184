<?php

namespace App\Filament\Merchant\Resources\UserThabitResource\Pages;

use App\Filament\Merchant\Resources\UserThabitResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListUserThabit extends ListRecords
{
    protected static string $resource = UserThabitResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
