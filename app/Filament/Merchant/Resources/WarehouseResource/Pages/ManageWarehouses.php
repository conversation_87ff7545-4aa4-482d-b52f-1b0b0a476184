<?php

namespace App\Filament\Merchant\Resources\WarehouseResource\Pages;

use App\Filament\Merchant\Resources\WarehouseResource;
use Filament\Actions;
use Filament\Resources\Pages\ManageRecords;
use Illuminate\Contracts\Support\Htmlable;
use Illuminate\Support\HtmlString;

class ManageWarehouses extends ManageRecords
{
    protected static string $resource = WarehouseResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make()->createAnother(false)->translateLabel()->label('translation.add_warehouse'),
        ];
    }

    public function getTitle(): string|Htmlable
    {
        return new HtmlString('
        <div class="flex items-center">
        <lord-icon
    src="https://cdn.lordicon.com/vulyriwq.json"
    trigger="morph"
    stroke="light"
    state="morph-close"
    colors="primary:#6c16c7,secondary:#6c16c7"
            style="width:50px;height:50px">
        </lord-icon>
        <span>'.__('translation.warehouses').'</span>
</div>
    ');
    }
}
