<?php

namespace App\Filament\Merchant\Resources\UserTranscorpResource\Pages;

use App\Filament\Merchant\Resources\UserTranscorpResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListUserTranscorp extends ListRecords
{
    protected static string $resource = UserTranscorpResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
