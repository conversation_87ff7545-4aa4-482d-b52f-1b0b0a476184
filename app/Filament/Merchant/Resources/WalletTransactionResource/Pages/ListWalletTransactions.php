<?php

namespace App\Filament\Merchant\Resources\WalletTransactionResource\Pages;

use App\Filament\Merchant\Resources\WalletTransactionResource;
use Filament\Resources\Pages\ListRecords;
use Illuminate\Contracts\Support\Htmlable;
use Illuminate\Support\HtmlString;

class ListWalletTransactions extends ListRecords
{
    protected static string $resource = WalletTransactionResource::class;

    protected function getHeaderActions(): array
    {
        return [];
    }

    public function getTitle(): string|Htmlable
    {
        return new HtmlString('
        <div class="flex items-center">
            <lord-icon
                src="https://cdn.lordicon.com/pfefuxbw.json"
                trigger="hover"
                stroke="light"
                colors="primary:#6c16c7,secondary:#6c16c7"
            ></lord-icon>
            <span>'.__('translation.wallet_transactions').'</span>
        </div>
    ');
    }
}
