<?php

namespace App\Filament\Merchant\Resources\ZidMerchantResource\Pages;

use App\Filament\Merchant\Resources\ZidMerchantResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListZidMerchants extends ListRecords
{
    protected static string $resource = ZidMerchantResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
