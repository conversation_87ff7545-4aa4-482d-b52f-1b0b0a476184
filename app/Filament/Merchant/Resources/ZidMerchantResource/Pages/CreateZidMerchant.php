<?php

namespace App\Filament\Merchant\Resources\ZidMerchantResource\Pages;

use App\Enums\SalesChannelEnum;
use App\Filament\Merchant\Resources\ZidMerchantResource;
use App\Models\Warehouse;
use Filament\Forms\Components\Actions;
use Filament\Forms\Components\Component;
use Filament\Forms\Components\Hidden;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Components\View;
use Filament\Forms\Components\Wizard\Step;
use Filament\Resources\Pages\CreateRecord;

class CreateZidMerchant extends CreateRecord
{
    use CreateRecord\Concerns\HasWizard;

    protected static string $resource = ZidMerchantResource::class;

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        $data['webhook_url'] = url('/api/webhook/zid/'.uniqid());

        return $data;
    }

    /**
     * @return array<int, Step>
     */
    protected function getSteps(): array
    {
        return [
            self::getInstructionsStep(),
            self::getCredentialsStep(),
        ];
    }

    protected static function getInstructionsStep(): Step
    {
        return Step::make('Instructions')->label(__('translation.instructions'))->translateLabel()
            ->schema([
                View::make('filament.merchant.pages.partials.zid-information'),
            ]);
    }

    protected static function getCredentialsStep(): Step
    {
        return Step::make('Credentials')->label(__('translation.account_info'))->translateLabel()
            ->schema(array_merge(
                self::getCredentialsFields(),
                [self::getEditOnlyButton()]
            ));
    }

    /**
     * Button that only appears on the edit page.
     */
    protected static function getEditOnlyButton(): ?\Filament\Forms\Components\Actions
    {
        return Actions::make([
            Actions\Action::make('Meeting Button')
                ->visible(fn ($livewire) => $livewire->record) // This ensures it appears only in edit mode
                ->label(__('translation.zid_ask_for_meeting'))
                ->action(function ($livewire) {
                    $meeting = new \App\Models\Meeting;
                    $meeting->user_id = $livewire->record->user_id;
                    $meeting->meeting_date = now()->toDateString();
                    $meeting->meeting_time = now()->toTimeString();
                    $meeting->status = 'pending';
                    $meeting->notes = 'Meeting requested from merchant dashboard';
                    $meeting->save();
                }),
        ]);
    }

    /**
     * @return array<int, Component>
     */
    protected static function getCredentialsFields(): array
    {
        return [
            TextInput::make('name')
                ->label(__('translation.merchant_name'))
                ->translateLabel()
                ->required()
                ->maxLength(191),

            TextInput::make('domain')
                ->label(__('translation.domain'))
                ->maxLength(191),
            Hidden::make('type')->default(SalesChannelEnum::ZID->value),
            Hidden::make('user_id')
                ->default(auth()->id()),

            Toggle::make('active')
                ->label(__('translation.active'))
                ->required(),
            TextInput::make('webhook_url')
                ->label(__('translation.webhook_url'))
                ->disabled(),
            Select::make('warehouse_id')
                ->nullable()
                ->label(__('translation.main_warehouse'))
                ->options(Warehouse::pluck('name', 'id')),
        ];
    }
}
