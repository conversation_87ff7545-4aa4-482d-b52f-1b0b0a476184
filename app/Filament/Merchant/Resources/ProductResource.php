<?php

namespace App\Filament\Merchant\Resources;

use App\Models\Product;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;

class ProductResource extends Resource
{
    protected static ?string $model = Product::class;

    protected static ?string $navigationIcon = 'jguexmoz.json';

    protected static ?string $navigationGroup = 'home';

    protected static ?int $navigationSort = 8;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('name')
                    ->label(__('translation.product.name'))
                    ->required()
                    ->maxLength(191),
                Forms\Components\TextInput::make('sku')
                    ->label(__('translation.product.sku'))
                    ->required()
                    ->maxLength(191),
                Forms\Components\TextInput::make('price')
                    ->label(__('translation.product.price'))
                    ->required()
                    ->numeric(),
                Forms\Components\TextInput::make('tax_amount')
                    ->label(__('translation.product.tax_amount'))
                    ->required()
                    ->numeric(),
                Forms\Components\Select::make('currency_id')
                    ->label(__('translation.product.currency'))
                    ->default(3)
                    ->disabled()
                    ->relationship('currency', 'name'),
                Forms\Components\Select::make('tax_currency_id')
                    ->label(__('translation.product.tax_currency'))
                    ->relationship('taxCurrency', 'name'),
                Forms\Components\TextInput::make('barcode')
                    ->label(__('translation.product.barcode'))
                    ->required()
                    ->maxLength(191),
                Forms\Components\TextInput::make('category')
                    ->label(__('translation.product.category'))
                    ->required()
                    ->maxLength(191),
                Forms\Components\TextInput::make('description')
                    ->label(__('translation.product.description'))
                    ->required()
                    ->maxLength(191),
                Forms\Components\TextInput::make('cubic_meter')
                    ->label(__('translation.product.cubic_meter'))
                    ->required()
                    ->numeric()
                    ->default(0),
                Forms\Components\TextInput::make('length')
                    ->minValue(1)
                    ->label(__('translation.product.length'))
                    ->required()
                    ->numeric()
                    ->default(0),
                Forms\Components\TextInput::make('width')
                    ->label(__('translation.product.width'))
                    ->minValue(1)
                    ->required()
                    ->numeric()
                    ->default(0),
                Forms\Components\TextInput::make('height')
                    ->label(__('translation.product.height'))
                    ->minValue(1)
                    ->required()
                    ->numeric()
                    ->default(0),
                Forms\Components\TextInput::make('total_weight')
                    ->label(__('translation.product.total_weight'))
                    ->minValue(1)
                    ->required()
                    ->numeric()
                    ->default(0),
                Forms\Components\FileUpload::make('image')
                    ->label(__('translation.product.image'))
                    ->image(),
                Forms\Components\Select::make('merchant_id')
                    ->label(__('translation.product.merchant'))
                    ->relationship('merchant', 'name'),
                Forms\Components\TextInput::make('external_id')
                    ->label(__('translation.product.external_id'))
                    ->maxLength(191),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->label(__('translation.product.name'))
                    ->searchable(),
                Tables\Columns\TextColumn::make('sku')
                    ->label(__('translation.product.sku'))
                    ->searchable(),
                Tables\Columns\TextColumn::make('price')
                    ->label(__('translation.product.price'))
                    ->money('SAR')
                    ->sortable(),
                Tables\Columns\TextColumn::make('tax_amount')
                    ->label(__('translation.product.tax_amount'))
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('currency.name')
                    ->label(__('translation.product.currency'))
                    ->sortable(),
                Tables\Columns\TextColumn::make('taxCurrency.name')
                    ->label(__('translation.product.tax_currency'))
                    ->sortable(),
                Tables\Columns\TextColumn::make('barcode')
                    ->label(__('translation.product.barcode'))
                    ->searchable(),
                Tables\Columns\TextColumn::make('category')
                    ->label(__('translation.product.category'))
                    ->searchable(),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => ProductResource\Pages\ListProducts::route('/'),
        ];
    }

    public static function getNavigationLabel(): string
    {
        return __('translation.product.products');
    }
}
