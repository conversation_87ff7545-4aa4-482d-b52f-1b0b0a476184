<?php

namespace App\Filament\Merchant\Resources\CodWalletResource\Pages;

use App\Enums\CodWalletStatusEnum;
use App\Filament\Merchant\Resources\CodWalletResource;
use App\Models\User;
use Filament\Actions;
use Filament\Resources\Pages\ManageRecords;
use Illuminate\Contracts\Support\Htmlable;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\HtmlString;

class ManageCodWallets extends ManageRecords
{
    protected static string $resource = CodWalletResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make()->translateLabel()->label('translation.debit')->mutateFormDataUsing(function (array $data): array {
                if ($data['type'] === 'wallet') {
                    /** @var User $user */
                    $user = Auth::user();
                    $user->increaseWalletBalanceByCorrectValue($data['amount'] * 100);
                    $user->save();
                    $data['status'] = CodWalletStatusEnum::PAID->value;
                }
                $data['amount'] = $data['amount'] * 100;

                return $data;
            }),
        ];
    }

    public function getHeaderWidgets(): array
    {
        return [
            CodWalletResource\Widgets\CodWalletOverview::class,
        ];
    }

    public function getTitle(): string|Htmlable
    {
        return new HtmlString('
        <div class="flex items-center">
            <lord-icon
                src="https://cdn.lordicon.com/pfefuxbw.json"
                trigger="hover"
                stroke="light"
                colors="primary:#6c16c7,secondary:#6c16c7"
            ></lord-icon>
            <span>'.__('translation.cod_wallet').'</span>
        </div>
    ');
    }
}
