<?php

namespace App\Filament\Merchant\Resources\CodWalletResource\Widgets;

use App\Enums\CodWalletTypeEnum;
use App\Models\CodWallet;
use Carbon\Carbon;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;

class CodWalletOverview extends BaseWidget
{
    protected function getStats(): array
    {
        $pickedMoneyFromBank = CodWallet::where('type', CodWalletTypeEnum::BANK->value)->where('status', 'paid')->sum('amount');
        $pickedMoneyToWallet = CodWallet::where('type', CodWalletTypeEnum::WALLET->value)->sum('amount');
        $codTotal = CodWallet::where('type', CodWalletTypeEnum::COD->value)->sum('amount');
        $now = now();
        $nextPaymentDay = $now->day < 15 ? 16 : 1;
        $nextPaymentDate = Carbon::create($now->year, $now->month, $nextPaymentDay);

        if ($nextPaymentDay === 1) {
            $nextPaymentDate->addMonth();
        }
        $lastPaymentRequest = CodWallet::where('type', 'bank')->where('status', 'in_progress')->orderBy('created_at', 'desc')->sum('amount');
        $lastPaymentRequestAmount = 0;
        if ($lastPaymentRequest) {
            $lastPaymentRequestAmount = number_format($lastPaymentRequest / 100, 2);
        }
        $currentCodBalance = $codTotal - ($lastPaymentRequest + $pickedMoneyToWallet + $pickedMoneyFromBank);

        return [
            Stat::make('next_payment', $lastPaymentRequestAmount.' ر. س.')
                ->label(__('translation.next_payment'))
                ->color('info')
                ->description('تاريخ التحويل القادم '.$nextPaymentDate->format('d-m-Y'))
                ->icon('heroicon-m-arrow-trending-up'),
            Stat::make('current_cod_balance', number_format($currentCodBalance / 100, 2).' ر. س.')
                ->label(__('translation.current_cod_balance'))
                ->color('info')
                ->icon('heroicon-m-arrow-trending-up'),
            Stat::make('cod_wallet_withdrawn', number_format(($pickedMoneyToWallet + $pickedMoneyFromBank) / 100, 2).' ر. س.')
                ->label(__('translation.cod_wallet_withdrawn'))
                ->color('info')
                ->icon('heroicon-m-arrow-trending-up'),

        ];
    }
}
