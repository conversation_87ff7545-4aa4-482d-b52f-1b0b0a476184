<?php

namespace App\Filament\Merchant\Resources\UserBarqResource\Pages;

use App\Filament\Merchant\Resources\UserBarqResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListUserBarq extends ListRecords
{
    protected static string $resource = UserBarqResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
