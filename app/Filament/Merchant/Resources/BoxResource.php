<?php

namespace App\Filament\Merchant\Resources;

use App\Filament\Merchant\Resources\BoxResource\Pages;
use App\Models\Box;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;

class BoxResource extends Resource
{
    protected static ?string $model = Box::class;

    protected static ?string $navigationGroup = 'home';

    protected static ?string $navigationIcon = 'drlfxosw.json';

    protected static ?int $navigationSort = 9;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('type')
                    ->translateLabel()
                    ->label(__('translation.box_type'))
                    ->required()
                    ->maxLength(191),
                Forms\Components\TextInput::make('length')
                    ->translateLabel()
                    ->minValue(1)
                    ->label('translation.length')
                    ->required()
                    ->numeric()
                    ->default(0),
                Forms\Components\TextInput::make('width')
                    ->translateLabel()
                    ->minValue(1)
                    ->label('translation.width')
                    ->required()
                    ->numeric()
                    ->default(0),
                Forms\Components\TextInput::make('height')
                    ->translateLabel()
                    ->label('translation.height')
                    ->minValue(1)
                    ->required()
                    ->numeric()
                    ->default(0),
                Forms\Components\Hidden::make('user_id')
                    ->default(fn () => auth()->id()),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('type')
                    ->translateLabel()
                    ->label('translation.box_type')
                    ->searchable(),
                Tables\Columns\TextColumn::make('length')
                    ->translateLabel()
                    ->label('translation.length')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('width')
                    ->translateLabel()
                    ->label('translation.width')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('height')
                    ->translateLabel()
                    ->label('translation.height')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ManageBoxes::route('/'),
        ];
    }

    public static function getNavigationLabel(): string
    {
        return __('translation.boxes');
    }

    public static function getLabel(): ?string
    {
        return __('translation.box');
    }
}
