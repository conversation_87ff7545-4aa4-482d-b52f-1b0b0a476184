<?php

namespace App\Filament\Merchant\Resources\SalesChannelsResource\Pages;

use App\Filament\Merchant\Resources\SalesChannelsResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListSalesChannels extends ListRecords
{
    protected static string $resource = SalesChannelsResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
