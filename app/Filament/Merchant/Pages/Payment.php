<?php

namespace App\Filament\Merchant\Pages;

use App\Models\Invoice;
use App\Models\Transaction;
use Filament\Pages\Page;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;

class Payment extends Page
{
    protected static ?string $navigationIcon = 'heroicon-o-document-text';

    protected static string $view = 'filament.merchant.pages.payment';

    protected static bool $shouldRegisterNavigation = false;

    public ?string $transactionStatus = null;

    public ?string $transactionId = null;

    private const array SUCCESS_CODES = ['000.100.110', '000.000.000'];

    /**
     * Initialize the page and fetch transaction status if available.
     */
    public function mount(): void
    {
        $query = request()->query('id');
        if (! is_string($query)) {
            $jsonMessage = json_encode([
                'error' => 'Transaction ID must be a string',
                'transaction_id' => $query,
                'user_id' => auth()->id(),
            ], JSON_UNESCAPED_UNICODE);
            if ($jsonMessage === false) {
                throw new \Exception('JSON encode failed: '.json_last_error_msg());
            }
            throw new \Exception($jsonMessage);
        }
        $this->transactionId = $query;
        if ($this->transactionId) {
            $this->checkTransactionStatus();
        }
    }

    /**
     * Check the status of a transaction from Hyperpay API.
     */
    public function checkTransactionStatus(): void
    {
        $settings = $this->getHyperpaySettings();
        if (! $settings) {
            $this->transactionStatus = 'Error: Missing Hyperpay settings.';

            return;
        }

        $url = $settings['baseUrl'].'/v1/checkouts/'.$this->transactionId.'/payment?entityId='.$settings['entityId'];

        $response = Http::withHeaders([
            'Authorization' => "Bearer {$settings['token']}",
        ])->get($url);

        if (! $response->successful()) {
            $this->transactionStatus = 'Error: Failed to fetch transaction status.';

            return;
        }

        $responseData = $response->json();

        if (! in_array($responseData['result']['code'], self::SUCCESS_CODES, true)) {
            $this->transactionStatus = 'Transaction failed: '.$responseData['result']['description'];

            return;
        }

        $this->handleSuccessfulTransaction($responseData);
    }

    /**
     * Handle a successful transaction by updating records.
     *
     * @param  array<mixed>  $response
     */
    private function handleSuccessfulTransaction(array $response): void
    {
        $merchantTransactionId = $response['merchantTransactionId'] ?? null;

        if (! $merchantTransactionId) {
            $this->transactionStatus = 'Error: Invalid transaction response.';

            return;
        }
        /** @var Transaction $transaction */
        $transaction = Transaction::findOrFail($merchantTransactionId);

        // Update the transaction
        $transaction->status = 'success';
        $resp = json_encode($response);
        if ($resp) {
            $transaction->response = $resp;
        }
        $transaction->save();

        $amount = ($response['amount'] * 100 / 115) * 100;

        $user = auth()->user();

        if (! $user) {
            $this->transactionStatus = 'Error: User not authenticated.';

            return;
        }

        // Create wallet transaction and update balance
        $user->walletTransactions()->create([
            'type' => 'credit',
            'amount' => $amount,
            'description' => 'Account recharge',
            'order_id' => null,
            'transaction_id' => $transaction->id,
        ]);
        Invoice::create([
            'wallet_transaction_id' => $user->walletTransactions()->latest()->first()->id,
            'identifier' => Invoice::latest()->first()->id ?? 1000,
        ]);
        $user->increment('wallet_balance', $amount);

        $this->transactionStatus = 'Payment successful.';
    }

    /**
     * Retrieve Hyperpay settings from the database.
     *
     * @return array<mixed>
     */
    private function getHyperpaySettings(): ?array
    {
        $settings = DB::table('settings')
            ->where('key', 'like', 'hyperpay.%')
            ->pluck('value', 'key')
            ->toArray();

        if (isset($settings['hyperpay.baseUrl'], $settings['hyperpay.entityId'], $settings['hyperpay.token'])) {
            return [
                'baseUrl' => $settings['hyperpay.baseUrl'],
                'entityId' => $settings['hyperpay.entityId'],
                'token' => $settings['hyperpay.token'],
            ];
        }

        return null;
    }
}
