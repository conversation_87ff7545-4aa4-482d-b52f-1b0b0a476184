<?php

namespace App\Filament\Merchant\Pages;

use App\Filament\Merchant\Widgets\GlobalOverview;
use App\Filament\Merchant\Widgets\MonthlyOrdersChart;
use App\Filament\Merchant\Widgets\OrdersWeeklyChart;
use App\Filament\Merchant\Widgets\SalesChannelsDistributionWidget;
use App\Filament\Merchant\Widgets\ShippingCompaniesDistributionWidget;
use App\Filament\Merchant\Widgets\StatsOverview;
use App\Filament\Merchant\Widgets\TopDestinationsWidget;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Form;
use Filament\Forms\Get;
use Filament\Forms\Set;
use Filament\Pages\Dashboard as BaseDashboard;
use Filament\Pages\Dashboard\Concerns\HasFiltersForm;
use Illuminate\Contracts\Support\Htmlable;
use Illuminate\Support\Carbon;
use Illuminate\Support\HtmlString;

class OrderDashboard extends BaseDashboard
{
    use HasFiltersForm;

    protected static ?string $navigationGroup = 'home';

    protected static ?int $navigationSort = 4;

    protected static ?string $navigationIcon = 'pfefuxbw.json';

    protected static string $view = 'filament.merchant.pages.order-dashboard';

    protected static string $routePath = 'order-dashboard';

    protected static ?string $title = 'Order Dashboard';

    public function filtersForm(Form $form): Form
    {
        return $form
            ->schema([
                Section::make(__('translation.filter'))
                    ->schema([
                        DatePicker::make('startDate')
                            ->label(__('translation.start_date'))
                            ->default(Carbon::now()->startOfMonth()->toDateString())
                            ->maxDate(fn (Get $get) => $get('endDate') ?: now())
                            ->native(false)
                            ->displayFormat('Y-m-d')
                            ->extraAttributes(['dir' => 'rtl']),

                        DatePicker::make('endDate')
                            ->label(__('translation.end_date'))
                            ->default(Carbon::now()->toDateString())
                            ->minDate(fn (Get $get) => $get('startDate') ?: now())
                            ->maxDate(now())
                            ->live()
                            ->native(false)
                            ->displayFormat('Y-m-d')
                            ->extraAttributes(['dir' => 'rtl']),
                        Select::make('period')
                            ->label(__('translation.period.period'))
                            ->options([
                                'day' => __('translation.period.this_day'),
                                'week' => __('translation.period.this_week'),
                                'month' => __('translation.period.this_month'),
                                'last_month' => __('translation.period.last_month'),
                                'trimester' => __('translation.period.this_trimester'),
                                'semester' => __('translation.period.this_semester'),
                                'year' => __('translation.period.this_year'),
                            ])
                            ->live()
                            ->afterStateUpdated(function (Get $get, Set $set, $state) {
                                $today = now();

                                if ($state === 'day') {
                                    $set('startDate', $today->copy()->startOfDay()->toDateString());
                                    $set('endDate', $today->copy()->endOfDay()->toDateString());
                                } elseif ($state === 'week') {
                                    $set('startDate', $today->copy()->startOfWeek()->toDateString());
                                    $set('endDate', $today->copy()->endOfWeek()->toDateString());
                                } elseif ($state === 'month') {
                                    $set('startDate', $today->copy()->startOfMonth()->toDateString());
                                    $set('endDate', $today->copy()->endOfMonth()->toDateString());
                                } elseif ($state === 'last_month') {
                                    $set('startDate', $today->copy()->subMonth()->startOfMonth()->toDateString());
                                    $set('endDate', $today->copy()->subMonth()->endOfMonth()->toDateString());
                                } elseif ($state === 'trimester') {
                                    $set('startDate', $today->copy()->startOfQuarter()->toDateString());
                                    $set('endDate', $today->copy()->endOfQuarter()->toDateString());
                                } elseif ($state === 'semester') {
                                    if ($today->month <= 6) {
                                        $set('startDate', $today->copy()->startOfYear()->toDateString());
                                        $set('endDate', $today->copy()->startOfYear()->addMonths(6)->subDay()->toDateString());
                                    } else {
                                        $set('startDate', $today->copy()->startOfYear()->addMonths(6)->toDateString());
                                        $set('endDate', $today->copy()->endOfYear()->toDateString());
                                    }
                                } elseif ($state === 'year') {
                                    $set('startDate', $today->copy()->startOfYear()->toDateString());
                                    $set('endDate', $today->copy()->endOfYear()->toDateString());
                                }
                            }),
                    ])->columns(2),
            ]);
    }

    public function getWidgets(): array
    {
        return [
            StatsOverview::class,
            GlobalOverview::class,
            OrdersWeeklyChart::class,
            MonthlyOrdersChart::class,
            TopDestinationsWidget::class,
            SalesChannelsDistributionWidget::class,
            ShippingCompaniesDistributionWidget::class,

        ];
    }

    public function getTitle(): string|Htmlable
    {
        return new HtmlString('
        <div class="flex items-center">
<lord-icon
  src="https://cdn.lordicon.com/pfefuxbw.json"
    trigger="hover"
    stroke="light"
    colors="primary:#6c16c7,secondary:#6c16c7"
    style="width:50px;height:50px">
</lord-icon>
        <span>'.__('translation.dashboard').'</span>
</div>
    ');
    }

    public function getColumns(): int
    {
        return 2;
    }

    public static function getNavigationLabel(): string
    {
        return __('translation.reports');
    }
}
