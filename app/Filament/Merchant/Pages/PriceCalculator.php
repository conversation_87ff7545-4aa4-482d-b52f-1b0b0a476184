<?php

declare(strict_types=1);

namespace App\Filament\Merchant\Pages;

use App\Dto\ShippingRateDto;
use App\Enums\CourierIdentifierEnum;
use App\Enums\SalesChannelEnum;
use App\Models\Box;
use App\Models\City;
use App\Models\Country;
use App\Models\Merchant;
use App\Models\Order;
use App\Models\OrderBox;
use App\Models\ShipmentCourierService;
use App\Models\User;
use App\Services\InvoiceService;
use App\Services\SalesChannelService;
use App\Services\ShipmentService;
use App\Services\ShippingServiceFactory;
use Exception;
use Filament\Actions\Action;
use Filament\Forms\Components\Hidden;
use Filament\Forms\Components\Repeater;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\ViewField;
use Filament\Forms\Components\Wizard;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Get;
use Filament\Notifications\Notification;
use Filament\Pages\Page;
use Filament\Tables;
use Filament\Tables\Concerns\InteractsWithTable;
use Filament\Tables\Contracts\HasTable;
use Filament\Tables\Table;
use Illuminate\Contracts\Support\Htmlable;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\HtmlString;
use Vicmans\FilamentNumberInput\NumberInput;

class PriceCalculator extends Page implements HasForms, HasTable
{
    use InteractsWithForms;
    use InteractsWithTable;

    protected static ?string $navigationIcon = 'mrniyolg.json';

    /**
     * @var array<mixed>|null
     */
    public ?array $data = [];

    protected static ?string $navigationGroup = 'home';

    protected static string $view = 'filament.merchant.pages.price-calculator';

    protected static ?int $navigationSort = 3;

    public ?Order $order = null;

    public ?int $length = 10;

    public ?int $width = 10;

    public ?int $height = 10;

    public ?ShipmentCourierService $selectedCourierService = null;

    public ?string $orderFrom = '';

    public string $order_number;

    public int $shipment_total_weight;

    public ?string $orderTo = '';

    public ?Order $record = null;

    public ?int $price;

    /**
     * @var array<string, array<string, null>>
     */
    protected array $queryString = [
        'orderId' => ['except' => null],
    ];

    /**
     * @var int[]
     */
    public array $prices = [000, 111, 2222, 3333, 11111, 0000, 1111];

    /** @var array<int, array<string, int|null>> */
    public array $boxes = [
        [
            'box_id' => null,
            'length' => 10,
            'width' => 10,
            'height' => 10,
            'weight' => 1,
        ],
    ];

    /**
     * @var array<mixed>|string|null
     */
    public array|string|null $orderId = null;

    public function mount(): void
    {
        $this->orderId = request()->query('order_id');

        if ($this->orderId) {
            /** @var Order $order */
            $order = Order::findOrFail($this->orderId);
            $this->order = $order;

            $this->orderFrom = $this->order->shipper_city.' '.$this->order->shipper_address_line;
            $this->orderTo = $this->order->receiverCity->name_ar.' '.$this->order->receiver_address_line;
            $this->order_number = $this->order->order_number;
            $this->shipment_total_weight = $this->order->shipment_total_weight;
            if ($this->order->boxes->isEmpty()) {
                $order->boxes()->create([
                    'box_id' => null,
                    'length' => 10,
                    'width' => 10,
                    'height' => 10,
                    'weight' => 1,
                ]);
                /** @var Order $order */
                $order = Order::findOrFail($this->orderId);
                $this->order = $order;
            }
            // @phpstan-ignore-next-line
            $this->form->fill([
                'boxes' => $this->order->boxes->toArray(),
            ]);

        }
    }

    public function table(Table $table): Table
    {
        return $table
            ->query(function () {
                $query = ShipmentCourierService::query();

                if (! $this->order?->canUseBarq() && ! $this->canUseBarq()) {
                    $query->where('identifier', '!=', CourierIdentifierEnum::BARQ->value);
                }

                if ($this->orderFrom && $this->orderTo && $this->isInternational()) {
                    return ShipmentCourierService::where('identifier', '=', CourierIdentifierEnum::ARAMEX->value);
                }

                return $query;
            })
            ->columns([
                Tables\Columns\ViewColumn::make('courier_name')
                    ->label(__('translation.shipping_company'))
                    ->view('filament.widgets.courier-logo'),
                Tables\Columns\TextColumn::make('service_name')->label(__('translation.service_type')),
                Tables\Columns\TextColumn::make('delivery_time')->label(__('translation.delivery_timee'))->badge()->color('success'),
                Tables\Columns\TextColumn::make('pickup')->label(__('translation.order_pickup_method')),
                Tables\Columns\TextColumn::make('delivery')
                    ->label(__('translation.shipping_company')),
                Tables\Columns\TextColumn::make('price')
                    ->label(__('translation.price'))
                    ->numeric()
                    ->default(220)
                    ->formatStateUsing(fn ($state, $record) => isset($this->prices[$record->id]) ? number_format($this->prices[$record->id] / 100, 2).' '.__('translation.sar') : ''),

            ])
            ->actions([
                Tables\Actions\Action::make('selectService')
                    ->label(__('translation.choose'))
                    ->icon('heroicon-o-check')
                    ->action(function ($record) {
                        // Check for restrictions before proceeding
                        if ($this->order) {
                            $restrictions = $this->order->getShipmentRestriction($record->identifier);
                            if (! empty($restrictions)) {
                                foreach ($restrictions as $restriction) {
                                    Notification::make()
                                        ->title(__('translation.shipment_company_restriction.'.$restriction.'.title'))
                                        ->body(__('translation.shipment_company_restriction.'.$restriction.'.body'))
                                        ->danger()
                                        ->send();
                                }

                                return;
                            }
                        }

                        /** @var User $user */
                        $user = auth()->user();
                        $shipmentCost = $this->prices[$record->id];
                        // Check if the wallet balance is sufficient
                        if ($user->wallet_balance < $shipmentCost) {
                            Notification::make()
                                ->title(__('translation.insufficient_wallet_balance'))
                                ->danger()
                                ->send();

                            return;
                        }

                        // Set the selected courier service
                        $this->selectedCourierService = $record;
                        Notification::make()
                            ->title(__('translation.shipping_company_selected'))
                            ->success()
                            ->send();
                    }),
            ]);
    }

    protected function getFormSchema(): array
    {
        return [
            Wizard::make()
                ->schema([
                    Wizard\Step::make(__('translation.shipment_information'))
                        ->schema([
                            Section::make('')->columns(3)
                                ->schema([
                                    Select::make('order_number')
                                        ->label(__('translation.choose_order'))
                                        ->options(function () {
                                            return Order::where('status', 'pending')->orderBy('created_at', 'desc')->pluck('order_number', 'id');
                                        })
                                        ->reactive()
                                        ->disabled(fn () => $this->orderId)
                                        ->default(fn () => $this->orderId ?: null)
                                        ->searchable()
                                        ->afterStateUpdated(function ($state, callable $set) {
                                            /** @var Order|null $order */
                                            $order = Order::find($state);
                                            if (! $order) {
                                                $this->orderFrom = '';
                                                $this->orderTo = '';
                                                $this->order = null;

                                                return;
                                            }

                                            $this->order = $order;
                                            $this->record = $order;
                                            $this->orderFrom = $this->order->shipper_city.' '.$this->order->shipper_address_line;
                                            $this->orderTo = $this->order->receiver_city.' '.$this->order->receiver_address_line;
                                            $this->order_number = $this->order->order_number;
                                            $this->shipment_total_weight = $this->order->shipment_total_weight;
                                            if ($this->order->boxes->isEmpty()) {
                                                $order->boxes()->create([
                                                    'box_id' => null,
                                                    'length' => 10,
                                                    'width' => 10,
                                                    'height' => 10,
                                                    'weight' => 1,
                                                ]);
                                            }
                                            /** @var \Illuminate\Database\Eloquent\Collection<int, \App\Models\OrderBox> $boxesCollection */
                                            $boxesCollection = $order->boxes()->get();
                                            $boxes = $boxesCollection->map(function (OrderBox $box) {
                                                return [
                                                    'id' => $box->id,
                                                    'box_id' => $box->box_id,
                                                    'length' => $box->length,
                                                    'height' => $box->height,
                                                    'width' => $box->width,
                                                    'weight' => $box->weight,
                                                ];
                                            })->toArray();
                                            $this->boxes = $boxes;
                                        }),
                                    Select::make('orderFrom')
                                        ->label(__('translation.order_from'))
                                        ->disabled(fn () => $this->order)
                                        ->options(fn () => City::whereNotNull('name_ar')->where('country_id', 1)->pluck('name_ar', 'id'))
                                        ->searchable()
                                        ->reactive()
                                        ->afterStateUpdated(function ($state, callable $set) {
                                            /** @var City|null $city */
                                            $city = City::find($state);
                                            $set('orderFrom', $state);
                                        })
                                        ->default($this->order?->shipper_city)
                                        ->required(),
                                    Select::make('orderTo')
                                        ->disabled(fn () => $this->order)
                                        ->options(fn () => City::whereNotNull('name_ar')->pluck('name_ar', 'id'))
                                        ->searchable()
                                        ->required()
                                        ->reactive()
                                        ->afterStateUpdated(function ($state, callable $set) {
                                            /** @var City|null $city */
                                            $city = City::find($state);
                                            if ($city) {
                                                $set('orderTo', $state);
                                            }
                                        })
                                        ->label(__('translation.order_to'))
                                        ->default($this->orderTo),
                                ]),
                            Repeater::make('boxes')
                                ->live()
                                ->reactive()
                                ->label(__('translation.boxes'))
                                ->schema([
                                    Hidden::make('id'),
                                    Select::make('box_id')
                                        ->label(__('نوع الصندوق'))
                                        ->options(Box::pluck('type', 'id'))
                                        ->reactive()
                                        ->afterStateUpdated(function ($state, callable $set, Get $get) {
                                            $this->updateBox($get('id'), $state);
                                            /** @var Box|null $box */
                                            $box = Box::find($state);
                                            if (! $box) {
                                                return;
                                            }
                                            $set('length', $box->length);
                                            $set('width', $box->width);
                                            $set('height', $box->height);
                                        }),
                                    NumberInput::make('length')
                                        ->minValue(1)
                                        // ->afterStateUpdated(fn ($state, callable $set) => $set('length', max(1, (int) $state)))
                                        ->label(__('translation.length'))
                                        ->required(),

                                    NumberInput::make('width')
                                        ->minValue(1)
                                        // ->afterStateUpdated(fn ($state, callable $set) => $set('width', max(1, (int) $state)))
                                        ->label(__('translation.width'))
                                        // ->reactive()
                                        ->required(),

                                    NumberInput::make('height')
                                        ->minValue(1)
                                        // ->afterStateUpdated(fn ($state, callable $set) => $set('height', max(1, (int) $state)))
                                        ->label(__('translation.height'))
                                        // ->reactive()
                                        ->required(),

                                    NumberInput::make('weight')
                                        ->minValue(1)
                                        ->hint(__('translation.units.kg'))
                                        // ->afterStateUpdated(fn ($state, callable $set) => $set('weight', max(1, (int) $state)))
                                        ->label(__('translation.weight'))
                                        // ->reactive()
                                        ->required(),
                                ])
                                ->default($this->order?->boxes)
                                ->columns(5),
                        ])
                        ->afterValidation(function (callable $get) {
                            $this->saveBoxesToOrder($get('boxes'));
                            $this->calculatePrices();
                        }),
                    Wizard\Step::make(__('translation.shipment_confirmation'))
                        ->schema([
                            ViewField::make('selectedCourierService')->view('livewire.test'),
                        ]),
                ])
                ->submitAction(
                    Action::make('submit')
                        ->label(__('translation.create_shipment'))
                        ->action('submit')
                        ->color('primary')
                ),
        ];
    }

    /**
     * @param  array<int, array<string, int|null>>  $boxes
     */
    public function saveBoxesToOrder(array $boxes): void
    {
        if (! $this->order) {
            return;
        }
        $this->order->boxes()->delete();
        foreach ($boxes as $boxData) {
            $this->order->boxes()->updateOrCreate(
                ['id' => $boxData['id'] ?? null], // Find by ID if it exists, otherwise create
                [
                    'box_id' => $boxData['box_id'],
                    'length' => $boxData['length'] ?? 10,
                    'width' => $boxData['width'] ?? 10,
                    'height' => $boxData['height'] ?? 10,
                    'weight' => $boxData['weight'] ?? 10,
                ]
            );
        }
    }

    public static function getNavigationLabel(): string
    {
        return __('translation.price_calculator');
    }

    public function getTitle(): string|Htmlable
    {
        return new HtmlString('
        <div class="flex items-center">
<lord-icon
    src="https://cdn.lordicon.com/mrniyolg.json"
    trigger="hover"
    stroke="light"
    colors="primary:#6c16c7,secondary:#6c16c7"
    style="width:50px;height:50px">
</lord-icon>
        <span>'.__('translation.price_calculator').'</span>
</div>
    ');
    }

    /**
     * @throws Exception
     */
    public function submit(): void
    {
        if (! $this->selectedCourierService) {
            Notification::make()
                ->title(__('translation.select_courier_service_error'))
                ->danger()
                ->send();

            return;
        }

        $shipmentService = new ShipmentService;

        if (! $this->order instanceof Order) {
            throw new Exception('order not found');
        }

        // 1. Create the shipment
        $shipmentService->createShipment(
            order: $this->order,
            createdWithGlobalConfig: true,
            courier: $this->selectedCourierService->identifier
        );

        /** @var User $user */
        $user = auth()->user();
        $shipmentCost = $this->prices[$this->selectedCourierService->id];

        // 2. Deduct wallet balance
        $user->wallet_balance -= $shipmentCost;
        $user->save();

        // 3. Generate shipment label PDF
        $mergedPdfPath = $shipmentService->printLabels(collect([$this->order]));

        // 4. Generate proforma ONLY if order is international
        $proformaUrl = null;
        if ($this->order->isInternational()) {
            $invoiceService = new InvoiceService;
            $proformaPdfPath = $invoiceService->generateProformaInvoice(collect([$this->order]));
            $proformaUrl = Storage::disk('public')->url($proformaPdfPath);
            session()->flash('proforma_url', $proformaUrl);
        }

        // 5. Record wallet transaction
        $user->walletTransactions()->create([
            'type' => 'debit',
            'amount' => $shipmentCost,
            'description' => 'رسوم شحنة: '.$this->selectedCourierService->courier_name,
            'order_id' => $this->order->id,
        ]);

        if ($this->order->source === SalesChannelEnum::ZID->value) {
            $merchantId = $this->order->merchant_id;
            $merchant = Merchant::withoutGlobalScopes()->findorFail($merchantId);
            $salesChannelService = new SalesChannelService($merchant);
            $salesChannelService->sendLabel($this->order, Storage::disk('public')->url($mergedPdfPath));
        }

        if ($this->order->isInternational()) {
            redirect()->route('view-label', [
                'url' => Storage::disk('public')->url($mergedPdfPath),
            ]);
        } else {
            redirect(Storage::disk('public')->url($mergedPdfPath));
        }
    }

    protected function calculatePrices(): void
    {
        $factory = app(ShippingServiceFactory::class);
        $shipmentCourierServices = ShipmentCourierService::all();
        if ($this->isInternational()) {
            $shipmentCourierServices = ShipmentCourierService::where('identifier', CourierIdentifierEnum::ARAMEX->value)->get();
        }
        if ($this->order) {
            $shippingRateDto = ShippingRateDto::fromOrder(order: $this->order);
        } else {
            $receiverCity = City::find($this->orderTo);
            $country = Country::findOrFail($receiverCity->country_id);
            $shippingRateDto = new ShippingRateDto(
                boxes: $this->boxes,
                weight: null,
                fromLocation: $this->orderFrom,
                toLocation: $this->orderTo,
                receiverCountryCode: $country->code_country,
                isCod: false,
                ignoreCod: false,
                isInternational: $this->isInternational()
            );
        }

        $this->prices = collect($shipmentCourierServices)->mapWithKeys(function ($service) use ($factory, $shippingRateDto) {
            assert($this->order instanceof Order);
            $serviceInstance = $factory->create($service->identifier, true, $this->order);
            $price = $serviceInstance->getRates($shippingRateDto);

            return [$service->id => $price];
        })->toArray();
    }

    public function updateBox(int|string|null $orderBox, string $boxId): void
    {
        if (! $this->order || ! $boxId) {
            return;
        }
        /** @var Box $fetchedBox */
        $fetchedBox = Box::findOrFail($boxId);
        $fetchedOrderBox = $this->order->boxes()->where('id', $orderBox)->first();
        if (! $fetchedOrderBox) {
            $this->order->boxes()->create([
                'width' => $fetchedBox->width,
                'height' => $fetchedBox->height,
                'length' => $fetchedBox->length,
                'box_id' => $orderBox,
            ]);

            return;
        }
        $data['width'] = $fetchedBox->width;
        $data['height'] = $fetchedBox->height;
        $data['length'] = $fetchedBox->length;
        $data['box_id'] = $fetchedBox->id;
        $fetchedOrderBox->update($data);
    }

    private function isInternational(): bool
    {
        if ($this->order) {
            return $this->order->isInternational();
        }
        $receiverCity = City::findOrFail($this->orderTo);
        $country = Country::findOrFail($receiverCity->country_id);

        return $country->code_country !== 'SA';
    }

    public function canUseBarq(): bool
    {
        $isSameCity = $this->isSameCity();
        if (! $isSameCity) {
            return false;
        }

        if ($this->orderTo !== '171' && $this->orderTo !== '62') {
            return false;
        }

        return true;
    }

    public function isSameCity(): bool
    {
        return $this->orderFrom === $this->orderTo;
    }
}
