<?php

namespace App\Filament\Merchant\Pages;

use Filament\Pages\Page;
use Illuminate\Contracts\Support\Htmlable;
use Illuminate\Support\HtmlString;

class Dashboard extends Page
{
    protected static ?string $navigationIcon = 'jeuxydnh.json';

    protected static ?string $navigationGroup = 'home';

    protected static string $view = 'filament.merchant.pages.dashboard';

    public static function getNavigationLabel(): string
    {
        return __('translation.Dashboards');
    }

    public function getTitle(): string|Htmlable
    {
        return new HtmlString('
        <div class="flex items-center">
        <lord-icon
    src="https://cdn.lordicon.com/jeuxydnh.json"
            trigger="hover"
                stroke="light"
    colors="primary:#6c16c7,secondary:#6c16c7"
            style="width:50px;height:50px">
        </lord-icon>
        <span>'.__('translation.Dashboards').'</span>
</div>
    ');
    }

    public static function shouldRegisterNavigation(): bool
    {
        return true; // Make it visible in navigation
    }
}
