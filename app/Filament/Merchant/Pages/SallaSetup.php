<?php

namespace App\Filament\Merchant\Pages;

use Filament\Pages\Page;

class SallaSetup extends Page
{
    protected static ?string $navigationIcon = 'heroicon-o-cog';

    protected static ?string $navigationGroup = 'home';

    protected static string $view = 'filament.merchant.pages.salla-setup';

    protected static bool $shouldRegisterNavigation = false;

    public static function getNavigationLabel(): string
    {
        return __('Salla Store Setup');
    }
}
