<?php

namespace App\Filament\Merchant\Pages;

use App\Enums\SalesChannelEnum;
use App\Filament\Merchant\Resources\SalesChannelsResource;
use App\Models\SalesChannel;
use Filament\Pages\Page;
use Illuminate\Contracts\Support\Htmlable;
use Illuminate\Http\RedirectResponse;
use Illuminate\Routing\Redirector;
use Illuminate\Support\HtmlString;

class ManageSalesChannels extends Page
{
    protected static string $resource = SalesChannelsResource::class;

    protected static ?string $navigationIcon = 'xncgulpt.json';

    protected static string $view = 'filament.merchant.pages.manage-sales-channels';

    protected static ?string $navigationGroup = 'home';

    protected static ?int $navigationSort = 5;

    public bool $showModal = false;

    public ?SalesChannel $selectedChannel = null;

    public function handleChannelClick(SalesChannel $channel): void
    {
        $this->selectedChannel = $channel;

        // Handle Salla modal (ID = 2)
        if ($channel['id'] == 2) {
            $this->processSelectedOption();
            $this->showModal = true;

            return;
        }

        // Handle Shopify modal (ID = 1)
        if ($channel['id'] == 1) {
            $this->dispatch('open-modal', id: 'shopify-channel-modal');

            return;
        }

        // Handle Zid modal (ID = 3)
        if ($channel['id'] == 3) {
            $this->dispatch('open-modal', id: 'zid-channel-modal');

            return;
        }

        // Handle WooCommerce modal (ID = 4)
        if ($channel['id'] == 4) {
            $this->dispatch('open-modal', id: 'woocommerce-channel-modal');

            return;
        }

        // Fallback for other channels - direct redirect
        $this->createMerchantWithChannelType($channel->identifier);
    }

    public string $activeTab = 'tab1';

    public function setActiveTab(string $tab): void
    {
        $this->activeTab = $tab;
    }

    protected function getViewData(): array
    {
        return [
            'channels' => SalesChannel::get(),
            'activeTab' => $this->activeTab,
        ];
    }

    public function createMerchantWithChannelType(string $channelIdentifier): RedirectResponse|Redirector
    {
        if ($channelIdentifier === SalesChannelEnum::SHOPIFY->value) {
            return redirect()->route('filament.merchant.resources.shopify-merchants.create');
        }

        if ($channelIdentifier === SalesChannelEnum::ZID->value) {
            return redirect()->route('filament.merchant.resources.zid-merchants.create');
        }

        if ($channelIdentifier === SalesChannelEnum::WOOCOMMERCE->value) {
            return redirect()->route('filament.merchant.resources.woo-commerce-merchants.create');
        }

        return redirect()->route('filament.merchant.resources.merchants.create', ['type' => $channelIdentifier]);
    }

    public function processSelectedOption(): void
    {
        $this->dispatch('open-modal', id: 'channel-modal');

    }

    public function getTitle(): string|Htmlable
    {
        return new HtmlString('
        <div class="flex items-center">
            <lord-icon
                src="https://cdn.lordicon.com/xncgulpt.json"
                trigger="hover"
                stroke="light"
                colors="primary:#6c16c7,secondary:#6c16c7"
                style="width:50px;height:50px">
            </lord-icon>
            <span>'.__('translation.sales_channels').'</span>
        </div>
    ');
    }

    public static function getNavigationLabel(): string
    {
        return __('translation.sales_channels');
    }
}
