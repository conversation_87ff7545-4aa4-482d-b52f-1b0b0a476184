<?php

namespace App\Filament\Merchant\Pages;

use App\Enums\CourierIdentifierEnum;
use App\Filament\Merchant\Resources\SalesChannelsResource;
use App\Models\ShipmentCourierService;
use Filament\Pages\Page;
use Illuminate\Auth\Access\AuthorizationException;
use Illuminate\Contracts\Support\Htmlable;
use Illuminate\Http\RedirectResponse;
use Illuminate\Routing\Redirector;
use Illuminate\Support\HtmlString;

class ManageShippingPartners extends Page
{
    protected static string $resource = SalesChannelsResource::class;

    protected static ?string $navigationGroup = 'home';

    protected static string $view = 'filament.merchant.pages.manage-shipping-partners';

    protected static ?string $navigationIcon = 'jiimrvrm.json';

    protected static ?int $navigationSort = 6;

    const int ARAMEX_ID = 1;

    const int BARQ_ID = 2;

    const int TRANSCORP_ID = 3;

    const int THABIT_ID = 4;

    const int JT_ID = 5;

    public function handleChannelClick(string $identifier): RedirectResponse|Redirector|null
    {
        $user = auth()->user();
        if (! $user) {
            throw new AuthorizationException;
        }
        // 1) If user clicked Barq:
        if ($identifier === CourierIdentifierEnum::BARQ->value) {
            $barqRecord = $user
                ->shipments()
                ->where('shipment_courier_service_id', self::BARQ_ID)
                ->first();

            if ($barqRecord) {
                return redirect()->route(
                    'filament.merchant.resources.user-barqs.edit',
                    $barqRecord->id
                );
            }

            return redirect()->route('filament.merchant.resources.user-barqs.create');
        }

        // 2) If user clicked Transcorp:
        if ($identifier === CourierIdentifierEnum::TRANSCORP->value) {
            $transcorpRecord = $user
                ->shipments()
                ->where('shipment_courier_service_id', self::TRANSCORP_ID)
                ->first();

            if ($transcorpRecord) {
                return redirect()->route(
                    'filament.merchant.resources.user-transcorps.edit',
                    $transcorpRecord->id
                );
            }

            return redirect()->route('filament.merchant.resources.user-transcorps.create');
        }

        // 3) Otherwise assume Aramex:
        // We'll assume "shipment_courier_service_id = 1" for Aramex:
        if ($identifier === CourierIdentifierEnum::ARAMEX->value) {
            $aramexRecord = $user
                ->shipments()
                ->where('shipment_courier_service_id', self::ARAMEX_ID)
                ->first();

            if ($aramexRecord) {
                return redirect()->route(
                    'filament.merchant.resources.user-aramexes.edit',
                    $aramexRecord->id
                );
            }

            return redirect()->route('filament.merchant.resources.user-aramexes.create');
        }

        if ($identifier === CourierIdentifierEnum::THABIT->value) {
            $thabitRecord = $user
                ->shipments()
                ->where('shipment_courier_service_id', self::THABIT_ID)
                ->first();

            if ($thabitRecord) {
                return redirect()->route(
                    'filament.merchant.resources.user-thabits.edit',
                    $thabitRecord->id
                );
            }

            return redirect()->route('filament.merchant.resources.user-thabits.create');
        }
        if ($identifier === CourierIdentifierEnum::JT->value) {
            $jtRecord = $user
                ->shipments()
                ->where('shipment_courier_service_id', self::JT_ID)
                ->first();

            if ($jtRecord) {
                return redirect()->route(
                    'filament.merchant.resources.user-jts.edit',
                    $jtRecord->id
                );
            }

            return redirect()->route('filament.merchant.resources.user-jts.create');
        }

        // If none of the above matched for some reason:
        return null;
    }

    public string $activeTab = 'tab1';

    public function setActiveTab(string $tab): void
    {
        $this->activeTab = $tab;
    }

    protected function getViewData(): array
    {
        return [
            'channels' => ShipmentCourierService::get(),
            'activeTab' => $this->activeTab,
        ];
    }

    public function getTitle(): string|Htmlable
    {
        return new HtmlString('
        <div class="flex items-center">
<lord-icon
    src="https://cdn.lordicon.com/jiimrvrm.json"
    trigger="hover"
    stroke="light"
    colors="primary:#6c16c7,secondary:#6c16c7"
    style="width:50px;height:50px">
</lord-icon>
        <span>'.__('translation.shipping_partners').'</span>
</div>
    ');
    }

    public static function getNavigationLabel(): string
    {
        return __('translation.shipping_partners');
    }
}
