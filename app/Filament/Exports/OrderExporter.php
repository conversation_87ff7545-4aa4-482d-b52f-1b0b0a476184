<?php

namespace App\Filament\Exports;

use App\Models\Order;
use Filament\Actions\Exports\ExportColumn;
use Filament\Actions\Exports\Exporter;
use Filament\Actions\Exports\Models\Export;

class OrderExporter extends Exporter
{
    protected static ?string $model = Order::class;

    public static function getColumns(): array
    {
        return [
            ExportColumn::make('order_number')
                ->label(__('translation.order_number')),
            ExportColumn::make('shipment_reference')
                ->label(__('translation.shipment_reference')),
            ExportColumn::make('date')
                ->label(__('translation.date')),
            ExportColumn::make('status')
                ->label(__('translation.status')),
            ExportColumn::make('updated_at')
                ->label(__('translation.status_changed_at')),
            ExportColumn::make('order_grand_total')
                ->label(__('translation.order_grand_total')),
            ExportColumn::make('shipper_name')
                ->label(__('translation.store')),

        ];
    }

    public static function getCompletedNotificationBody(Export $export): string
    {
        $body = 'Your order export has completed and '.number_format($export->successful_rows).' '.str('row')->plural($export->successful_rows).' exported.';

        if ($failedRowsCount = $export->getFailedRowsCount()) {
            $body .= ' '.number_format($failedRowsCount).' '.str('row')->plural($failedRowsCount).' failed to export.';
        }

        return $body;
    }
}
