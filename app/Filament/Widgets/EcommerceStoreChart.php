<?php

namespace App\Filament\Widgets;

use App\Models\SurveyResponse;
use Filament\Widgets\PieChartWidget;
use Illuminate\Support\Facades\DB;

class EcommerceStoreChart extends PieChartWidget
{
    public function getHeading(): ?string
    {
        return __('translation.widgets.ecommerce_store_responses');
    }

    protected function getData(): array
    {
        $ecommerceStoreStats = SurveyResponse::select('has_ecommerce_store', DB::raw('count(*) as count'))
            ->groupBy('has_ecommerce_store')
            ->pluck('count', 'has_ecommerce_store')
            ->toArray();

        return [
            'labels' => [
                __('translation.yes'),
                __('translation.no'),
            ],
            'datasets' => [
                [
                    'label' => 'Responses',
                    'data' => [
                        $ecommerceStoreStats['Yes'] ?? 0,
                        $ecommerceStoreStats['No'] ?? 0,
                    ],
                ],
            ],
        ];
    }
}
