<?php

namespace App\Filament\Widgets;

use App\Models\SurveyResponse;
use Filament\Widgets\BarChartWidget;
use Illuminate\Support\Facades\DB;

class IndustryChart extends BarChartWidget
{
    public function getHeading(): ?string
    {
        return __('translation.widgets.industry_distribution');
    }

    protected function getData(): array
    {
        $industryStats = SurveyResponse::select('business_industry', DB::raw('count(*) as count'))
            ->groupBy('business_industry')
            ->orderByDesc('count')
            ->pluck('count', 'business_industry')
            ->toArray();

        $translatedIndustries = array_map(fn ($industry) => __('translation.survey.industries.'.$industry), array_keys($industryStats));

        return [
            'labels' => $translatedIndustries,
            'datasets' => [
                [
                    'label' => __('translation.survey.responses'),
                    'data' => array_values($industryStats),
                ],
            ],
        ];
    }
}
