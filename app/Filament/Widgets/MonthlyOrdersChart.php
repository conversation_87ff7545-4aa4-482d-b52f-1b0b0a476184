<?php

namespace App\Filament\Widgets;

use App\Models\SurveyResponse;
use Filament\Widgets\BarChartWidget;
use Illuminate\Support\Facades\DB;

class MonthlyOrdersChart extends BarChartWidget
{
    public function getHeading(): ?string
    {
        return __('translation.widgets.monthly_orders');
    }

    protected function getData(): array
    {
        $orderStats = SurveyResponse::select('monthly_orders', DB::raw('count(*) as count'))
            ->groupBy('monthly_orders')
            ->orderByDesc('count')
            ->pluck('count', 'monthly_orders')
            ->toArray();

        $translatedOrders = array_map(fn ($order) => __('translation.survey.orderOptions.'.$order), array_keys($orderStats));

        return [
            'labels' => $translatedOrders,
            'datasets' => [
                [
                    'label' => __('translation.survey.responses'),
                    'data' => array_values($orderStats),
                ],
            ],
        ];
    }
}
