<?php

namespace App\Enums;

use App\Models\CourierStatus;
use Illuminate\Support\Facades\Log;

enum OrderStatusEnum: string
{
    case PENDING = 'pending';
    case AWAITING_PICKUP = 'awaiting_pickup';
    case CURRENTLY_SHIPPING = 'currently_shipping';
    case DELIVERED = 'delivered';
    case RETURNED = 'returned';
    case SHIPMENT_ON_HOLD = 'shipment_on_hold';
    case CANCELED = 'canceled';
    case FAILED = 'failed';
    case NOT_CHANGED = 'not_changed';

    /**
     * Map Aramex UpdateCode to OrderStatusEnum.
     */
    public static function fromAramexStatus(string $updateCode): self
    {
        return self::fromCourierStatus('aramex', $updateCode);
    }

    /**
     * Map Thabit UpdateCode to OrderStatusEnum.
     */
    public static function fromThabitStatus(string $updateCode): self
    {
        // Handle empty string as a special case for Thabit
        $code = $updateCode === '' ? 'empty_status' : $updateCode;

        return self::fromCourierStatus('thabit', $code);
    }

    /**
     * Map Barq UpdateCode to OrderStatusEnum.
     */
    public static function fromBarqStatus(string $updateCode): self
    {
        return self::fromCourierStatus('barq', $updateCode);
    }

    /**
     * Map Transcorp UpdateCode to OrderStatusEnum.
     */
    public static function fromTranscorpStatus(string $updateCode): self
    {
        return self::fromCourierStatus('transcorp', $updateCode);
    }

    /**
     * @return string[]
     */
    public static function values(): array
    {
        return array_column(self::cases(), 'value');
    }

    /**
     * Map SPLCode to OrderStatusEnum.
     */
    public static function fromSPLCode(string $splCode): self
    {
        return self::fromCourierStatus('spl', $splCode);
    }

    /**
     * Map JT Status to OrderStatusEnum.
     */
    public static function fromJtStatus(string $jtStatus): self
    {
        return self::fromCourierStatus('jt', $jtStatus);
    }

    /**
     * Generic method to map any courier status to OrderStatusEnum.
     */
    public static function fromCourierStatus(string $courier, string $statusCode): self
    {
        $orderStatus = CourierStatus::getOrderStatus($courier, $statusCode);

        if ($orderStatus) {
            return self::from($orderStatus);
        }

        // Log when status code is not found in database and using default
        Log::warning("Unmapped status code '{$statusCode}' for courier '{$courier}' - using default NOT_CHANGED status");

        // Default fallback for unmapped statuses
        return self::NOT_CHANGED;
    }
}
