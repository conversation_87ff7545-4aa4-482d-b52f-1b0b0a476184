<?php

namespace App\Enums;

enum OrderHistoryEnum: string
{
    case UNDEFINED = 'undefined';
    case NEW = 'new';
    case SEARCHING_DRIVER = 'searching_driver';
    case PICKED_UP = 'picked_up';
    case SHIPMENT_CREATED = 'shipment_created';
    case HELD_FOR_PICKUP = 'held_for_pickup';
    case DELIVERED = 'delivered';

    public static function fromAramexStatus(string $aramexStatus): OrderHistoryEnum
    {
        return match ($aramexStatus) {
            'SH014' => self::SHIPMENT_CREATED,
            'SH012', 'SH314','SH047','SH022', 'SH001','SH533','SH164' => self::PICKED_UP,
            'SH369' => self::HELD_FOR_PICKUP,
            'SH005','SH006' => self::DELIVERED,
            default => self::UNDEFINED,
        };

    }

    public function color(): string
    {
        return match ($this) {
            self::NEW => 'primary',
            self::SEARCHING_DRIVER => 'warning',
            self::PICKED_UP => 'info',
            self::SHIPMENT_CREATED => 'success',
            self::HELD_FOR_PICKUP => 'warning',
            self::DELIVERED => 'primary',
            self::UNDEFINED => 'gray',
        };
    }
}
