<?php

namespace App\Enums\AlthabitEnums;

/**
 * Enum for charge types specific to the Althabit service.
 *
 * This enum defines the valid charge types used when creating shipments via the Althabit API.
 *
 * Possible values:
 * - 'cod' (Cash on Delivery)
 * - 'service_custom' (Custom service charge)
 */
enum ChargeTypeEnum: string
{
    case COD = 'cod';
    case SERVICE_CUSTOM = 'service_custom';

}
