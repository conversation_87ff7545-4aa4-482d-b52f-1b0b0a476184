<?php

namespace App\Imports;

use App\Enums\OrderStatusEnum;
use App\Enums\SalesChannelEnum;
use App\Models\City;
use App\Models\Country;
use App\Models\Order;
use App\Models\Warehouse;
use DateTime;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\ToCollection;

class SallaOrderImport implements ToCollection
{
    const ORDER_NUMBER = 0;

    const RECEIVER_FIRST_NAME = 2;

    const RECEIVER_PHONE = 3;

    const RECEIVER_CITY = 4;

    const RECEIVER_COUNTRY = 5;

    const PAYMENT_METHOD = 12;

    const ORDER_GRAND_TOTAL = 15;

    const DATE = 16;

    const RECEIVER_ADDRESS_LINE = 19;

    const PRODUCT = 20;

    const EXTERNAL_ID = 22;

    /** Required columns list */
    private const REQUIRED_COLUMNS = [
        self::RECEIVER_FIRST_NAME,
        self::RECEIVER_PHONE,
        self::RECEIVER_ADDRESS_LINE,
        self::RECEIVER_CITY,
        self::RECEIVER_COUNTRY,
        self::ORDER_NUMBER,
        self::PAYMENT_METHOD,
    ];

    /**
     * @param  Collection<int, Collection<int, string|null>>  $collection
     * @return Collection<int, Collection<int, string>>
     */
    public function collection(Collection $collection): Collection
    {

        $collection = $collection
            ->map(function (Collection $row) {
                return $row->filter(fn ($value) => ! is_null($value) && trim($value) !== '');
            })
            ->filter(function (Collection $row) {
                $requiredColumns = [0, 2];
                foreach ($requiredColumns as $index) {
                    if (isset($row[$index]) && trim($row[$index]) !== '') {
                        return true;
                    }
                }

                return false;
            })
            ->values();
        foreach ($collection as $key => $row) {
            if ($key === 0) {
                continue;
            }
            $isMissingRequiredColumn = false;
            foreach (self::REQUIRED_COLUMNS as $index) {
                if (! isset($row[$index]) || trim($row[$index]) === '') {
                    $isMissingRequiredColumn = true;
                    break;
                }
            }
            if ($isMissingRequiredColumn == true) {
                continue;
            }

            if (Order::withoutGlobalScopes()->where('order_number', $row[self::ORDER_NUMBER])->exists()) {
                continue;
            }
            if (Order::withoutGlobalScopes()->where('external_id', $row[self::EXTERNAL_ID])->exists()) {
                continue;
            }

            /** @var Warehouse|null $warehouse */
            $warehouse = WareHouse::where('status', 1)->first();

            if (! $warehouse) {
                break;
            }

            $receiverCountryId = Country::findByString($row[self::RECEIVER_COUNTRY], $row[self::RECEIVER_COUNTRY]);
            $date = isset($row[self::DATE]) && DateTime::createFromFormat('Y-m-d H:i:s', $row[self::DATE]) ? $row[self::DATE] : null;
            $productRaw = $row[self::PRODUCT] ?? '';
            preg_match('/\(SKU:\s*(.*?)\)\s*(.*?)\s*\(Qty:\s*(\d+)\)/u', $productRaw, $matches);
            $order = Order::create([

                'receiver_first_name' => $row[self::RECEIVER_FIRST_NAME],
                'receiver_last_name' => '.',
                'receiver_phone' => phoneCorrector(receivedPhone: $row[self::RECEIVER_PHONE], correctWithCountry: $receiverCountryId),
                'receiver_address_line' => $row[self::RECEIVER_ADDRESS_LINE],
                'receiver_city_id' => City::findByString($row[self::RECEIVER_CITY]),
                'receiver_city' => $row[self::RECEIVER_CITY],
                'receiver_country' => $row[self::RECEIVER_COUNTRY],
                'receiver_country_id' => $receiverCountryId,
                'order_number' => $row[self::ORDER_NUMBER],
                'external_id' => $row[self::EXTERNAL_ID],
                'payment_method' => $this->paymentMethods[$row[self::PAYMENT_METHOD]] ?? $row[self::PAYMENT_METHOD],
                'order_grand_total' => $row[self::ORDER_GRAND_TOTAL] ?? 0,
                'date' => $date ?? now(),
                'warehouse_id' => $warehouse->id,
                'shipper_name' => $warehouse->name,
                'shipper_city' => $warehouse->city->name,
                'shipper_address_line' => $warehouse->address,
                'shipper_phone' => $warehouse->sender_phone,
                'source' => SalesChannelEnum::SALLA->value,
                'status' => OrderStatusEnum::PENDING->value,
            ]);

            $product = [
                'sku' => $matches[1] ?? null,
                'name' => $matches[2] ?? null,
                'quantity' => isset($matches[3]) ? intval($matches[3]) : null,
            ];

            if (array_filter($product, fn ($value) => ! is_null($value) && $value !== '')) {
                $order->products()->create($product);

                $boxes = collect(range(1, max(1, $product['quantity'])))
                    ->map(fn () => [
                        'weight' => 0,
                        'width' => 10,
                        'length' => 10,
                        'height' => 10,
                    ])
                    ->toArray();

                $order->boxes()->createMany($boxes);
            }
        }

        return $collection;
    }

    /** @var array<string, string> */
    protected array $paymentMethods = [
        'دفع عند التسليم' => 'cod',
        'دفع عند الإستلام' => 'cod',
    ];
}
