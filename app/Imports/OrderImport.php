<?php

namespace App\Imports;

use App\Enums\OrderStatusEnum;
use App\Models\City;
use App\Models\Country;
use App\Models\Order;
use App\Models\Warehouse;
use DateTime;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\ToCollection;

class OrderImport implements ToCollection
{
    const RECEIVER_FIRST_NAME = 0;

    const RECEIVER_LAST_NAME = 1;

    const RECEIVER_PHONE = 2;

    const RECEIVER_ADDRESS_LINE = 3;

    const RECEIVER_CITY = 4;

    const RECEIVER_COUNTRY = 5;

    const ORDER_NUMBER = 6;

    const PAYMENT_METHOD = 7;

    const ORDER_GRAND_TOTAL = 8;

    const CURRENCY = 9;

    const SHIPPER_NAME = 26;

    const DESCRIPTION = 11;

    const DATE = 12;

    const NB_BOXES = 13;

    const BOX_WEIGHT = 14;

    const RECEIVER_EMAIL = 15;

    const RECEIVER_POSTAL_CODE = 16;

    const BOX_WIDTH = 17;

    const BOX_LENGTH = 18;

    const BOX_HEIGHT = 19;

    const PRODUCT_NAME = 20;

    const PRODUCT_PRICE = 21;

    const PRODUCT_QUANTITY = 22;

    const PRODUCT_SKU = 23;

    const PRODUCT_WEIGHT = 24;

    const PRODUCT_WEIGHT_UNIT = 25;

    const RECEIVER_STREET_NAME = 27;

    const RECEIVER_BLOCK = 28;

    /** Required columns list */
    private const REQUIRED_COLUMNS = [
        self::RECEIVER_FIRST_NAME,
        self::RECEIVER_LAST_NAME,
        self::RECEIVER_PHONE,
        self::RECEIVER_ADDRESS_LINE,
        self::RECEIVER_CITY,
        self::RECEIVER_COUNTRY,
        self::ORDER_NUMBER,
        self::PAYMENT_METHOD,
        self::ORDER_GRAND_TOTAL,
        self::CURRENCY,
        self::SHIPPER_NAME,
    ];

    /**
     * @param  Collection<int, Collection<int, string|null>>  $collection
     * @return Collection<int, Collection<int, string>>
     */
    public function collection(Collection $collection): Collection
    {

        $collection = $collection
            ->map(function (Collection $row) {
                return $row->filter(fn ($value) => ! is_null($value) && trim($value) !== '');
            })
            ->filter(function (Collection $row) {
                $requiredColumns = [0, 2];
                foreach ($requiredColumns as $index) {
                    if (isset($row[$index]) && trim($row[$index]) !== '') {
                        return true;
                    }
                }

                return false;
            })
            ->values();
        foreach ($collection as $key => $row) {
            if ($key === 0 || $key === 1 || $key === 2) {
                continue;
            }
            $isMissingRequiredColumn = false;
            foreach (self::REQUIRED_COLUMNS as $index) {
                if (! isset($row[$index]) || trim($row[$index]) === '') {
                    $isMissingRequiredColumn = true;
                    break;
                }
            }
            if ($isMissingRequiredColumn == true) {
                continue;
            }
            /** @var Warehouse $warehouse */
            $warehouse = WareHouse::where('name', $row[self::SHIPPER_NAME])
                ->where('status', 1)->first()
                ?? WareHouse::where('status', 1)->first();

            $orderNumber = $row[self::ORDER_NUMBER];
            while (Order::withoutGlobalScopes()->where('order_number', $orderNumber)->exists()) {
                $orderNumber .= rand(1, 1000);
            }
            $receiverCountryId = Country::findByString($row[self::RECEIVER_COUNTRY], $row[self::RECEIVER_COUNTRY]);
            $date = isset($row[self::DATE]) && DateTime::createFromFormat('Y-m-d H:i:s', $row[self::DATE]) ? $row[self::DATE] : null;
            $order = Order::create([

                'receiver_first_name' => $row[self::RECEIVER_FIRST_NAME],
                'receiver_last_name' => $row[self::RECEIVER_LAST_NAME],
                'receiver_phone' => phoneCorrector(receivedPhone: $row[self::RECEIVER_PHONE], correctWithCountry: $receiverCountryId),
                'receiver_address_line' => $row[self::RECEIVER_ADDRESS_LINE],
                'receiver_city_id' => City::findByString($row[self::RECEIVER_CITY]),
                'receiver_city' => $row[self::RECEIVER_CITY],
                'receiver_country' => $row[self::RECEIVER_COUNTRY],
                'receiver_country_id' => $receiverCountryId,
                'receiver_email' => $row[self::RECEIVER_EMAIL] ?? null,
                'receiver_postal_code' => $row[self::RECEIVER_POSTAL_CODE] ?? null,
                'order_number' => $orderNumber,
                'payment_method' => $this->paymentMethods[$row[self::PAYMENT_METHOD]] ?? $row[self::PAYMENT_METHOD],
                'order_grand_total' => $row[self::ORDER_GRAND_TOTAL],
                'description' => $row[self::DESCRIPTION] ?? null,
                'shipper_name' => $row[self::SHIPPER_NAME],
                'receiver_street_name' => $row[self::RECEIVER_STREET_NAME] ?? null,
                'receiver_block' => $row[self::RECEIVER_BLOCK] ?? null,
                'shipper_city' => $warehouse->city->name,
                'shipper_address_line' => $warehouse->address,
                'shipper_phone' => $warehouse->sender_phone,
                'date' => $date ?? now(),
                'warehouse_id' => $warehouse->id,
                'status' => OrderStatusEnum::PENDING->value,
            ]);

            $product = [
                'name' => $row[self::PRODUCT_NAME] ?? null,
                'price' => $row[self::PRODUCT_PRICE] ?? null,
                'quantity' => $row[self::PRODUCT_QUANTITY] ?? null,
                'sku' => $row[self::PRODUCT_SKU] ?? null,
                'weight' => $row[self::PRODUCT_WEIGHT] ?? null,
                'weight_unit' => $row[self::PRODUCT_WEIGHT_UNIT] ?? null,
                'total_price' => isset($row[self::PRODUCT_PRICE]) ? (floatval($row[self::PRODUCT_PRICE]) * floatval($row[self::PRODUCT_QUANTITY]) * 1.15) : null,
            ];
            if (array_filter($product, fn ($value) => ! is_null($value) && $value !== '')) {
                $order->products()->create($product);
            }

            $boxes = collect(range(1, max(1, intval($row[self::NB_BOXES] ?? 1))))
                ->map(fn () => [
                    'weight' => $row[self::BOX_WEIGHT] ?? 0,
                    'width' => $row[self::BOX_WIDTH] ?? 10,
                    'length' => $row[self::BOX_LENGTH] ?? 10,
                    'height' => $row[self::BOX_HEIGHT] ?? 10,
                ])
                ->toArray();
            if (! empty($row[self::NB_BOXES]) || ! empty($row[self::BOX_WEIGHT]) || ! empty($row[self::BOX_WIDTH]) || ! empty($row[self::BOX_LENGTH]) || ! empty($row[self::BOX_HEIGHT])) {
                $order->boxes()->createMany($boxes);
            }
        }

        return $collection;
    }

    /** @var array<string, string> */
    protected array $paymentMethods = [
        'Cash on Delivery' => 'cod',
        'Paid' => 'paid',
    ];
}
