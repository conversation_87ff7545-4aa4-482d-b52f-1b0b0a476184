<?php

namespace App\Providers;

use App\Filament\Merchant\Widgets\ShippingCompaniesDistributionWidget;
use App\Filament\Merchant\Widgets\TopDestinationsWidget;
use App\Services\SettingsService;
use App\Services\ShippingServiceFactory;
use BezhanSalleh\FilamentLanguageSwitch\LanguageSwitch;
use BezhanSalleh\FilamentShield\Facades\FilamentShield;
use Filament\Facades\Filament;
use Filament\Support\Assets\Js;
use Filament\Support\Facades\FilamentAsset;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\URL;
use Illuminate\Support\ServiceProvider;
use Monolog\Logger as MonologLogger;
use Monolog\LogRecord;
use Spatie\Health\Checks\Checks\CacheCheck;
use Spatie\Health\Checks\Checks\DatabaseCheck;
use Spatie\Health\Checks\Checks\DebugModeCheck;
use Spatie\Health\Checks\Checks\EnvironmentCheck;
use Spatie\Health\Checks\Checks\OptimizedAppCheck;
use Spatie\Health\Checks\Checks\UsedDiskSpaceCheck;
use Spatie\Health\Facades\Health;
use Symfony\Component\Mailer\Bridge\Brevo\Transport\BrevoTransportFactory;
use Symfony\Component\Mailer\Transport\Dsn;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        $this->app->singleton(ShippingServiceFactory::class, function ($app) {
            return new ShippingServiceFactory(new SettingsService);
        });

    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        //        FilamentShield::prohibitDestructiveCommands($this->app->isProduction());
        Filament::registerWidgets([
            TopDestinationsWidget::class,
            ShippingCompaniesDistributionWidget::class,
        ]);
        FilamentAsset::register([
            Js::make('custom-script', __DIR__.'/../../resources/js/custom.js'),
        ]);
        FilamentAsset::register([
            Js::make('example-external-script', 'https://js.sentry-cdn.com/7802386834d82c39e7d44557c3bc1a11.min.js'),
        ]);
        Model::unguard();
        $appUrl = config('app.url');
        //    dd($appUrl);
        if (is_bool($appUrl)) {
            $appUrl = '';
        }

        if (! empty($appUrl) && str_contains($appUrl, 'https')) {
            URL::forceScheme('https');
            URL::forceRootUrl($appUrl);
        }

        Schema::defaultStringLength(191);
        LanguageSwitch::configureUsing(function (LanguageSwitch $switch) {
            $switch
                ->locales(['ar', 'en'])->flags([
                    'ar' => asset('images/flags/ksa.jpeg'),
                    'en' => asset('images/flags/us.jpg'),
                ]);
        });
        Table::$defaultNumberLocale = 'en';

        Health::checks([
            OptimizedAppCheck::new(),
            DebugModeCheck::new(),
            EnvironmentCheck::new(),
            DatabaseCheck::new(),
            CacheCheck::new(),
            UsedDiskSpaceCheck::new(),
        ]);

        Mail::extend('brevo', function () {
            return (new BrevoTransportFactory)->create(
                new Dsn(
                    'brevo+api',
                    'default',
                    config('services.brevo.key')
                )
            );
        });

        $logger = Log::channel('stack');

        $monolog = method_exists($logger, 'getLogger') ? $logger->getLogger() : null;

        if ($monolog instanceof MonologLogger) {
            $monolog->pushProcessor(function (LogRecord $record): LogRecord {
                if (! Auth::check()) {
                    return $record->with(extra: array_merge($record->extra, [
                        'user_id' => 'not logged user',
                        'first_name' => null,
                        'last_name' => null,
                    ]));
                }
                $user = Auth::user();

                return $record->with(extra: array_merge($record->extra, [
                    'user_id' => $user->id,
                    'first_name' => $user->first_name ?? null,
                    'last_name' => $user->last_name ?? null,
                ]));

            });
        }

    }
}
