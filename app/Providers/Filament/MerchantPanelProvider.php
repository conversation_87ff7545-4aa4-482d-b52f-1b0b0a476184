<?php

namespace App\Providers\Filament;

use App\Filament\Merchant\Widgets\GlobalOverview;
use App\Filament\Merchant\Widgets\StatsOverview;
use App\Filament\Pages\Auth\EditProfile;
use App\Filament\Pages\Auth\Login;
use App\Filament\Pages\Registration;
use App\Models\Merchant;
use DutchCodingCompany\FilamentDeveloperLogins\FilamentDeveloperLoginsPlugin;
use Filament\Enums\ThemeMode;
use Filament\Http\Middleware\Authenticate;
use Filament\Http\Middleware\DisableBladeIconComponents;
use Filament\Http\Middleware\DispatchServingFilamentEvent;
use Filament\Panel;
use Filament\PanelProvider;
use Filament\Support\Colors\Color;
use Filament\View\PanelsRenderHook;
use Filament\Widgets;
use Illuminate\Auth\Middleware\EnsureEmailIsVerified;
use Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse;
use Illuminate\Cookie\Middleware\EncryptCookies;
use Illuminate\Foundation\Http\Middleware\VerifyCsrfToken;
use Illuminate\Routing\Middleware\SubstituteBindings;
use Illuminate\Session\Middleware\AuthenticateSession;
use Illuminate\Session\Middleware\StartSession;
use Illuminate\View\Middleware\ShareErrorsFromSession;
use Livewire\Livewire;
use Swis\Filament\Backgrounds\FilamentBackgroundsPlugin;
use Swis\Filament\Backgrounds\ImageProviders\MyImages;

class MerchantPanelProvider extends PanelProvider
{
    public function panel(Panel $panel): Panel
    {
        // Avoid database queries during package discovery
        $merchant = null;
        try {
            if (config('app.env') !== 'testing') {
                $merchant = Merchant::first();
            }
        } catch (\Exception $e) {
            // Silently fail if database connection is not available
        }

        return $panel
            ->id('merchant')
            ->path('merchant')
            ->font('Poppins')
            ->login(Login::class)
            ->registration(Registration::class)
            ->passwordReset()
            ->emailVerification()
            ->databaseTransactions()
            ->profile(EditProfile::class, isSimple: false)
//            ->databaseNotifications()
            ->breadcrumbs(false)
            ->colors([
                'danger' => Color::Rose,
                'gray' => Color::Gray,
                'info' => Color::Blue,
                'primary' => '#6f42c1',
                'success' => Color::Emerald,
                'warning' => Color::Orange,
            ])
            ->brandLogo(asset('images/treek-logo-name.png'))
            ->discoverResources(in: app_path('Filament/Merchant/Resources'), for: 'App\\Filament\\Merchant\\Resources')
            ->discoverPages(in: app_path('Filament/Merchant/Pages'), for: 'App\\Filament\\Merchant\\Pages')
            ->pages([
            ])
            ->discoverWidgets(in: app_path('Filament/Merchant/Widgets'), for: 'App\\Filament\\Merchant\\Widgets')
            ->widgets([
                //                Widgets\AccountWidget::class,
                //                Widgets\FilamentInfoWidget::class,
                //                StatsOverview::class,
                //                GlobalOverview::class,
            ])
            ->databaseTransactions()
            ->viteTheme('resources/css/filament/merchant/theme.css')
            ->middleware([
                EncryptCookies::class,
                AddQueuedCookiesToResponse::class,
                StartSession::class,
                AuthenticateSession::class,
                ShareErrorsFromSession::class,
                VerifyCsrfToken::class,
                SubstituteBindings::class,
                DisableBladeIconComponents::class,
                DispatchServingFilamentEvent::class,
            ])
            ->maxContentWidth('full')
            ->authMiddleware([
                Authenticate::class,
                EnsureEmailIsVerified::class,
            ])
            ->plugins(
                [
                    FilamentDeveloperLoginsPlugin::make()
                        ->enabled(app()->environment('local'))
                        ->users([
                            'Dive/Leonci' => '<EMAIL>',
                            'Sumer' => '<EMAIL>',
                            'Chrisbella' => '<EMAIL>',
                            'Test' => '<EMAIL>',
                        ]),
                    FilamentBackgroundsPlugin::make()->showAttribution(false)
                        ->imageProvider(
                            MyImages::make()
                                ->directory('images/backgrounds')
                        ),
                ]
            )
            ->collapsibleNavigationGroups(false)
            ->defaultThemeMode(ThemeMode::Light)
            ->sidebarWidth('15rem')
            ->darkMode(false)
            ->renderHook('panels::head.start',
                static fn (): string => \View::make('chatbot')->render())
//            ->renderHook(PanelsRenderHook::BODY_START,
//                fn () => Livewire::mount('survey-modal'))
//            ->renderHook(
//                PanelsRenderHook::TOPBAR_START,
//                function () {
//                    $user = auth()->user();
//                    if ($user && $user->merchant) {
//                        return Livewire::mount('synchronization-button');
//                    }
//
//                    return null;
//                },
//            )
            ->renderHook(
                PanelsRenderHook::USER_MENU_BEFORE,
                fn () => view('filament.custom-topbar-button')
            )
//            ->renderHook(
//                PanelsRenderHook::USER_MENU_BEFORE,
//                function () {
//                    try {
//                        if (config('app.env') !== 'testing') {
//                            return view('filament.sync-salla-button', ['merchant' => Merchant::first()]);
//                        }
//                    } catch (\Exception $e) {
//                        // Silently fail if database connection is not available
//                    }
//
//                    return view('filament.sync-salla-button', ['merchant' => null]);
//                }
//            )
            ->renderHook(
                PanelsRenderHook::SIDEBAR_FOOTER,
                fn () => view('components.filament.sidebar-user')
            );
    }
}
