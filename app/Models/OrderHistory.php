<?php

namespace App\Models;

use App\Enums\CourierIdentifierEnum;
use Database\Factories\OrderHistoryFactory;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Facades\URL;

class OrderHistory extends Model
{
    /** @use HasFactory<OrderHistoryFactory> */
    use HasFactory;

    protected $fillable = [
        'order_id',
        'event_type',
        'description',
        'shipment_id',
        'shipment_status',
        'shipment_company',
        'performed_by',
        'action_time',
        'additional_info',
        'reference',
    ];

    protected $casts = [
        'additional_info' => 'array',
    ];

    /**
     * @return BelongsTo<Order, $this>
     */
    public function order(): BelongsTo
    {
        return $this->belongsTo(Order::class);
    }

    public function getShipmentCompanyLogoAttribute(): string
    {
        if ($this->shipment_company === CourierIdentifierEnum::ARAMEX->value) {
            return URL::asset('build/images/aramex.png');
        }

        if ($this->shipment_company === CourierIdentifierEnum::BARQ->value) {
            return URL::asset('build/images/barq.png');
        }

        if ($this->shipment_company === CourierIdentifierEnum::TRANSCORP->value) {
            return URL::asset('build/images/transcorp.png');
        }

        return '';
    }
}
