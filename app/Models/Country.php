<?php

namespace App\Models;

use Database\Factories\CountryFactory;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Country extends Model
{
    /** @use HasFactory<CountryFactory> */
    use HasFactory;

    protected $fillable = [
        'name',
        'name_ar',
        'name_zid',
        'code_country',
        'phone_code',
        'phone_number_length',
    ];

    /**
     * @return string
     */
    public function getTranslatedNameAttribute()
    {
        $lang = app()->getLocale();

        return $this->attributes[$lang === 'ar' ? 'name_ar' : 'name'] ?? $this->attributes['name'];
    }

    /**
     * TODO: KEEP ONLY ONE PARAMETER, CODE OR NAME
     */
    public static function findByString(?string $name = null, ?string $code = null): ?int
    {
        return Country::where('name', $name)
            ->orWhere('name_ar', $name)
            ->orWhere('code_country', $code)
            ->orWhere('name_zid', $name)
            ->first()?->id;
    }
}
