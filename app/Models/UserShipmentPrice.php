<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class UserShipmentPrice extends Model
{
    protected $fillable = [
        'user_id',
        'shipment_courier_service_id',
        'base_price',
        'extra_weight_from',
        'additional_weight_cost',
        'cash_on_delivery_cost',
        'distance_cost',
        'is_active',
    ];

    protected $casts = [
        'is_active' => 'boolean',
    ];

    /**
     * @return BelongsTo<User, $this>
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * @return BelongsTo<ShipmentCourierService, $this>
     */
    public function shipmentCourierService(): BelongsTo
    {
        return $this->belongsTo(ShipmentCourierService::class, 'shipment_courier_service_id');
    }

    /**
     * Scope for shipments with "application".
     *
     * @param  Builder<UserShipmentPrice>  $query
     * @return Builder<UserShipmentPrice>
     */
    public function scopeActive(Builder $query): Builder
    {
        return $query->where('is_active', true);
    }

    /**
     * Get the courier identifier for this pricing configuration.
     */
    public function getCourierIdentifierAttribute(): string
    {
        return $this->shipmentCourierService->identifier;
    }
}
