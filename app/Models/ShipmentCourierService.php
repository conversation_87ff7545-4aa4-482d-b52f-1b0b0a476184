<?php

namespace App\Models;

use App\Enums\CourierIdentifierEnum;
use Database\Factories\ShipmentCourierServiceFactory;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class ShipmentCourierService extends Model
{
    /** @use HasFactory<ShipmentCourierServiceFactory> */
    use HasFactory;

    public $timestamps = false;

    protected $fillable = ['courier_name', 'service_name', 'logo', 'volumetric_divisor', 'base_volumetric_divisor'];

    public function getCourierNameAttribute(string $value): string
    {
        return __('translation.'.strtolower($value));
    }

    public function getServiceNameAttribute(string $value): string
    {
        return __('translation.'.strtolower($value));
    }

    public function getEditUrl(): string
    {
        if (str_contains($this->logo, 'aramex')) {
            return route('filament.merchant.resources.user-aramexes.edit', ['record' => $this]);
        }

        return route('filament.merchant.resources.user-transcorps.edit', ['record' => $this]);

    }

    public function getDeliveryTimeAttribute(): string
    {
        if ($this->identifier === CourierIdentifierEnum::ARAMEX->value) {
            return __('translation.delivery_time');
        }
        if ($this->identifier === CourierIdentifierEnum::BARQ->value) {
            return __('translation.same_day');
        }

        return __('translation.work_days');
    }

    public function getPickupAttribute(): string
    {
        if ($this->identifier === CourierIdentifierEnum::ARAMEX->value) {
            return __('translation.delivery_by_shipping_company');
        }
        if ($this->identifier === CourierIdentifierEnum::BARQ->value) {
            return __('translation.delivery_by_shipping_company');
        }

        return __('translation.delivery_by_shipping_company');
    }

    public function getDeliveryAttribute(): string
    {
        return __('translation.delivery_to_customer');
    }

    public function getShipmentsCostsAttribute(): mixed
    {
        return Order::shipmentsByCompany($this->identifier)
            ->get()
            ->sum(fn ($order) => $order->shipment_approved_cost);
    }

    public function getShipmentsOrderGrandTotalAttribute(): mixed
    {
        return Order::shipmentsByCompany($this->identifier)->sum('order_grand_total');
    }

    /**
     * @return HasMany<UserShipmentPrice, $this>
     */
    public function userShipmentPrices(): HasMany
    {
        return $this->hasMany(UserShipmentPrice::class);
    }

    /**
     * Get the volumetric divisor for this courier service.
     */
    public function getVolumetricDivisor(): int
    {
        return $this->volumetric_divisor;
    }

    /**
     * Get the base volumetric divisor for this courier service.
     */
    public function getBaseVolumetricDivisor(): int
    {
        return $this->base_volumetric_divisor;
    }
}
