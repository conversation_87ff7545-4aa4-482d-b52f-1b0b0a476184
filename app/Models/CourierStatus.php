<?php

namespace App\Models;

use App\Enums\OrderStatusEnum;
use Illuminate\Database\Eloquent\Model;

class CourierStatus extends Model
{
    protected $fillable = [
        'courier',
        'code',
        'description',
        'order_status',
    ];

    /**
     * Get the translated description for the current locale.
     */
    public function getTranslatedDescription(): ?string
    {
        return __("translation.{$this->courier}_status.{$this->order_status}");
    }

    /**
     * Get the OrderStatusEnum value for a given courier status code.
     */
    public static function getOrderStatus(string $courier, string $code): ?string
    {
        $status = self::where('courier', $courier)
            ->where('code', $code)
            ->first();

        if ($status) {
            return $status->order_status;
        }

        // Apply courier-specific fallback logic
        return match ($courier) {
            'barq', 'spl' => OrderStatusEnum::CURRENTLY_SHIPPING->value, // Default for unmapped Barq codes
            'transcorp', 'thabit' => OrderStatusEnum::AWAITING_PICKUP->value, // Default for unmapped Transcorp codes
            // Default for unmapped Thabit codes
            // Default for unmapped SPL codes
            default => null
        };
    }

    /**
     * Get all courier status codes mapped to a specific OrderStatusEnum value.
     *
     * @return string[]
     */
    public static function getCodesByOrderStatus(string $courier, string $orderStatus): array
    {
        return self::where('courier', $courier)
            ->where('order_status', $orderStatus)
            ->pluck('code')
            ->toArray();
    }

    /**
     * Get all available couriers with status mappings.
     *
     * @return string[]
     */
    public static function getAvailableCouriers(): array
    {
        return self::distinct('courier')
            ->pluck('courier')
            ->toArray();
    }
}
