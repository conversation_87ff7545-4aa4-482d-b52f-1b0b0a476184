<?php

namespace App\Models\Scopes;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Scope;
use Illuminate\Support\Facades\Auth;

class UserScope implements Scope
{
    /**
     * @param  Builder<Model>  $builder
     */
    public function apply(Builder $builder, Model $model): void
    {
        if (Auth::user() && Auth::user()->role === 'admin' && Auth::user()->hasRole('super_admin')) {
            return;
        }
        if (Auth::user() && Auth::user()->role === 'admin' && Auth::user()->hasRole('commercial')) {
            $merchantIds = Auth::user()->merchantUsers()->pluck('users.id');

            $builder->whereIn('user_id', $merchantIds);

            return;
        }
        if (Auth::user() && Auth::user()->role === 'admin') {
            return;
        }
        $builder->where('user_id', auth()->id());
    }
}
