<?php

namespace App\Models\Scopes;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Scope;
use Illuminate\Support\Facades\Auth;

class MerchantScope implements Scope
{
    /**
     * Apply the scope to a given Eloquent query builder.
     *
     * @param  Builder<Model>  $builder
     */
    public function apply(Builder $builder, Model $model): void
    {
        if (Auth::user() && Auth::user()->role === 'admin' && Auth::user()->hasRole('super_admin')) {
            return;
        }
        if (Auth::user() && Auth::user()->role === 'admin' && Auth::user()->hasRole('commercial')) {
            $merchantIds = Auth::user()->merchantUsers()->pluck('users.id');

            $builder->where(function ($query) use ($merchantIds) {
                $query->whereHas('merchant', fn ($q) => $q->whereIn('user_id', $merchantIds))
                    ->orWhereHas('warehouse', fn ($q) => $q->whereIn('user_id', $merchantIds));
            });

            return;
        }

        if (Auth::user() && Auth::user()->role === 'admin') {
            return;
        }

        $builder->whereHas('merchant', function ($merchantQuery) {
            $merchantQuery->where('user_id', Auth::id());
        })
            ->orWhereHas('warehouse', function ($merchantQuery) {
                $merchantQuery->where('user_id', Auth::id());
            });
    }
}
