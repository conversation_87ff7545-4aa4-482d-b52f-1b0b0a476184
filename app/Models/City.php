<?php

namespace App\Models;

use Database\Factories\CityFactory;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class City extends Model
{
    /** @use HasFactory<CityFactory> */
    use HasFactory;

    protected $fillable = [
        'name',
        'name_ar',
        'country_id',
    ];

    /**
     * @return HasMany<Warehouse,$this>
     */
    public function warehouse(): HasMany
    {
        return $this->hasMany(Warehouse::class);
    }

    /**
     * @return BelongsTo<Country, $this>
     */
    public function country(): BelongsTo
    {
        return $this->belongsTo(Country::class);
    }

    /**
     * @return string
     */
    public function getTranslatedNameAttribute()
    {
        $lang = app()->getLocale();

        return $this->attributes[$lang === 'ar' ? 'name_ar' : 'name'] ?? $this->attributes['name'];
    }

    public static function findByString(?string $city): ?int
    {
        if (empty($city)) {
            return null;
        }

        return City::where(function ($query) use ($city) {
            $query->whereLike('name_ar', $city)
                ->orWhereLike('name', $city)
                ->orWhereHas('cityTranslations', function ($q) use ($city) {
                    $q->whereLike('value', $city);
                });
        })->first()?->id;
    }

    /**
     * @return HasMany<CityTranslation,$this>
     */
    public function cityTranslations(): HasMany
    {
        return $this->hasMany(CityTranslation::class);
    }
}
