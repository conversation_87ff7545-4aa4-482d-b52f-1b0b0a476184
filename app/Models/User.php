<?php

namespace App\Models;

use Database\Factories\UserFactory;
use Filament\Models\Contracts\FilamentUser;
use Filament\Models\Contracts\HasAvatar;
use Filament\Models\Contracts\HasName;
use Filament\Panel;
use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Lab404\Impersonate\Models\Impersonate;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;
use Spatie\Permission\Traits\HasRoles;

class User extends Authenticatable implements FilamentUser, HasAvatar, HasName, MustVerifyEmail
{
    /** @use HasFactory<UserFactory> */
    use HasFactory, Notifiable;

    use HasRoles;
    use Impersonate;
    use LogsActivity;

    /** @var list<string> */
    protected $fillable = [
        'first_name',
        'last_name',
        'email',
        'password',
        'dob',
        'avatar',
        'phone',
        'role',
        'email_verified_at',
        'company_city_id',
        'company_name',
        'company_commercial_registration_number',
        'company_tax_number',
        'rate_cost',
    ];

    /**
     * The attributes that should be hidden for arrays.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * The attributes that should be cast to native types.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
    ];

    /**
     * @return HasMany<Merchant, $this>
     */
    public function merchants(): HasMany
    {
        return $this->hasMany(Merchant::class);
    }

    /**
     * Get the user's first merchant.
     */
    public function getMerchantAttribute(): ?Merchant
    {
        return $this->merchants()->first();
    }

    /**
     * @return HasMany<Warehouse, $this>
     */
    public function warehouses(): HasMany
    {
        return $this->hasMany(Warehouse::class);
    }

    public function getFullNameAttribute(): string
    {
        return $this->first_name.' '.$this->last_name;
    }

    /**
     * @return HasMany<UserShipmentCourierService, $this>
     */
    public function shipments(): HasMany
    {
        return $this->hasMany(UserShipmentCourierService::class);
    }

    /**
     * @return HasMany<UserShipmentPrice, $this>
     */
    public function shipmentPrices(): HasMany
    {
        return $this->hasMany(UserShipmentPrice::class);
    }

    /**
     * @return HasOne<SurveyResponse,$this>
     */
    public function survey(): HasOne
    {
        return $this->hasOne(SurveyResponse::class)->latestOfMany();
    }

    public function canAccessPanel(Panel $panel): bool
    {
        if ($panel->getId() === 'admin') {
            return $this->role === 'admin';
        }

        return $this->role === 'merchant';
    }

    /**
     * @return string[]
     */
    public function getShippingConfig(string $serviceName): array
    {
        $relation = $this->shipments()
            ->whereHas('service', function ($query) use ($serviceName) {
                $query->where('identifier', $serviceName);
            })
            ->first();
        if (! $relation) {
            $jsonMessage = json_encode([
                'error' => 'Shipping Service not found',
                'user_id' => $this->id,
                'user_email' => $this->email,
            ], JSON_UNESCAPED_UNICODE);
            if ($jsonMessage === false) {
                throw new \Exception('JSON encode failed: '.json_last_error_msg());
            }
            throw new \Exception($jsonMessage);
        }

        return $relation->toArray();
    }

    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()->logAll()->logOnlyDirty();
    }

    public function getFilamentName(): string
    {
        return "{$this->first_name} {$this->last_name}";
    }

    public function getFilamentAvatarUrl(): ?string
    {
        if ($this->avatar) {
            return asset('storage/'.$this->avatar);
        }

        return null;
    }

    protected static function booted(): void
    {
        static::creating(function (User $user) {
            $user->role = 'merchant';
        });
    }

    /**
     * @return HasMany<WalletTransaction, $this>
     */
    public function walletTransactions(): HasMany
    {
        return $this->hasMany(WalletTransaction::class);
    }

    // Accessor: Convert cents to dollars when retrieving
    public function getWalletBalanceFormattedAttribute(): float|int
    {
        return $this->wallet_balance / 100;
    }

    public function decreaseWalletBalance(int $value): void
    {
        \DB::transaction(function () use ($value) {
            // Lock the user row for update to prevent race conditions
            $this->refresh();
            $user = self::lockForUpdate()->find($this->id);

            $user->wallet_balance -= $value;
            $user->save();

            // Update the current instance
            $this->wallet_balance = $user->wallet_balance;
        });
    }

    public function increaseWalletBalanceByCorrectValue(int $value): void
    {
        \DB::transaction(function () use ($value) {
            $this->refresh();
            $user = self::lockForUpdate()->find($this->id);

            $user->wallet_balance += $value;
            $user->save();

            // Update the current instance
            $this->wallet_balance = $user->wallet_balance;
        });
    }

    /**
     * @return BelongsTo<City,$this>
     */
    public function companyCity(): BelongsTo
    {
        return $this->belongsTo(City::class);
    }

    public function getShipmentsCostsAttribute(): mixed
    {
        return Order::shipmentsByUser($this->id)
            ->get()
            ->sum(fn ($order) => $order->shipment_approved_cost);
    }

    public function getShipmentsOrderGrandTotalAttribute(): mixed
    {
        return Order::shipmentsByUser($this->id)->sum('order_grand_total');
    }

    /**
     * @return BelongsToMany<User, $this>
     */
    public function merchantUsers(): BelongsToMany
    {
        return $this->belongsToMany(User::class, 'admin_clients', 'admin_id', 'user_id')
            ->where('role', 'merchant');
    }

    /**
     * Scope for users that are supervised by the given admin user.
     *
     * @param  Builder<User>  $query
     * @return Builder<User>
     */
    public function scopeSupervisedBy(Builder $query, ?User $admin): Builder
    {
        if ($admin->hasRole('super_admin')) {
            return $query;
        }

        if ($admin->hasRole('commercial')) {
            return $query->whereIn('id', $admin->merchantUsers()->pluck('users.id'));
        }
        if (! $admin) {
            return $query->whereRaw('1 = 0');
        }

        return $query;
    }

    public function getShippingService(): mixed
    {
        if (! method_exists($this, 'shippingService')) {
            return null;
        }
        $service = $this->shippingService();
        if (! $service) {
            $jsonMessage = json_encode([
                'error' => 'Shipping Service not found',
                'user_id' => $this->id,
                'user_email' => $this->email,
            ], JSON_UNESCAPED_UNICODE);
            if ($jsonMessage === false) {
                throw new \Exception('JSON encode failed: '.json_last_error_msg());
            }
            throw new \Exception($jsonMessage);
        }

        return $service;
    }

    public function getHasCorrectBalanceAttribute(): bool
    {
        $sumDebit = $this->walletTransactions()->where('type', 'debit')->sum('amount') / 100;
        $sumCharge = $this->walletTransactions()->where('type', 'credit')->sum('amount') / 100;
        $sumCancel = $this->walletTransactions()->where('type', 'cancel')->sum('amount') / 100;
        $sumChargeCancel = $sumCharge + $sumCancel;
        $total = (int) ($sumChargeCancel * 100) - (int) round($sumDebit * 100);

        return $total === $this->wallet_balance;
    }

    /**
     * Scope for users with correct balance.
     *
     * @param  Builder<User>  $query
     * @return Builder<User>
     */
    public function scopeHasCorrectBalance(Builder $query, bool $isCorrect = true): Builder
    {
        // This matches the logic in getHasCorrectBalanceAttribute
        $operator = $isCorrect ? '=' : '!=';

        return $query->whereRaw('(
            (SELECT (COALESCE(credit.total, 0) + COALESCE(cancel.total, 0) - COALESCE(debit.total, 0))
                FROM users u
                LEFT JOIN (
                    SELECT user_id, SUM(amount) as total
                    FROM wallet_transactions
                    WHERE type = "debit"
                    GROUP BY user_id
                ) debit ON debit.user_id = users.id
                LEFT JOIN (
                    SELECT user_id, SUM(amount) as total
                    FROM wallet_transactions
                    WHERE type = "credit"
                    GROUP BY user_id
                ) credit ON credit.user_id = users.id
                LEFT JOIN (
                    SELECT user_id, SUM(amount) as total
                    FROM wallet_transactions
                    WHERE type = "cancel"
                    GROUP BY user_id
                ) cancel ON cancel.user_id = users.id
                WHERE u.id = users.id
            ) '.$operator.' users.wallet_balance
        )');
    }
}
