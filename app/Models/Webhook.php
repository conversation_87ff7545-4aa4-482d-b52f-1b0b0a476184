<?php

namespace App\Models;

use Database\Factories\WebhookFactory;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Webhook extends Model
{
    /** @use HasFactory<WebhookFactory> */
    use HasFactory;

    protected $fillable = [
        'payload',
        'status',
        'error_message',
        'stack_trace',
        'url',
    ];

    protected $casts = [
        'payload' => 'array',
    ];
}
