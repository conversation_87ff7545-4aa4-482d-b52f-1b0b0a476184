<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\Pivot;

class OrderProduct extends Pivot
{
    protected $table = 'order_product';

    protected $fillable = ['order_id', 'product_id', 'quantity', 'price'];

    public $timestamps = true;

    /**
     * @return BelongsTo<Order,$this>
     */
    public function order(): BelongsTo
    {
        return $this->belongsTo(Order::class);
    }

    /**
     * @return BelongsTo<Product,$this>
     */
    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class);
    }
}
