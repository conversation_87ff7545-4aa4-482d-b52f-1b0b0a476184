<?php

namespace App\Models;

use App\Models\Scopes\UserScope;
use Database\Factories\CodWalletFactory;
use Illuminate\Database\Eloquent\Attributes\ScopedBy;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

#[ScopedBy([UserScope::class])]
class CodWallet extends Model
{
    /** @use HasFactory<CodWalletFactory> */
    use HasFactory;

    protected $table = 'cod_wallet';

    protected $fillable = [
        'user_id',
        'amount',
        'status',
        'type',
        'bank_invoice',
        'paid_at',
    ];

    protected $casts = [
        'paid_at' => 'datetime',
    ];

    /**
     * @return BelongsTo<User, $this>
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * @return HasMany<Order, $this>
     */
    public function orders(): HasMany
    {
        return $this->hasMany(Order::class, 'cod_wallet_id');
    }
}
