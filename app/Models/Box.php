<?php

namespace App\Models;

use App\Models\Scopes\UserScope;
use Database\Factories\BoxFactory;
use Illuminate\Database\Eloquent\Attributes\ScopedBy;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

#[ScopedBy([UserScope::class])]
class Box extends Model
{
    /** @use HasFactory<BoxFactory> */
    use HasFactory;

    protected $fillable = [
        'type',
        'length',
        'width',
        'height',
        'weight',
        'user_id',
    ];
}
