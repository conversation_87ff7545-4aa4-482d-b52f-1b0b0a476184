<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class OrderBox extends Model
{
    protected $fillable = [
        'order_id',
        'box_id',
        'length',
        'width',
        'height',
        'weight',
    ];

    /**
     * @return BelongsTo<Order, $this>
     */
    public function order(): BelongsTo
    {
        return $this->belongsTo(Order::class, 'order_id', 'id');
    }

    public function getWeightAttribute(): float
    {
        $rawWeight = $this->attributes['weight'] ?? 0;

        return round($rawWeight / 1000);
    }

    public function getWeightWithGramAttribute(): int
    {
        return $this->attributes['weight'];
    }

    public function setWeightAttribute(int|float|string $value): void
    {
        $this->attributes['weight'] = ((int) $value) * 1000;
    }
}
