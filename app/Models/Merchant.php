<?php

namespace App\Models;

use App\Enums\SalesChannelEnum;
use App\Models\Scopes\UserScope;
use Database\Factories\MerchantFactory;
use Illuminate\Database\Eloquent\Attributes\ScopedBy;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

#[ScopedBy([UserScope::class])]
class Merchant extends Model
{
    /** @use HasFactory<MerchantFactory> */
    use HasFactory;

    protected $fillable = [
        'name',
        'user_id',
        'domain',
        'merchant_id',
        'access_token',
        'refresh_token',
        'authorization',
        'metadata',
        'expires_in',
        'active',
        'type',
        'webhook_url',
        'warehouse_id',
        'trigger_url',
        'api_key',
        'api_secret_key',
        'webhook_is_linked',
    ];

    //    /**
    //     * @return Attribute<string, never>
    //     */
    //    protected function webhookUrl(): Attribute
    //    {
    //        return Attribute::make(
    //            get: fn (string $value) => ucfirst($value),
    //        );
    //    }

    /**
     * @return Attribute<string, never>
     */
    protected function image(): Attribute
    {
        return Attribute::make(
            get: function (mixed $value, array $attributes) {
                return match ($attributes['type']) {
                    SalesChannelEnum::SALLA->value => 'https://storage.googleapis.com/tryoto-public/sales-channels-logo/salla.png',
                    SalesChannelEnum::SHOPIFY->value => 'https://storage.googleapis.com/tryoto-public/sales-channels-logo/shopify.png',
                    SalesChannelEnum::ZID->value => 'https://storage.googleapis.com/tryoto-public/sales-channels-logo/zid.png',
                    SalesChannelEnum::WOOCOMMERCE->value => asset('images/'.SalesChannelEnum::WOOCOMMERCE->value.'-logo.png'),
                    default => asset('images/'.$attributes['type'].'-logo.png')
                };
            },
        );
    }

    public function hasExpired(): bool
    {
        return now()->timestamp > $this->expires_in;
    }

    /**
     * @return Attribute<string, never>
     */
    protected function formUrl(): Attribute
    {
        return Attribute::make(
            get: function (mixed $value, array $attributes) {
                return match ($attributes['type']) {
                    SalesChannelEnum::SALLA->value => "/merchant/salla-merchants/$this->id/edit",
                    SalesChannelEnum::SHOPIFY->value => "/merchant/shopify-merchants/$this->id/edit",
                    SalesChannelEnum::ZID->value => "/merchant/zid-merchants/$this->id/edit",
                    SalesChannelEnum::WOOCOMMERCE->value => "/merchant/woo-commerce-merchants/$this->id/edit",
                    default => "/merchant/$this->type-merchants/$this->id/edit"
                };
            },
        );
    }

    /**
     * @return BelongsTo<User, $this>
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * @return BelongsTo<Warehouse,$this>
     */
    public function warehouse(): BelongsTo
    {
        return $this->belongsTo(Warehouse::class);
    }

    /**
     * @return HasMany<Order,$this>
     */
    public function orders(): HasMany
    {
        return $this->hasMany(Order::class);
    }
}
