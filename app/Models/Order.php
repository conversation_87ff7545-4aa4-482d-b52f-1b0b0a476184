<?php

namespace App\Models;

use App\Dto\ShippingRateDto;
use App\Enums\CourierIdentifierEnum;
use App\Enums\OrderHistoryEnum;
use App\Enums\OrderStatusEnum;
use App\Enums\SalesChannelEnum;
use App\Models\Scopes\MerchantScope;
use App\Services\SalesChannelService;
use App\Services\SettingsService;
use App\Services\ShippingServiceFactory;
use App\Services\TranscorpSBService;
use Carbon\Carbon;
use Database\Factories\UserFactory;
use DateTime;
use Illuminate\Database\Eloquent\Attributes\ScopedBy;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Http\Client\ConnectionException;
use Illuminate\Support\Facades\Auth;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;

#[ScopedBy([MerchantScope::class])]
class Order extends Model
{
    /** @use HasFactory<UserFactory> */
    use HasFactory;

    use LogsActivity;

    protected $fillable = [
        'order_number',
        'date',
        'payment_method',
        'status',
        'external_id',
        'order_grand_total',
        'warehouse_id',
        'description',
        'shipment_total_weight',
        'receiver_first_name',
        'receiver_last_name',
        'receiver_country_code',
        'receiver_phone',
        'receiver_email',
        'receiver_country_id',
        'receiver_country',
        'receiver_city',
        'receiver_address_line',
        'receiver_postal_code',
        'receiver_street_name',
        'receiver_block',
        'receiver_latitude',
        'receiver_longitude',
        'shipper_name',
        'shipper_email',
        'shipper_phone',
        'shipper_country_id',
        'shipper_city',
        'shipper_address_line',
        'shipper_latitude',
        'shipper_longitude',
        'merchant_id',
        'merchant_email',
        'merchant_phone',
        'tax',
        'webhook_id',
        'shipment_credentials',
        'shipment_credentials_type',
        'shipment_label_url',
        'shipment_company',
        'shipment_tracking_link',
        'shipment_reference',
        'shipment_logo',
        'source',
        'return_type',
        'base_shipment_cost',
        'base_shipment_cod_cost',
        'currency',
        'add_box', // Virtual attribute for box addition
    ];

    protected $casts = [
        'shipment_credentials' => 'array',
    ];

    /**
     * @return BelongsTo<PaymentMethod,$this>
     */
    public function paymentMethod(): BelongsTo
    {
        return $this->belongsTo(PaymentMethod::class, 'payment_method_id');
    }

    //    /**
    //     * @return BelongsTo<Currency,$this>
    //     */
    //    public function currency(): BelongsTo
    //    {
    //        return $this->belongsTo(Currency::class, 'currency_id');
    //    }

    /**
     * @return BelongsTo<Warehouse,$this>
     */
    public function warehouse(): BelongsTo
    {
        return $this->belongsTo(Warehouse::class, 'warehouse_id');
    }

    /**
     * @return HasMany<OrderItem,$this>
     */
    public function products(): HasMany
    {
        return $this->hasMany(OrderItem::class, 'order_id');
    }

    /**
     * @return HasMany<OrderBox,$this>
     */
    public function boxes(): HasMany
    {
        return $this->hasMany(OrderBox::class, 'order_id');
    }

    public function boxesCount(): int
    {
        return $this->boxes()->count();
    }

    /**
     * @return HasMany<OrderItem,$this>
     */
    public function items(): HasMany
    {
        return $this->hasMany(OrderItem::class, 'order_id');
    }

    /**
     * @return BelongsTo<Merchant, $this>
     */
    public function merchant(): BelongsTo
    {
        return $this->belongsTo(Merchant::class, 'merchant_id');
    }

    public function getReceiverFullNameAttribute(): string
    {
        return $this->receiver_first_name.' '.$this->receiver_last_name;
    }

    public function getAddBoxAttribute(): int
    {
        return $this->boxes()->count();
    }

    public function setAddBoxAttribute(mixed $value): void
    {
        $currentCount = $this->boxes()->count();
        $targetValue = (int) $value;

        if ($targetValue > $currentCount) {
            // Add boxes - create the difference
            $boxesToAdd = $targetValue - $currentCount;

            // First, ensure the primary box template exists for this merchant
            $primaryBox = \App\Models\Box::firstOrCreate([
                'type' => 'primary',
                'user_id' => $this->merchant ? $this->merchant->user_id : $this->warehouse->user_id,
            ], [
                'type' => 'primary',
                'width' => 10,
                'length' => 10,
                'height' => 10,
                'user_id' => $this->merchant ? $this->merchant->user_id : $this->warehouse->user_id,
            ]);

            // Create the required number of boxes
            for ($i = 0; $i < $boxesToAdd; $i++) {
                $this->boxes()->create([
                    'box_id' => $primaryBox->id,
                    'length' => $primaryBox->length,
                    'width' => $primaryBox->width,
                    'height' => $primaryBox->height,
                ]);
            }
        } elseif ($targetValue < $currentCount && $targetValue >= 0) {
            // Remove boxes - delete the difference
            $boxesToRemove = $currentCount - $targetValue;

            // Get the latest boxes and delete them
            $this->boxes()
                ->latest()
                ->take($boxesToRemove)
                ->get()
                ->each(function ($box) {
                    $box->delete();
                });
        }
    }

    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()->logAll()->logOnlyDirty();
    }

    /**
     * @return HasMany<OrderHistory,$this>
     */
    public function orderHistories(): HasMany
    {
        return $this->hasMany(OrderHistory::class);
    }

    private function logHistory(string $eventType, ?string $description = null, ?int $performedBy = null, ?string $shipmentId = null): void
    {
        $this->orderHistories()->create([
            'event_type' => $eventType,
            'performed_by' => $performedBy,
            'description' => $description,
            'shipment_id' => $shipmentId,
        ]);
    }

    public function logOrderCreation(): void
    {
        $this->logHistory(eventType: OrderHistoryEnum::NEW->value, description: 'Order Created');
    }

    public function logShipmentCreation(string $shipmentId): void
    {
        /** @var \App\Models\User $authenticatedUser */
        $authenticatedUser = Auth::user();
        $this->logHistory(eventType: OrderHistoryEnum::SEARCHING_DRIVER->value, description: 'Shipment Created By '.$authenticatedUser->first_name, shipmentId: $shipmentId);
    }

    public function logShipmentStatusChange(string $shipmentId): void
    {
        $this->logHistory(eventType: 'shipmentInProgress', description: 'SH314-Pickup Completed', shipmentId: $shipmentId);
    }

    /**
     * @throws ConnectionException
     */
    public function cancelOrder(): void
    {
        if ($this->shipment_reference !== null) {
            /** @var User $user */
            $user = Auth::user();
            $refundAmount = $this->createdWithGlobalConfig() ? $this->shipment_cost : ($this->applied_rate_cost ?? $user->rate_cost);
            $user->increaseWalletBalanceByCorrectValue($refundAmount);
            $user->save();
            $user->walletTransactions()->create([
                'type' => 'cancel',
                'amount' => $refundAmount,
                'description' => 'رسوم شحنة إلغاء'.$this->shipment_company,
                'order_id' => $this->id,
            ]);
        }
        if ($this->shipment_company === CourierIdentifierEnum::TRANSCORP->value) {
            $factory = app(ShippingServiceFactory::class);
            $service = $factory->create(serviceName: CourierIdentifierEnum::TRANSCORP->value, useGlobalConfig: $this->createdWithGlobalConfig(), order: $this);
            /** @var TranscorpSBService $service */
            $service->cancelShipment($this->order_number);

        }
        if ($this->shipment_company === CourierIdentifierEnum::JT->value) {
            $factory = app(ShippingServiceFactory::class);
            $service = $factory->create(serviceName: CourierIdentifierEnum::JT->value, useGlobalConfig: $this->createdWithGlobalConfig(), order: $this);
            /** @var TranscorpSBService $service */
            $service->cancelShipment($this->order_number);

        }
        if ($this->shipment_company === CourierIdentifierEnum::THABIT->value) {
            $factory = app(ShippingServiceFactory::class);
            $service = $factory->create(serviceName: CourierIdentifierEnum::THABIT->value, useGlobalConfig: $this->createdWithGlobalConfig(), order: $this);
            /** @var \App\Services\AlthabitService $service */
            $service->cancelShipment($this->shipment_external_id);

        }
        // Clear shipment related data
        $this->update([
            'shipment_label_url' => null,
            'shipment_tracking_link' => null,
            'shipment_reference' => null,
            'shipment_company' => '',
            'shipment_logo' => null,
            'shipment_credentials' => null,
            'shipment_credentials_type' => null,
            'volumetric_divisor' => null,
        ]);

        // Update status using centralized method
        $this->updateStatus(OrderStatusEnum::CANCELED->value);

        if ($this->source === SalesChannelEnum::ZID->value) {
            $salesChannelService = new SalesChannelService($this->merchant);
            $salesChannelService->updateStatus($this);
        }

    }

    /**
     * Centralized method to update order status with all necessary side effects
     */
    public function updateStatus(string $newStatus): void
    {
        $oldStatus = $this->status;

        // Don't update if status hasn't changed
        if ($oldStatus === $newStatus || $newStatus === OrderStatusEnum::NOT_CHANGED->value) {
            return;
        }

        // Update the status
        $this->update(['status' => $newStatus]);

        // Log the status change
        $this->logHistory(eventType: $newStatus);
    }

    public function markDelivered(): void
    {
        $this->updateStatus(OrderStatusEnum::DELIVERED->value);
        if ($this->merchant) {
            $salesChannelService = new SalesChannelService($this->merchant);
            $salesChannelService->updateStatus($this);
        }
    }

    public function createdWithGlobalConfig(): bool
    {
        // TODO: REMOVE || !shipment_credentials_type we need to always set a value
        return $this->shipment_credentials_type === 'application' || ! $this->shipment_credentials_type;
    }

    // Accessor: Convert cents to dollars when retrieving
    public function getOrderGrandTotalAttribute(int $value): float|int
    {
        return $value / 100;
    }

    // Mutator: Convert dollars to cents when setting
    public function setOrderGrandTotalAttribute(int|float $value): void
    {
        $this->attributes['order_grand_total'] = $value * 100;
    }

    public function isCod(): bool
    {
        return $this->payment_method === 'cod';
    }

    public function isInternational(): bool
    {
        return $this->receiverCountry->code_country !== $this->shipperCountry->code_country;
    }

    public function isSameCity(): bool
    {
        return $this->receiverCity?->name === $this->shipper_city;
    }

    public function getWeightVolume(): int
    {
        $divisor = $this->volumetric_divisor ?? app(SettingsService::class)->getVolumetricDivisor();

        $totalWeightVolume = $this->boxes()->get()->sum(function ($box) use ($divisor) {
            return ($box->length * $box->width * $box->height) / $divisor;
        });

        return (int) round($totalWeightVolume);
    }

    public function getWeight(): int
    {

        $weight = $this->boxes()->get()->sum(function ($box) {
            return $box->weight_with_gram;
        });

        return (int) ceil($weight / 1000);
    }

    public function getMaxWeight(): int
    {
        if ($this->getWeightVolume() > $this->getWeight()) {
            return $this->getWeightVolume();
        }

        return $this->getWeight();
    }

    public function getMaxWeightAfterCorrecting(): int
    {
        $weightFromTotalWeight = (int) ceil($this->shipment_total_weight / 1000);

        if ($this->getWeightVolume() > $weightFromTotalWeight) {
            return $this->getWeightVolume();
        }

        return $weightFromTotalWeight;
    }

    /**
     * @deprecated we should call the shipment service and get the rate from it
     */
    public function getPriceAttribute(): int
    {
        $factory = app(ShippingServiceFactory::class);
        $serviceInstance = $factory->create(CourierIdentifierEnum::ARAMEX->value, true, $this);

        return $serviceInstance->getRates(ShippingRateDto::fromOrder($this));
    }

    /**
     * @return BelongsTo<City,$this>
     */
    public function receiverCity(): BelongsTo
    {
        return $this->belongsTo(City::class);
    }

    /**
     * @return BelongsTo<Country, $this>
     */
    public function receiverCountry(): BelongsTo
    {
        return $this->belongsTo(Country::class);
    }

    public function getErrorAttribute(): string
    {
        $errors = [];

        if (! $this->receiverCity) {
            $errors[] = __('translation.order_errors.wrong_city');
        }
        if (! $this->receiverCountry) {
            $errors[] = __('translation.order_errors.wrong_country');
        }
        if (! $this->receiver_first_name) {
            $errors[] = __('translation.order_errors.wrong_receiver_first_name');
        }
        if (! $this->receiver_last_name) {
            $errors[] = __('translation.order_errors.wrong_receiver_last_name');
        }
        if (isPhoneInvalid($this->receiver_phone, $this->receiver_country_id)) {
            $errors[] = __('translation.order_errors.wrong_reciever_phone_number');
        }

        return empty($errors) ? __('translation.order_errors.no_error') : implode(', ', $errors);
    }

    /**
     * Get all restrictions for a specific shipment company
     *
     * @return string[]
     */
    public function getShipmentRestriction(string $shipmentCompany): array
    {
        $restrictions = [];

        switch ($shipmentCompany) {
            case CourierIdentifierEnum::SPL->value:
                if ($this->hasWeightItemsRestriction(CourierIdentifierEnum::SPL->value)) {
                    $restrictions[] = CourierIdentifierEnum::SPL->value.'.weight_restriction';
                }
                if ($this->items->count() === 0) {
                    $restrictions[] = CourierIdentifierEnum::SPL->value.'.mandatory_item_restriction';
                }
                if ($this->isCod()) {
                    $restrictions[] = CourierIdentifierEnum::SPL->value.'.cod_restriction';
                }
                break;
            case CourierIdentifierEnum::TRANSCORP->value:
                // Add TRANSCORP restrictions here if any
                break;
            case CourierIdentifierEnum::JT->value:
                // Add JT restrictions here if any
                break;
        }

        return $restrictions;
    }

    public function hasWeightItemsRestriction(string $shipmentCompany): bool
    {
        switch ($shipmentCompany) {
            case CourierIdentifierEnum::SPL->value:
                // Check if total weight from boxes exceeds 30kg
                foreach ($this->boxes as $box) {
                    if ($box->weight > 30) {
                        return true;
                    }
                }

                return false;
            default:
                return false;
        }
    }

    // public function getReceiverCityWithFallback(string $source): string
    // {
    //     if (! $this->receiverCity) {
    //         throw new \Exception('city not found');
    //     }
    //     $sourceTranslation = $this->receiverCity->cityTranslations()
    //         ->where('source', 'like', '%'.$source.'%')
    //         ->first();

    //     if ($sourceTranslation)
    //     {
    //         return $sourceTranslation->value;
    //     }
    //     if ($this->receiverCity)
    //     {
    //         $this->receiverCity->name;
    //     }
    //     return '';
    // }

    public function getReceiverCityWithFallback(string $source): string
    {
        if (! $this->receiverCity) {
            $jsonMessage = json_encode([
                'error' => 'city not found',
                'order_id' => $this->id,
                'receiver_city_id' => $this->receiver_city_id,
                'receiver_city' => $this->receiver_city,
                'source' => $source,
            ], JSON_UNESCAPED_UNICODE);
            if ($jsonMessage === false) {
                throw new \Exception('JSON encode failed: '.json_last_error_msg());
            }
            throw new \Exception($jsonMessage);
        }
        $sourceTranslation = $this->receiverCity->cityTranslations()
            ->where('source', 'like', '%'.$source.'%')
            ->first();

        return $sourceTranslation->value ?? $this->receiverCity->name;
    }

    public function resetOrder(): void
    {
        if ($this->shipment_reference) {
            /** @var User $user */
            $user = Auth::user();
            $refundAmount = $this->createdWithGlobalConfig() ? $this->shipment_cost : ($this->applied_rate_cost ?? $user->rate_cost);
            $user->increaseWalletBalanceByCorrectValue($refundAmount);
            $user->save();
            $user->walletTransactions()->create([
                'type' => 'cancel',
                'amount' => $refundAmount,
                'description' => 'رسوم شحنة إلغاء'.$this->shipment_company,
                'order_id' => $this->id,
            ]);
        }
        // Clear shipment related data
        $this->update([
            'shipment_label_url' => null,
            'shipment_tracking_link' => null,
            'shipment_reference' => null,
            'shipment_company' => '',
            'shipment_logo' => null,
            'shipment_credentials' => null,
            'selected_shipment_company' => null,
            'shipment_credentials_type' => null,
            'volumetric_divisor' => null,
        ]);

        // Update status using centralized method
        $this->updateStatus(OrderStatusEnum::PENDING->value);
        if ($this->merchant) {
            $salesChannelService = new SalesChannelService($this->merchant);
            $salesChannelService->updateStatus($this);

        }
    }

    /**
     * @return BelongsTo<Country, $this>
     */
    public function shipperCountry(): BelongsTo
    {
        return $this->belongsTo(Country::class);
    }

    public function duplicate(): self
    {
        // Create a new order with the same attributes except unique ones
        $newOrder = $this->replicate([
            'id', 'status', 'created_at', 'updated_at', 'external_id', 'webhook_id', 'shipment_company', 'shipment_reference', 'shipment_tracking_url', 'shipment_status', 'shipment_cost', 'shipment_cost_without_cod', 'shipment_credentials', 'shipment_label_url', 'volumetric_divisor', 'applied_rate_cost',
        ]);

        // Optionally modify attributes before saving (e.g., change status)
        $newOrder->order_number = $this->order_number.rand(1, 10);
        $newOrder->date = now();
        $newOrder->status = OrderStatusEnum::PENDING->value;

        $newOrder->save();

        // Update status using centralized method

        // Duplicate order items
        foreach ($this->items as $item) {
            $newItem = $item->replicate(['id', 'order_id']);
            $newItem->order_id = $newOrder->id;
            $newItem->save();
        }

        // Duplicate order boxes if applicable
        foreach ($this->boxes as $box) {
            $newBox = $box->replicate(['id', 'order_id']);
            $newBox->order_id = $newOrder->id;
            $newBox->save();
        }

        return $newOrder;
    }

    /**
     * Scope for shipments with "application".
     *
     * @param  Builder<Order>  $query
     * @return Builder<Order>
     */
    public function scopeWithApplication(Builder $query): Builder
    {
        return $query->where('shipment_credentials_type', 'application');
    }

    /**
     * Scope for shipments without "application" (including NULL values).
     *
     * @param  Builder<Order>  $query
     * @return Builder<Order>
     */
    public function scopeWithoutApplication(Builder $query): Builder
    {
        return $query->where(function (Builder $query) {
            $query->whereNot('shipment_credentials_type', 'application')
                ->orWhereNull('shipment_credentials_type');
        });
    }

    /**
     * @return string[]
     */
    public function getAvailableShipmentCompanies(): array
    {
        $destinations = [
            'aramex' => __('translation.aramex'),
        ];

        if ($this->canUseBarq()) {
            $destinations['barq'] = __('translation.barq');
        }

        if (! $this->isInternational()) {
            $destinations['transcorp'] = __('translation.transcorp');
            $destinations['jt'] = __('translation.jt');
            $destinations['spl'] = __('translation.spl');
        }
        if ($this->canUseThabit()) {
            $destinations['thabit'] = __('translation.thabit');

        }

        return $destinations;
    }

    /**
     * @return string[]
     */
    public function getAvailableReverseShipmentCompanies(): array
    {
        if ($this->isInternational()) {
            return [];
        }
        $destinations = [
            'aramex' => __('translation.aramex'),
            'jt' => __('translation.jt'),
            'spl' => __('translation.spl'),
        ];

        if ($this->canUseThabit()) {
            $destinations['thabit'] = __('translation.thabit');

        }

        return $destinations;
    }

    public function canUseBarq(): bool
    {
        $isSameCity = $this->isSameCity();
        if (! $isSameCity) {
            return false;
        }
        if ($this->receiverCity->name !== 'Jeddah' && $this->receiver_city !== 'Riyadh') {
            return false;
        }

        return true;
    }

    private function canUseThabit(): bool
    {
        return $this->receiverCity->cityTranslations()
            ->where('source', 'name_thabit')
            ->exists();
    }

    /**
     * Scope for shipments that belongs to user either through merchant or warehouse
     *
     * @param  Builder<Order>  $query
     * @return Builder<Order>
     */
    public function scopeShipments(Builder $query): Builder
    {
        return $query->whereNotNull('shipment_reference')
            ->where('status', '!=', OrderStatusEnum::CANCELED->value)
            ->orderBy('updated_at', 'desc');
    }

    /**
     * Scope for shipments that belongs to user either through merchant or warehouse
     *
     * @param  Builder<Order>  $query
     * @return Builder<Order>
     */
    public function scopeShipmentsByUser(Builder $query, ?int $userId): Builder
    {
        return $query->where(function ($q) use ($userId): void {
            $q->whereHas('merchant', fn ($subQuery) => $subQuery->where('user_id', $userId))
                ->orWhereHas('warehouse', fn ($subQuery) => $subQuery->where('user_id', $userId));
        })
            ->whereNotNull('shipment_reference')
            ->where('status', '!=', OrderStatusEnum::CANCELED->value)
            ->orderBy('updated_at', 'desc');
    }

    /**
     * Scope for shipments that belongs to user either through merchant or warehouse
     *
     * @param  Builder<Order>  $query
     * @return Builder<Order>
     */
    public function scopeShipmentsByAdmin(Builder $query, User $admin): Builder
    {
        $merchantIds = $admin->merchantUsers()->pluck('users.id');

        return $query->where(function ($query) use ($merchantIds) {
            $query->whereHas('merchant', fn ($q) => $q->whereIn('user_id', $merchantIds))
                ->orWhereHas('warehouse', fn ($q) => $q->whereIn('user_id', $merchantIds));
        })
            ->whereNotNull('shipment_reference')
            ->where('status', '!=', OrderStatusEnum::CANCELED->value)
            ->orderBy('updated_at', 'desc');
    }

    /**
     * Scope for shipments that belongs to given company
     *
     * @param  Builder<Order>  $query
     * @return Builder<Order>
     */
    public function scopeShipmentsByCompany(Builder $query, ?string $companyId): Builder
    {
        return $query->where('shipment_company', $companyId)
            ->where('status', '!=', OrderStatusEnum::CANCELED->value)
            ->orderBy('updated_at', 'desc');
    }

    public function getDelivredAtAttribute(): ?DateTime
    {
        if ($this->status === OrderStatusEnum::DELIVERED->value) {
            return $this->updated_at ? $this->updated_at : null;
        }

        return null;
    }

    public function getShipmentApprovedCostAttribute(): mixed
    {
        if (! $this->shipment_cost) {
            return null;
        }

        if ($this->shipment_credentials_type === 'user') {
            return 100;
        }

        return $this->shipment_cost;

    }

    public function getBaseShipmentApprovedCostAttribute(): mixed
    {
        if (! $this->base_shipment_cost) {
            return null;
        }

        if ($this->shipment_credentials_type === 'user') {
            return 100;
        }

        return $this->base_shipment_cost;

    }

    /**
     * @return HasMany<OrderHistory,$this>
     */
    public function histories(): HasMany
    {
        return $this->hasMany(OrderHistory::class);
    }

    public function getOrderTimeAttribute(): ?string
    {
        return $this->date ? Carbon::parse($this->date)->format('H:i') : null;
    }

    public function setWarehouse(int $warehouse_id): void
    {
        $warehouse = Warehouse::findOrFail($warehouse_id);

        $this->shipper_phone = $warehouse->sender_phone;
        $this->shipper_email = $warehouse->sender_email;
        $this->shipper_name = $warehouse->name;
        $this->shipper_city = $warehouse->city->name ?? $warehouse->city->name_ar ?? '';
        $this->shipper_address_line = $warehouse->address;
        $this->shipper_latitude = 0;
        $this->shipper_longitude = 0;
    }

    /**
     * @return HasOne<OrderHistory,$this>
     */
    public function latestOrderHistory()
    {
        return $this->hasOne(OrderHistory::class)->latestOfMany();
    }

    /**
     * @return HasMany<WalletTransaction,$this>
     */
    public function walletTransactions(): HasMany
    {
        return $this->hasMany(WalletTransaction::class);
    }

    public function getUserAttribute(): User
    {
        if ($this->merchant) {
            return $this->merchant->user;
        }

        return $this->warehouse->user;
    }
}
