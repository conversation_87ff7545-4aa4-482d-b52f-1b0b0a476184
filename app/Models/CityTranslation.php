<?php

namespace App\Models;

use Database\Factories\CityTranslationFactory;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class CityTranslation extends Model
{
    /** @use HasFactory<CityTranslationFactory> */
    use HasFactory;

    protected $fillable = [
        'city_id',
        'source',
        'value',
    ];

    /**
     * @return BelongsTo<City, $this>
     */
    public function city(): BelongsTo
    {
        return $this->belongsTo(City::class);
    }
}
