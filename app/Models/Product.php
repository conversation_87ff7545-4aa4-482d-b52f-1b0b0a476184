<?php

namespace App\Models;

use App\Models\Scopes\ProductScope;
use Database\Factories\ProductFactory;
use Illuminate\Database\Eloquent\Attributes\ScopedBy;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;

#[ScopedBy([ProductScope::class])]
class Product extends Model
{
    /** @use HasFactory<ProductFactory> */
    use HasFactory;

    protected $fillable = [
        'name',
        'sku',
        'price',
        'tax_amount',
        'currency_id',
        'tax_currency_id',
        'barcode',
        'category',
        'description',
        'cubic_meter',
        'length',
        'weight',
        'weight_unit',
        'width',
        'height',
        'total_weight',
        'image',
        'merchant_id',
        'external_id',
    ];

    /**
     * @return BelongsTo<Currency, $this>
     */
    public function currency(): BelongsTo
    {
        return $this->belongsTo(Currency::class, 'currency_id');
    }

    /**
     * @return BelongsTo<Currency, $this>
     */
    public function taxCurrency(): BelongsTo
    {
        return $this->belongsTo(Currency::class, 'tax_currency_id');
    }

    /**
     * @return BelongsToMany<Order, $this>
     */
    public function order(): BelongsToMany
    {
        return $this->belongsToMany(Order::class, 'order_product', 'product_id', 'order_id')->withTimestamps();
    }

    /**
     * @return HasMany<OtherBarcode, $this>
     */
    public function otherBarcode(): HasMany
    {
        return $this->hasMany(OtherBarcode::class);
    }

    /**
     * @return BelongsTo<Merchant, $this>
     */
    public function merchant(): BelongsTo
    {
        return $this->belongsTo(Merchant::class);
    }

    /**
     * @return BelongsToMany<Order, $this>
     */
    public function orders(): BelongsToMany
    {
        return $this->belongsToMany(Order::class, 'order_product')
            ->withPivot('quantity', 'price')
            ->withTimestamps();
    }

    /**
     * @return Attribute<int, never>
     */
    protected function price(): Attribute
    {
        return Attribute::make(
            get: fn (int $value) => $value / 100,
        );
    }
}
