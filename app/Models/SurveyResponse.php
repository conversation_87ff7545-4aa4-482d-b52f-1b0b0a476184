<?php

namespace App\Models;

use Database\Factories\SurveyResponseFactory;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class SurveyResponse extends Model
{
    /** @use HasFactory<SurveyResponseFactory> */
    use HasFactory;

    protected $fillable = [
        'user_id',
        'has_ecommerce_store',
        'business_industry',
        'monthly_orders',
        'submitted_at',
    ];

    public $timestamps = false;

    /**
     * @return BelongsTo<User, $this>
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function getUserFullNameAttribute(): ?string
    {
        return $this->user ? $this->user->first_name.' '.$this->user->last_name : null;
    }
}
