<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\App;

class SalesChannel extends Model
{
    protected $fillable = [
        'name',
        'name_en',
        'identifier',
        'description',
        'logo',
        'url',
        'action_btn_label',
        'action_btn_label_en',
        'active',
    ];

    protected $casts = [
        'name' => 'array',
        'action_btn_label' => 'array',
        'description' => 'array',
    ];

    public function getNameAttribute(string $value): string
    {
        $currentLang = App::getLocale();
        $nameArray = json_decode($value, true);

        return $nameArray[$currentLang] ?? $nameArray['en'] ?? '';
    }

    public function getActionBtnLabelAttribute(string $value): string
    {
        $currentLang = App::getLocale();
        $nameArray = json_decode($value, true);

        return $nameArray[$currentLang] ?? $nameArray['en'] ?? '';
    }

    public function getDescriptionAttribute(string $value): string
    {
        $currentLang = App::getLocale();
        $nameArray = json_decode($value, true);

        return $nameArray[$currentLang] ?? $nameArray['en'] ?? '';
    }
}
