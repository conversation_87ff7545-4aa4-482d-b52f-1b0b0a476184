<?php

namespace App\Models;

use Database\Factories\UserShipmentCourierServiceFactory;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class UserShipmentCourierService extends Model
{
    /** @use HasFactory<UserShipmentCourierServiceFactory> */
    use HasFactory;

    protected $fillable = [
        'shipment_courier_service_id',
        'user_id',
        'username',
        'password',
        'account_number',
        'account_entity',
        'account_pin',
    ];

    /**
     * @return BelongsTo<ShipmentCourierService, $this>
     */
    public function service(): BelongsTo
    {
        return $this->belongsTo(ShipmentCourierService::class, 'shipment_courier_service_id');
    }

    /**
     * @return BelongsTo<User, $this>
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }
}
