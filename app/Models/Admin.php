<?php

namespace App\Models;

use App\Models\Scopes\AdminScope;
use Illuminate\Database\Eloquent\Attributes\ScopedBy;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Spatie\Permission\Traits\HasRoles;

#[ScopedBy([AdminScope::class])]
class Admin extends Model
{
    use HasRoles;

    protected $table = 'users';

    /**
     * @return BelongsToMany<User, $this>
     */
    public function merchantUsers(): BelongsToMany
    {
        return $this->belongsToMany(User::class, 'admin_clients', 'admin_id', 'user_id')
            ->where('role', 'merchant');
    }

    /**
     * @return BelongsToMany<\Spatie\Permission\Models\Role, $this>
     */
    public function roles(): BelongsToMany
    {
        return $this->belongsToMany(\Spatie\Permission\Models\Role::class, 'model_has_roles', 'model_id', 'role_id')
            ->where('model_type', User::class);
    }
}
