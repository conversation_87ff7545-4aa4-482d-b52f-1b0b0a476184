<?php

namespace App\Services;

use App\Dto\PricingConfigDto;
use App\Dto\ShipmentRequestDto;
use App\Dto\ShippingRateDto;
use App\Enums\CostCodTypeEnum;
use App\Enums\CourierIdentifierEnum;
use App\Interfaces\ShipmentCourierInterface;
use App\Models\Order;
use Illuminate\Http\Client\ConnectionException;
use Illuminate\Http\Client\RequestException;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Storage;

class BarqFleetService implements ShipmentCourierInterface
{
    // TODO: SAVE TRACKING ID IN DATABASE
    const string SHIPMENT_TRACKING_URL = 'https://tracking.barqapp.com/?trackingId=';

    protected string $baseUrl;

    protected string $token;

    protected string $userName;

    protected string $password;

    /**
     * @var string[]
     */
    protected array $config = [];

    public function __construct()
    {
        $this->baseUrl = config('services.barqfleet.base_url');
        $this->token = '';
    }

    /**
     * @param  string[]  $config
     */
    public function setConfig(array $config): void
    {
        $this->userName = $config['username'];
        $this->password = $config['password'];
    }

    /**
     * @return string[]
     */
    private function headers(): array
    {
        return [
            'Authorization' => "{$this->token}",
            'Content-Type' => 'application/json',
        ];
    }

    /**
     * @throws RequestException
     */
    public function login(): void
    {
        $response = Http::post("{$this->baseUrl}/api/v1/merchants/login", [
            'email' => $this->userName,
            'password' => $this->password,
        ]);

        if ($response->successful()) {
            $this->token = $response->json('token');

            return;
        }

        $response->throw();
    }

    /**
     * @return string[]
     */
    public function calculateDistance(float $lat1, float $lng1, float $lat2, float $lng2): array
    {
        return Http::get("{$this->baseUrl}/api/v1/merchants/distance", [
            'lat1' => $lat1,
            'lng1' => $lng1,
            'lat2' => $lat2,
            'lng2' => $lng2,
        ])->json();
    }

    /**
     * @throws RequestException
     * @throws \Illuminate\Http\Client\ConnectionException
     */
    public function createShipment(Order $order): ShipmentRequestDto
    {

        // TODO: Check if hub exist or not , if not exist create it and assign it in the request
        if (! $this->token) {
            $this->login();
        }
        $products = $order->items;
        $barqProducts = [];
        foreach ($products as $product) {
            $barqProducts[] = [
                'sku' => $product->sku,
                'serial_no' => $product->sku,
                'name' => $product->name,
                'color' => '',
                'brand' => '',
                'price' => $product->price / 100,
                'weight_kg' => $product->weight,
                'qty' => $product->quantity,
            ];
        }
        $jayParsedAry = [
            'payment_type' => $order->isCod() ? 'cash_on_delivery' : 'credit_card',
            'shipment_type' => 0,
            'hub_id' => $this->getOrCreateHubId($order),
            'hub_code' => $order->shipper_name,
            'merchant_order_id' => $order->order_number,
            'invoice_total' => $order->isCod() ? $order['order_grand_total'] : 0,
            'customer_details' => [
                'first_name' => $order['receiver_first_name'],
                'last_name' => $order['receiver_last_name'],
                'country' => $order->receiver_country,
                'city' => $order->getReceiverCityWithFallback(CourierIdentifierEnum::ARAMEX->value),
                'mobile' => $order['receiver_phone'],
                'address' => $order->receiver_address_line,
            ],
            'products' => $barqProducts,
            'destination' => [
                'latitude' => $order->receiver_latitude,
                'longitude' => $order->receiver_longitude,
            ],
        ];

        $url = "{$this->baseUrl}/api/v1/merchants/orders";
        $headers = $this->headers();

        return ShipmentRequestDto::create($url, $jayParsedAry, $headers);
    }

    /**
     * @return array|string[]
     *
     * @throws RequestException
     * @throws \Illuminate\Http\Client\ConnectionException
     */
    public function createReverseShipment(Order $order): array
    {
        // TODO: Check if hub exist or not , if not exist create it and assign it in the request
        if (! $this->token) {
            $this->login();
        }
        $products = $order->items;
        $barqProducts = [];
        foreach ($products as $product) {
            $barqProducts[] = [
                'sku' => $product->sku,
                'serial_no' => $product->sku,
                'name' => $product->name,
                'color' => '',
                'brand' => '',
                'price' => $product->price / 100,
                'weight_kg' => $product->weight,
                'qty' => $product->quantity,
            ];
        }
        $jayParsedAry = [
            'payment_type' => $order->isCod() ? 'cash_on_delivery' : 'credit_card',
            'shipment_type' => 0,
            'hub_id' => $this->getOrCreateHubId($order),
            'hub_code' => $order->shipper_name,
            'merchant_order_id' => $order->order_number.'-R',
            'invoice_total' => $order->isCod() ? $order['order_grand_total'] : 0,
            'customer_details' => [
                'first_name' => $order['receiver_first_name'],
                'last_name' => $order['receiver_last_name'],
                'country' => $order->receiver_country,
                'city' => $order->receiver_city,
                'mobile' => $order['receiver_phone'],
                'address' => $order->receiver_address_line,
            ],
            'products' => $barqProducts,
            'destination' => [
                'latitude' => $order->receiver_latitude,
                'longitude' => $order->receiver_longitude,
            ],
        ];

        return Http::withHeaders($this->headers())
            ->post("{$this->baseUrl}/api/v1/merchants/orders", $jayParsedAry)
            ->json();
    }

    /**
     * @return string[]
     *
     * @throws \Illuminate\Http\Client\ConnectionException
     */
    public function listOrders(): array
    {
        return Http::withHeaders($this->headers())
            ->get("{$this->baseUrl}/api/v1/merchants/orders")
            ->json();
    }

    /**
     * Calculate the shipping cost for the given order.
     *
     * @return int Calculated shipping cost
     */
    public function getRates(ShippingRateDto $shippingRateDto): int
    {
        //        if ($order->isInternational()) {
        //            return $this->getInternationalRates($order);
        //        }

        // Get pricing configuration from factory
        $factory = app(ShippingServiceFactory::class);
        $pricingConfig = $factory->getPricingConfig(
            CourierIdentifierEnum::BARQ->value,
            $shippingRateDto
        );

        return $this->calculateRateFromConfig($pricingConfig, $shippingRateDto);
    }

    /**
     * Calculate shipping cost from configuration data for rates
     */
    private function calculateRateFromConfig(PricingConfigDto $pricingConfig, ShippingRateDto $shippingRateDto): int
    {
        // Calculate total weight from order boxes
        $totalWeight = ($shippingRateDto->calculateWeightFromBoxes($pricingConfig->volumetricDivisor) * 100);

        $basePrice = $pricingConfig->basePrice;
        $additionalWeightCost = 0;
        $distanceCost = 0;

        // Calculate additional weight cost
        if ($totalWeight > $pricingConfig->extraWeightFrom) {
            $additionalWeight = $totalWeight - $pricingConfig->extraWeightFrom;
            $additionalWeightCost = ($additionalWeight / 100) * $pricingConfig->additionalWeightCost;
        }

        // Add cash on delivery cost if applicable
        if ($shippingRateDto->isCod && ! $shippingRateDto->ignoreCod) {
            $basePrice = $basePrice + $pricingConfig->cashOnDeliveryCost;
        }

        // Calculate distance cost if applicable
        if ($pricingConfig->distanceCost > 0) {
            // TODO: Implement distance calculation logic
            // $distanceCost = $this->calculateDistanceCost($shippingRateDto, $pricingConfig->distanceCost);
        }

        // Calculate the total cost
        return $basePrice + $additionalWeightCost + $distanceCost;
    }

    //    public function refreshToken(): string
    //    {
    //        // TODO: Implement refreshToken() method.
    //    }

    /**
     * @return string[]
     *
     * @throws \Exception
     */
    public function trackShipments(array $trackingNumbers): array
    {
        return Http::withHeaders($this->headers())
            ->get("{$this->baseUrl}/api/v1/merchants/orders")
            ->json();
    }

    /**
     * @return array<mixed>
     *
     * @throws ConnectionException
     * @throws RequestException
     */
    public function trackShipment(string $trackingNumber): array
    {
        if (! $this->token) {
            $this->login();
        }

        return Http::withHeaders($this->headers())
            ->get("{$this->baseUrl}/api/v1/merchants/orders/{$trackingNumber}")
            ->json();
    }

    public function printLabel(Order $order): ShipmentRequestDto
    {
        if (! $this->token) {
            $this->login();
        }

        $url = "{$this->baseUrl}/api/v1/merchants/orders/airwaybill/$order->shipment_reference";
        $headers = $this->headers();

        return ShipmentRequestDto::create($url, [], $headers, 'GET');
    }

    public function extractPrintLabelUrl($response, ?Order $order = null): string
    {
        // Handle HTTP response object with binary PDF data
        if ($response instanceof \Illuminate\Http\Client\Response && $response->ok()) {
            Storage::disk('public')->put("airwaybill_$order->shipment_reference.pdf", $response->body());

            return Storage::disk('public')->url("airwaybill_$order->shipment_reference.pdf");
        }

        $jsonMessage = json_encode([
            'error' => 'Label not found',
            'order_id' => $order ? $order->id : 'unknown',
            'shipment_reference' => $order ? $order->shipment_reference : 'unknown',
            'response_type' => gettype($response),
        ], JSON_UNESCAPED_UNICODE);
        if ($jsonMessage === false) {
            throw new \Exception('JSON encode failed: '.json_last_error_msg());
        }
        throw new \Exception($jsonMessage);
    }

    private function getOrCreateHubId(Order $order): string
    {
        $response = Http::withHeaders($this->headers())->put("{$this->baseUrl}/api/v1/merchants/hubs/update_hub", [
            'code' => $order->shipper_name,
            'city_id' => $this->getCityId($order),
            'manager' => $order->shipper_name,
            'mobile' => $order->shipper_phone,
            'phone' => $order->shipper_phone,
            'latitude' => $order->shipper_latitude,
            'longitude' => $order->shipper_longitude,
            'is_active' => true,
            'opening_time' => '10:00:00',
            'closing_time' => '23:59:00',
            'start_day' => 0,
            'end_day' => 4,
        ]);

        return $response->json()['id'];

    }

    private function getCityId(Order $order): int
    {
        if ($order->shipper_city === 'Jeddah') {
            return 6;
        }
        if ($order->shipper_city === 'Riyadh') {
            return 1;
        }
        $jsonMessage = json_encode([
            'error' => 'City not found for Barq',
            'shipper_city' => $order->shipper_city,
            'order_id' => $order->id,
        ], JSON_UNESCAPED_UNICODE);
        if ($jsonMessage === false) {
            throw new \Exception('JSON encode failed: '.json_last_error_msg());
        }
        throw new \Exception($jsonMessage);
    }

    public function cancelShipment(string $orderNumber): array
    {
        // TODO: Implement cancelShipment() method.
        $jsonMessage = json_encode([
            'error' => 'Cancel shipment not implemented',
            'order_number' => $orderNumber,
        ], JSON_UNESCAPED_UNICODE);
        if ($jsonMessage === false) {
            throw new \Exception('JSON encode failed: '.json_last_error_msg());
        }
        throw new \Exception($jsonMessage);
    }

    /**
     * Calculate the shipping cost for the given order.
     *
     * @return int Calculated shipping cost
     */
    public function getCosts(ShippingRateDto $shippingRateDto): int
    {
        if ($shippingRateDto->isInternational) {
            // TODO: FINISH THIS LATER
            return 999999999999;
        }

        // Get pricing configuration from factory
        $factory = app(ShippingServiceFactory::class);
        $pricingConfig = $factory->getCostPricingConfig(
            CourierIdentifierEnum::BARQ->value,
            $shippingRateDto
        );

        return $this->calculateCostFromConfig($pricingConfig, $shippingRateDto);
    }

    /**
     * Calculate shipping cost from configuration data for costs
     */
    private function calculateCostFromConfig(PricingConfigDto $pricingConfig, ShippingRateDto $shippingRateDto): int
    {
        // Calculate total weight from order boxes
        $totalWeight = ($shippingRateDto->calculateWeightFromBoxes($pricingConfig->volumetricDivisor) * 100);

        $basePrice = $pricingConfig->basePrice;
        $additionalWeightCost = 0;

        // Calculate additional weight cost
        if ($totalWeight > $pricingConfig->extraWeightFrom) {
            $additionalWeight = $totalWeight - $pricingConfig->extraWeightFrom;
            $additionalWeightCost = ($additionalWeight / 100) * $pricingConfig->additionalWeightCost;
        }

        // Add cash on delivery cost if applicable (VALUE type)
        if ($shippingRateDto->isCod && ! $shippingRateDto->ignoreCod && $pricingConfig->codType === CostCodTypeEnum::VALUE->value) {
            $basePrice = $basePrice + $pricingConfig->cashOnDeliveryCost;
        }

        // Add cash on delivery cost if applicable (PERCENTAGE type)
        if ($shippingRateDto->isCod && ! $shippingRateDto->ignoreCod && $pricingConfig->codType === CostCodTypeEnum::PERCENTAGE->value) {
            $codValue = (int) (($basePrice * $pricingConfig->cashOnDeliveryCost) / 100);
            $basePrice = $basePrice + $codValue;
        }

        // Add additional weight cost to base price
        $basePrice = $basePrice + $additionalWeightCost;

        // Calculate fuel cost
        $fuelCost = ($basePrice * $pricingConfig->fuel) / 100;

        // Calculate the total cost
        return $basePrice + $fuelCost;
    }
}
