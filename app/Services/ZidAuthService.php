<?php

namespace App\Services;

use App\Dto\ZidOrderDto;
use App\Enums\OrderStatusEnum;
use App\Enums\SalesChannelEnum;
use App\Interfaces\EcommerceInterface;
use App\Models\City;
use App\Models\Country;
use App\Models\Merchant;
use App\Models\Order;
use App\Models\OrderItem;
use App\Models\Warehouse;
use Filament\Notifications\Notification;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use League\OAuth2\Client\Provider\Exception\IdentityProviderException;
use Salla\OAuth2\Client\Provider\Salla;

/**
 * @mixin Salla
 */
class ZidAuthService implements EcommerceInterface
{
    public Merchant $merchant;

    /**
     * Get the token from the user model.
     *
     *
     * @return $this
     */
    public function forUser(Merchant $merchant)
    {
        $this->merchant = $merchant;

        return $this;
    }

    public function testConnection(): void
    {
        // TODO: Implement testConnection() method.
    }

    public function createWebhook(Merchant $merchant): void
    {
        /** @var string $url */
        $url = 'https://api.zid.sa/v1/managers/webhooks';
        $jayParsedAry = json_encode(
            [
                'event' => 'product.create',
                'target_url' => config('app.url').'/api/webhook/'.SalesChannelEnum::ZID->value,
                'original_id' => 1,
            ]
        );
        if ($jayParsedAry !== false) {
            Http::withHeaders([
                'Authorization' => 'Bearer '.$merchant->authorization,
                'X-Manager-Token' => $merchant->access_token,
            ])->withBody($jayParsedAry)->post($url);
        }
        $jayParsedAry = json_encode(
            [
                'event' => 'product.update',
                'target_url' => config('app.url').'/api/webhook/'.SalesChannelEnum::ZID->value,
                'original_id' => 1,
            ]
        );
        if ($jayParsedAry !== false) {
            Http::withHeaders([
                'Authorization' => 'Bearer '.$merchant->authorization,
                'X-Manager-Token' => $merchant->access_token,
            ])->withBody($jayParsedAry)->post($url);
        }
        $jayParsedAry = json_encode(
            [
                'event' => 'order.create',
                'target_url' => config('app.url').'/api/webhook/'.SalesChannelEnum::ZID->value,
                'original_id' => 1,
            ]
        );
        if ($jayParsedAry !== false) {
            Http::withHeaders([
                'Authorization' => 'Bearer '.$merchant->authorization,
                'X-Manager-Token' => $merchant->access_token,
            ])->withBody($jayParsedAry)->post($url);
        }
        $jayParsedAry = json_encode(
            [
                'event' => 'order.status.update',
                'target_url' => config('app.url').'/api/webhook/'.SalesChannelEnum::ZID->value,
                'original_id' => 1,
            ]
        );
        if ($jayParsedAry !== false) {
            Http::withHeaders([
                'Authorization' => 'Bearer '.$merchant->authorization,
                'X-Manager-Token' => $merchant->access_token,
            ])->withBody($jayParsedAry)->post($url);
        }
        $jayParsedAry = json_encode(
            [
                'event' => 'order.payment_status.update',
                'target_url' => config('app.url').'/api/webhook/'.SalesChannelEnum::ZID->value,
                'original_id' => 1,
            ]
        );
        if ($jayParsedAry !== false) {
            Http::withHeaders([
                'Authorization' => 'Bearer '.$merchant->authorization,
                'X-Manager-Token' => $merchant->access_token,
            ])->withBody($jayParsedAry)->post($url);
        }
    }

    /**
     * @return array<string,string>
     *
     * @throws \Illuminate\Http\Client\ConnectionException
     */
    public function getStore(Merchant $merchant): array
    {
        /** @var string $url */
        $url = 'https://api.zid.sa/v1/managers/account/profile';

        // dd($merchant->authorization,  $merchant->access_token);
        return Http::withHeaders([
            'Authorization' => 'Bearer '.$merchant->authorization,
            'X-Manager-Token' => $merchant->access_token,
        ])->get($url)->json('user')['store'];
        // dd($response);

    }

    /**
     * @return array<string, mixed>
     */
    public function getStoreWithAccessToken(string $authorization, string $access_token): array
    {
        /** @var string $url */
        $url = 'https://api.zid.sa/v1/managers/account/profile';

        // dd($merchant->authorization,  $merchant->access_token);
        return Http::withHeaders([
            'Authorization' => 'Bearer '.$authorization,
            'X-Manager-Token' => $access_token,
        ])->get($url)->json('user')['store'];
    }

    /**
     * @return array<mixed>
     *
     * @throws IdentityProviderException
     */
    public function getOrderByOrderNumber(string|int $orderNumber): array
    {

        $response = Http::withHeaders([
            'Authorization' => 'Bearer '.$this->merchant->authorization,
            'X-Manager-Token' => $this->merchant->access_token,
            'Content-Type' => 'application/json',
        ])
            ->get('https://api.zid.sa/v1/managers/store/orders/'.$orderNumber.'/view');

        if (! $response->successful()) {
            $errorMessage = $response->json('errors') ?? 'Unknown error while fetching order from Zid API';
            $jsonMessage = json_encode([
                'error' => 'Zid API error',
                'api_error' => $errorMessage,
                'order_number' => $orderNumber,
                'status_code' => $response->status(),
            ], JSON_UNESCAPED_UNICODE);
            if ($jsonMessage === false) {
                throw new \Exception('JSON encode failed: '.json_last_error_msg());
            }
            throw new \Exception($jsonMessage);
        }

        $order = $response->json('order');
        if (empty($order)) {
            $jsonMessage = json_encode([
                'error' => 'No orders found in zid for order number',
                'order_number' => $orderNumber,
                'merchant_id' => $this->merchant->merchant_id,
            ], JSON_UNESCAPED_UNICODE);
            if ($jsonMessage === false) {
                throw new \Exception('JSON encode failed: '.json_last_error_msg());
            }
            throw new \Exception($jsonMessage);
        }

        return $order;
    }

    public function handleZidOrderCreated(ZidOrderDto $orderDto): void
    {
        $alreadyExist = Order::withoutGlobalScopes()->where('order_number', $orderDto->orderNumber)->exists();
        if ($alreadyExist) {
            $jsonMessage = json_encode([
                'error' => 'Order from Zid already exists',
                'order_number' => $orderDto->orderNumber,
                'merchant_id' => $orderDto->merchantId,
            ], JSON_UNESCAPED_UNICODE);

            Notification::make()
                ->title('order exist')
                ->body("order '".$orderDto->orderNumber."' already exist")
                ->warning()
                ->send();

            if ($jsonMessage === false) {
                throw new \Exception('JSON encode failed: '.json_last_error_msg());
            }
            throw new \Exception($jsonMessage);
        }
        $warehouse = $this->getWarehouse(Merchant::withoutGlobalScopes()->findOrFail($orderDto->merchantId), $orderDto);
        try {
            /** @var Order $createdOrder */
            $createdOrder = Order::create([
                'external_id' => $orderDto->externalId,
                'order_number' => $orderDto->externalId,
                'status' => $orderDto->status,
                'date' => $orderDto->date,
                'order_grand_total' => $orderDto->orderGrandTotal,
                'description' => $orderDto->description,
                'payment_method' => $orderDto->paymentMethod,
                'receiver_first_name' => $orderDto->receiverFirstName,
                'receiver_last_name' => $orderDto->receiverLastName,
                'receiver_phone' => phoneCorrector(receivedPhone: $orderDto->receiverPhone, correctWithCountry: Country::findByString(name: $orderDto->receiverCountry)),
                'receiver_email' => $orderDto->receiverEmail,
                'receiver_country_id' => Country::findByString(name: $orderDto->receiverCountry),
                'receiver_country' => $orderDto->receiverCountry,
                'receiver_address_line' => $orderDto->receiverAddressLine,
                'receiver_city_id' => City::findByString($orderDto->receiverCity),
                'receiver_city' => $orderDto->receiverCity,
                'receiver_street_name' => $orderDto->receiverStreetName,
                'receiver_postal_code' => $orderDto->receiverPostalCode,
                'receiver_latitude' => $orderDto->receiverLatitude,
                'receiver_longitude' => $orderDto->receiverLongitude,
                'warehouse_id' => $orderDto->warehouseId,
                'merchant_id' => $orderDto->merchantId,
                'webhook_id' => $orderDto->webhookId,
                'shipment_total_weight' => $orderDto->shipmentTotalWeight,
                'tax' => $orderDto->tax,
                'source' => SalesChannelEnum::ZID->value,
                ...$warehouse,
            ]);
            $createdOrder->logOrderCreation();
            foreach ($orderDto->items as $itemDto) {
                OrderItem::create([
                    'order_id' => $createdOrder->id,
                    'name' => $itemDto->name,
                    'sku' => $itemDto->sku,
                    'quantity' => $itemDto->quantity,
                    'weight' => $itemDto->weight,
                    'weight_unit' => 'g',
                    'price' => $itemDto->price,
                    'total_price' => $itemDto->totalPrice,
                    'tax' => $itemDto->tax,
                ]);
            }
            $boxes = [];

            foreach ($orderDto->items as $item) {
                for ($i = 0; $i < $item->quantity; $i++) {
                    $boxes[] = [
                        'weight' => $item->weight ?? 0,
                        'width' => 10,
                        'length' => 10,
                        'height' => 10,
                        //                        'content_total_amount' => $item->price ? $item->price / 100 : null,
                        //                        'description' => $item->name,
                    ];
                }
            }
            if (empty($boxes)) {
                // fallback: at least 1 box
                $boxes[] = [
                    'weight' => 0,
                    'width' => 10,
                    'length' => 10,
                    'height' => 10,
                    //                    'content_total_amount' => null,
                    //                    'description' => null,
                ];
            }
            $createdOrder->boxes()->createMany($boxes);

        } catch (\Exception $e) {
            // Log errors for debugging
            logger('Error creating order', [
                'error' => $e->getMessage(),
                'payload' => $orderDto,
            ]);
            throw $e;
        }
    }

    /**
     * @return array<string, int|string|null>
     */
    private function getWarehouse(Merchant $merchant, ZidOrderDto $zidOrderDto): array
    {
        if ($merchant->warehouse_id) {
            $warehouse = Warehouse::withoutGlobalScopes()->findOrFail($merchant->warehouse_id);

            return [
                'shipper_phone' => $warehouse->sender_phone,
                'shipper_email' => $warehouse->sender_email,
                'shipper_name' => $warehouse->name,
                'shipper_city' => $warehouse->city?->name,
                'shipper_address_line' => $warehouse->address,
                'shipper_latitude' => 0,
                'shipper_longitude' => 0,
            ];
        }

        return [
            'shipper_phone' => $zidOrderDto->shipperPhone,
            'shipper_name' => $zidOrderDto->shipperName,
            'shipper_city' => $zidOrderDto->shipperCity,
            'shipper_address_line' => $zidOrderDto->shipperAddressLine,
            'shipper_latitude' => $zidOrderDto->shipperLatitude,
            'shipper_longitude' => $zidOrderDto->shipperLongitude,
        ];
    }

    public function changeStatus(Order $order): void
    {
        try {
            $orderId = $order->external_id;
            $status = $this->getZidStatus($order);
            $response = Http::withHeaders([
                'Authorization' => 'Bearer '.$this->merchant->authorization,
                'X-Manager-Token' => $this->merchant->access_token,
            ])
                ->post('https://api.zid.sa/v1/managers/store/orders/'.$orderId.'/change-order-status', [
                    'order_status' => $status,
                ]);
        } catch (\Exception $exception) {
            Log::info('zid update order status issue');
            Log::info($exception->getMessage());
            Notification::make()
                ->title('zid update order status issue')
                ->danger()
                ->send();
        }
    }

    public function sendLabel(Order $order, string $url): void
    {
        try {
            $orderId = $order->external_id;
            $trackingNumber = $order->shipment_reference;
            $trackingUrl = $order->shipment_tracking_link;
            $status = $this->getZidStatus($order);
            $response = Http::withHeaders([
                'Authorization' => 'Bearer '.$this->merchant->authorization,
                'X-Manager-Token' => $this->merchant->access_token,
            ])
                ->post('https://api.zid.sa/v1/managers/store/orders/'.$orderId.'/change-order-status', [
                    'order_status' => $status,
                    'waybill_url' => $url,
                    'tracking_number' => $trackingNumber,
                    'tracking_url' => $trackingUrl,
                ]);
            Log::info('Zid changeStatus : '.$response);
        } catch (\Exception $exception) {
            Log::info('zid update order status issue');
            Log::info($exception->getMessage());
            Notification::make()
                ->title('zid update order status issue')
                ->danger()
                ->send();
        }
    }

    private function getZidStatus(Order|ZidOrderDto $order): string
    {
        return match ($order->status) {
            OrderStatusEnum::PENDING->value => 'indelivery',
            OrderStatusEnum::AWAITING_PICKUP->value => 'indelivery',
            OrderStatusEnum::CURRENTLY_SHIPPING->value => 'indelivery',
            OrderStatusEnum::DELIVERED->value => 'delivered',
            OrderStatusEnum::SHIPMENT_ON_HOLD->value => 'voided',
            OrderStatusEnum::CANCELED->value => 'cancelled',
            OrderStatusEnum::FAILED->value => 'failed',
            default => 'indelivery',
        };
    }
}
