<?php

namespace App\Services;

use App\Enums\OrderStatusEnum;
use App\Enums\SalesChannelEnum;

class OrderStatusMapper
{
    /**
     * @var array<string, array<string, OrderStatusEnum>>
     */
    protected static array $statusMaps = [
        SalesChannelEnum::SALLA->value => [
            'under_review' => OrderStatusEnum::PENDING,
        ],
        SalesChannelEnum::SHOPIFY->value => [
            'payment_pending' => OrderStatusEnum::PENDING,
        ],
        SalesChannelEnum::ZID->value => [
            'pending' => OrderStatusEnum::PENDING,
        ],
    ];

    public static function mapStatus(string $platform, string $status): OrderStatusEnum
    {
        return self::$statusMaps[$platform][$status] ?? OrderStatusEnum::FAILED;
    }
}
