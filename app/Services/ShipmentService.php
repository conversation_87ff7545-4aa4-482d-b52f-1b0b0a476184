<?php

namespace App\Services;

use App\Dto\ShippingRateDto;
use App\Enums\CourierIdentifierEnum;
use App\Enums\OrderStatusEnum;
use App\Enums\PaymentMethodEnum;
use App\Enums\ReturnTypeEnum;
use App\Enums\SalesChannelEnum;
use App\Jobs\SendShipmentWhatsAppJob;
use App\Jobs\UpdateShopifyFulfillmentJob;
use App\Models\Merchant;
use App\Models\Order;
use App\Models\ShipmentCourierService;
use Exception;
use Filament\Notifications\Notification;
use Illuminate\Http\Client\Pool;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use setasign\Fpdi\Fpdi;
use setasign\Fpdi\PdfParser\CrossReference\CrossReferenceException;
use setasign\Fpdi\PdfParser\Filter\FilterException;
use setasign\Fpdi\PdfParser\PdfParserException;
use setasign\Fpdi\PdfParser\Type\PdfTypeException;
use setasign\Fpdi\PdfReader\PdfReaderException;

class ShipmentService
{
    /**
     * @return array{error: false|true, shipment_id: string}
     *
     * @throws Exception
     */
    public function createShipment(Order $order, bool $createdWithGlobalConfig, string $courier = 'aramex'): array
    {
        $factory = app(ShippingServiceFactory::class);
        $service = $factory->create(serviceName: $courier, useGlobalConfig: $createdWithGlobalConfig, order: $order);
        $shipmentCredentialsType = $createdWithGlobalConfig ? 'application' : 'user';

        // Get the courier service for volumetric divisor
        $courierService = ShipmentCourierService::where('identifier', $courier)->firstOrFail();
        $volumetricDivisor = $courierService->getVolumetricDivisor();

        // Get request data from courier service
        $requestDto = $service->createShipment($order);

        // Make HTTP request
        $httpClient = Http::withHeaders($requestDto->headers);

        if ($requestDto->contentType === 'form') {
            $httpResponse = $httpClient->asForm()->{strtolower($requestDto->method)}($requestDto->url, $requestDto->payload);
        } else {
            $httpResponse = $httpClient->{strtolower($requestDto->method)}($requestDto->url, $requestDto->payload);
        }

        $response = $httpResponse->json();
        if ($courier === 'spl') {
            Log::info("Spl Debug Info:\n".
                    // "User Credentials:\n" . print_r($requestDto->userCredentials, true) . "\n" .
                    "Headers:\n".print_r($requestDto->headers, true)."\n".
                    "Payload:\n".print_r($requestDto->payload, true)."\n".
                    "Response:\n".print_r($response, true)
            );
        }
        $hasAramexError = isset($response['HasErrors']) && $response['HasErrors'];
        $hasThabitError = isset($response['status']) && $response['status'] === 'error';
        if ($hasAramexError || $hasThabitError) {
            // DO NOT REMOVE THIS BECAUSE IT PREVENT WALLET DEDUCTION
            // dd($response, $order->order_number);
            //            Log::error($response); Notification::make()
            //                ->title('Failed to create shipment: '.$response['message'])
            //                ->danger()
            //                ->send();
            //
            //            return ['error' => true, 'message' => $response['HasErrors']];
        }

        $shipmentReference = $this->getShipmentReference($courier, $response);
        $shipmentExternalId = $this->getShipmentExternalId($courier, $response);
        $order->update([
            'shipment_reference' => $shipmentReference,
            'shipment_company' => CourierIdentifierEnum::from($courier)->value,
            'shipment_tracking_link' => $service::SHIPMENT_TRACKING_URL.$shipmentReference,
            'shipment_credentials_type' => $shipmentCredentialsType,
            'shipment_credentials' => $factory->resolveConfig(serviceName: $courier, useGlobalConfig: $createdWithGlobalConfig, order: $order),
            'shipment_cost' => $service->getRates(ShippingRateDto::fromOrder(order: $order)),
            'shipment_cost_without_cod' => $service->getRates(ShippingRateDto::fromOrder(order: $order, ignoreCod: true)),
            'shipment_external_id' => $shipmentExternalId,
            'base_shipment_cost' => $service->getCosts(ShippingRateDto::fromOrder(order: $order)),
            'base_shipment_cod_cost' => 0,
            'volumetric_divisor' => $courierService->getVolumetricDivisor(),
        ]);

        // Update status using centralized method
        $order->updateStatus(OrderStatusEnum::AWAITING_PICKUP->value);

        // Handle Shopify fulfillment via queue
        if ($order->source === SalesChannelEnum::SHOPIFY->value && $order->external_id !== null) {
            UpdateShopifyFulfillmentJob::dispatch(
                orderId: $order->id,
                trackingNumber: $shipmentReference,
                trackingCompany: $courier,
                trackingUrl: $order->shipment_tracking_link
            );

            Log::info('Shopify fulfillment job dispatched', [
                'order_id' => $order->id,
                'order_number' => $order->order_number,
                'tracking_number' => $shipmentReference,
                'tracking_company' => $courier,
            ]);
        }

        // Dispatch WhatsApp message job
        SendShipmentWhatsAppJob::dispatch($order);

        Notification::make()
            ->title(__('translation.shipment_created_successfully').$shipmentReference)
            ->success()
            ->send();

        return ['error' => false, 'shipment_id' => $shipmentReference];
    }

    /**
     * @return array{error: false, shipment_id: string}
     *
     * @throws Exception
     */
    public function createReverseShipment(Order $order, bool $createdWithGlobalConfig, string $courier): array
    {
        $factory = app(ShippingServiceFactory::class);
        $service = $factory->create(serviceName: $courier, useGlobalConfig: $createdWithGlobalConfig, order: $order);
        $shipmentCredentialsType = $createdWithGlobalConfig ? 'application' : 'user';

        // Get the courier service for volumetric divisor
        $courierService = ShipmentCourierService::where('identifier', $courier)->firstOrFail();
        $volumetricDivisor = $courierService->getVolumetricDivisor();
        $response = $service->createReverseShipment($order);
        $hasAramexError = isset($response['HasErrors']) && $response['HasErrors'];
        $hasThabitError = isset($response['status']) && $response['status'] === 'error';
        if ($hasAramexError || $hasThabitError) {
            // DO NOT REMOVE THIS BECAUSE IT PREVENT WALLET DEDUCTION
            dd($response, $order);
            //            Log::error($response);
            //            Notification::make()
            //                ->title('Failed to create shipment: '.$response['message'])
            //                ->danger()
            //                ->send();
            //
            //            return ['error' => true, 'message' => $response['HasErrors']];
        }
        $shipmentReference = $this->getShipmentReference($courier, $response);
        $shipmentExternalId = $this->getShipmentExternalId($courier, $response);
        $order->update([
            'shipment_reference' => $shipmentReference,
            'shipment_company' => CourierIdentifierEnum::from($courier)->value,
            'shipment_label_url' => null,
            'shipment_tracking_link' => $service::SHIPMENT_TRACKING_URL.$this->getShipmentReference($courier, $response),
            'shipment_credentials_type' => $shipmentCredentialsType,
            'shipment_cost' => $order->shipment_cost ?? $service->getRates(ShippingRateDto::fromOrder($order)),
            'shipment_credentials' => $factory->resolveConfig(serviceName: $courier, useGlobalConfig: $createdWithGlobalConfig, order: $order),
            'shipment_external_id' => $shipmentExternalId,
            'payment_method' => PaymentMethodEnum::PAID->value,
            'return_type' => ReturnTypeEnum::REVERSE_SHIPMENT->value,
            'base_shipment_cost' => 1600,
            'base_shipment_cod_cost' => 0,
        ]);

        // Update status using centralized method
        $order->updateStatus(OrderStatusEnum::RETURNED->value);
        try {
            $merchantId = $order->merchant_id;
            $merchant = Merchant::withoutGlobalScopes()->findorFail($merchantId);
            // Notify the merchant via Salla service
            $sallaAuthProvider = new SallaAuthService;
            $sallaAuthProvider->forUser($merchant);
            $sallaAuthProvider->changeStatus($order);
        } catch (\Exception $exception) {
            Log::info("the merchant $merchantId has not linked his merchant with salla token");
        }

        Notification::make()
            ->title(__('translation.shipment_created_successfully').$shipmentReference)
            ->success()
            ->send();

        return ['error' => false, 'shipment_id' => $shipmentReference];
    }

    /**
     * @param  Collection<int,Order>  $orders
     *
     * @throws \Exception
     */
    public function printLabels(Collection $orders): string
    {
        $factory = app(ShippingServiceFactory::class);
        $pdfUrls = [];
        $requestData = [];
        $orderMap = [];
        $transcorpOrders = [];
        $transcorpLabelUrls = [];
        $total = $orders->count();
        $failedExtractions = 0;
        sleep(3);
        // TIMING: Start
        $startTime = microtime(true);
        Log::info("BULK PRINT LABELS START: Processing {$total} orders");

        // Prepare requests for concurrent processing
        foreach ($orders as $index => $order) {
            $order = Order::withoutGlobalScopes()->findorFail($order->id);

            // Skip if already has label URL
            if ($order->shipment_label_url) {
                $pdfUrls[] = $order->shipment_label_url;

                continue;
            }

            $courierName = $order->shipment_company ?? $order->selected_shipment_company ?? 'aramex';
            $service = $factory->create(serviceName: $courierName, useGlobalConfig: $order->createdWithGlobalConfig(), order: $order);

            // If transcorp or SPL, handle separately (no HTTP requests needed)
            if ($courierName === 'transcorp' || $courierName === 'spl') {
                $transcorpOrders[$index] = ['order' => $order, 'service' => $service];
                $requestDto = $service->printLabel($order);
                $transcorpLabelUrls[$index] = $requestDto->url;

                continue;
            }

            $requestDto = $service->printLabel($order);
            $requestData[$index] = $requestDto;
            $orderMap[$index] = ['order' => $order, 'service' => $service];
        }

        // TIMING: Request preparation done
        $prepTime = microtime(true);
        $existingLabelsCount = count($pdfUrls);
        $newLabelsCount = count($requestData) + count($transcorpOrders);
        Log::info('BULK PRINT LABELS PREP: '.round(($prepTime - $startTime) * 1000, 2).'ms for '.$newLabelsCount.' new requests ('.$existingLabelsCount.' existing labels)');
        // Execute concurrent HTTP requests (for non-transcorp only)
        if (! empty($requestData)) {
            $responses = Http::pool(function (Pool $pool) use ($requestData) {
                foreach ($requestData as $index => $dto) {
                    $request = $pool->as((string) $index)->withHeaders($dto->headers)->timeout(30);

                    if ($dto->contentType === 'form') {
                        $request = $request->asForm();
                    }
                    Log::info('TranscorpSB printLabel response debug', [
                        'method' => strtolower($dto->method),
                        'headers' => $dto->headers,
                        'url' => $dto->url,
                        'payload' => $dto->payload,
                    ]);

                    $request->{strtolower($dto->method)}($dto->url, $dto->payload);
                }
            });

            // TIMING: HTTP requests done
            $httpTime = microtime(true);
            Log::info('BULK PRINT LABELS HTTP: '.round(($httpTime - $prepTime) * 1000, 2).'ms for '.count($requestData).' concurrent requests');

            // Process responses and extract label URLs
            $successfulExtractions = 0;
            $urlUpdates = []; // Collect for bulk update
            foreach ($responses as $index => $response) {
                $orderData = $orderMap[$index];
                $order = $orderData['order'];
                $service = $orderData['service'];
                $courierName = $order->shipment_company ?? $order->selected_shipment_company;
                // Debug logging for TranscorpSB and SPL
                if ($courierName === 'transcorp' || $courierName === 'spl') {
                    // Should not happen, but skip if so
                    continue;
                }

                if ($response->successful()) {
                    try {
                        // Handle different response types with unified method
                        if ($courierName === 'barq') {
                            // Binary PDF response - pass raw response object
                            $printLabelUrl = $service->extractPrintLabelUrl($response, $order);
                        } else {
                            // JSON response with URL - parse and pass response data
                            $responseData = $response->json();
                            $printLabelUrl = $service->extractPrintLabelUrl($responseData, $order);
                        }

                        $pdfUrls[] = $printLabelUrl;
                        $urlUpdates[$order->id] = $printLabelUrl; // Collect for bulk update
                        $successfulExtractions++;
                    } catch (\Exception $e) {
                        $failedExtractions++;
                        $jsonMessage = json_encode([
                            'error' => 'error when printing',
                            'order_id' => $order->id,
                            'order_number' => $order->order_number,
                            'original_error' => $e->getMessage(),
                        ], JSON_UNESCAPED_UNICODE);
                        if ($jsonMessage === false) {
                            throw new \Exception('JSON encode failed: '.json_last_error_msg());
                        }
                        throw new \Exception($jsonMessage);
                    }
                } else {
                    $failedExtractions++;
                    $jsonMessage = json_encode([
                        'error' => 'HTTP response error when printing label',
                        'order_id' => $order->id,
                        'order_number' => $order->order_number,
                        'response_body' => $response->body(),
                        'status_code' => $response->status(),
                    ], JSON_UNESCAPED_UNICODE);
                    if ($jsonMessage === false) {
                        throw new \Exception('JSON encode failed: '.json_last_error_msg());
                    }
                    throw new \Exception($jsonMessage);
                }
            }

            // BULK DATABASE UPDATE - Major optimization!
            foreach ($urlUpdates as $orderId => $url) {
                Order::where('id', $orderId)->update(['shipment_label_url' => $url]);
            }

            // TIMING: Label extraction and database updates done
            $processingTime = microtime(true);
            Log::info('BULK PRINT LABELS PROCESSING: '.round(($processingTime - $httpTime) * 1000, 2).'ms for extracting URLs and updating database ('.$successfulExtractions.' successful, '.$failedExtractions.' failed)');
        }

        // Handle transcorp and SPL orders (no HTTP request, just use the URL)
        if (! empty($transcorpOrders)) {
            $urlUpdates = [];
            foreach ($transcorpOrders as $index => $orderData) {
                $order = $orderData['order'];
                $service = $orderData['service'];
                $labelUrl = $transcorpLabelUrls[$index];
                try {
                    $printLabelUrl = $service->extractPrintLabelUrl($labelUrl, $order);
                    $pdfUrls[] = $printLabelUrl;
                    $urlUpdates[$order->id] = $printLabelUrl;
                } catch (\Exception $e) {
                    $failedExtractions++;
                    $jsonMessage = json_encode([
                        'error' => 'Error when printing transcorp/SPL label',
                        'order_id' => $order->id,
                        'order_number' => $order->order_number,
                        'original_error' => $e->getMessage(),
                    ], JSON_UNESCAPED_UNICODE);
                    if ($jsonMessage === false) {
                        throw new \Exception('JSON encode failed: '.json_last_error_msg());
                    }
                    throw new \Exception($jsonMessage);
                }
            }
            // BULK DATABASE UPDATE for transcorp and SPL
            foreach ($urlUpdates as $orderId => $url) {
                Order::where('id', $orderId)->update(['shipment_label_url' => $url]);
            }
        }

        // TIMING: Start PDF merging
        $mergingStartTime = microtime(true);
        $totalPdfCount = count($pdfUrls);
        Log::info('BULK PRINT LABELS MERGING START: Merging '.$totalPdfCount.' PDF files');

        $mergedPdfPath = $this->mergePdfUrls($pdfUrls);

        // TIMING: PDF merging done and overall completion
        $endTime = microtime(true);
        $mergingTime = round(($endTime - $mergingStartTime) * 1000, 2);
        $totalTime = round(($endTime - $startTime) * 1000, 2);

        Log::info('BULK PRINT LABELS MERGING: '.$mergingTime.'ms for merging '.$totalPdfCount.' PDFs');
        Log::info('BULK PRINT LABELS TOTAL: '.$totalTime.'ms for complete process ('.$total.' orders total, '.$existingLabelsCount.' existing, '.$newLabelsCount.' new)');

        return $mergedPdfPath;
    }

    /**
     * @param  string[]  $pdfUrls
     *
     * @throws CrossReferenceException
     * @throws FilterException
     * @throws PdfParserException
     * @throws PdfTypeException
     * @throws PdfReaderException
     */
    private function mergePdfUrls(array $pdfUrls): string
    {
        $fpdi = new Fpdi;
        $downloadStart = microtime(true);
        // CONCURRENT PDF DOWNLOADS - Major bottleneck fix!
        $responses = Http::pool(function (Pool $pool) use ($pdfUrls) {
            foreach ($pdfUrls as $index => $url) {
                $pool->as($index)->withOptions([
                    CURLOPT_ENCODING => 'identity',
                    'Content-Encoding' => 'UTF-8',
                    'Accept-Encoding' => 'gzip, deflate',
                    'Content-Type' => 'application/json',
                    'decode_content' => false,
                ])->timeout(30)->get($url);
            }
        });

        $downloadEnd = microtime(true);
        $downloadTime = ($downloadEnd - $downloadStart) * 1000;
        $processStart = microtime(true);
        $downloadCount = count($responses);

        // Process downloaded PDFs
        foreach ($responses as $index => $response) {
            if ($response->successful()) {
                $path = 'temp/temp_'.uniqid().'.pdf';
                Storage::disk('public')->put($path, $response->body());
                $tempPdfPath = Storage::disk('public')->path($path);

                $pageCount = $fpdi->setSourceFile($tempPdfPath);

                for ($i = 1; $i <= $pageCount; $i++) {
                    $template = $fpdi->importPage($i);
                    $size = $fpdi->getTemplateSize($template);
                    if (! is_array($size)) {
                        $jsonMessage = json_encode([
                            'error' => 'Unable to open PDF page',
                            'page_number' => $i,
                            'size_info' => $size,
                        ], JSON_UNESCAPED_UNICODE);
                        if ($jsonMessage === false) {
                            throw new \Exception('JSON encode failed: '.json_last_error_msg());
                        }
                        throw new \Exception($jsonMessage);
                    }
                    $fpdi->AddPage($size['orientation'], [$size['width'], $size['height']]);
                    $fpdi->useTemplate($template);
                }

                Storage::delete($path);
            }
        }

        $processEnd = microtime(true);
        $processTime = ($processEnd - $processStart) * 1000;

        $mergedPdfPath = 'merged_labels/merged_'.now()->timestamp.'.pdf';
        Storage::disk('public')->put($mergedPdfPath, $fpdi->Output('S'));

        // TIMING: PDF merge details
        $avgDownloadTime = $downloadCount > 0 ? round($downloadTime / $downloadCount, 2) : 0;
        Log::info('PDF MERGE BREAKDOWN: '.round($downloadTime, 2).'ms downloading '.$downloadCount.' PDFs ('.round($avgDownloadTime, 2).'ms avg), '.round($processTime, 2).'ms processing/merging');

        return $mergedPdfPath;
    }

    /**
     * @param  array<mixed>  $response
     *
     * @throws Exception
     */
    private function getShipmentExternalId(string $courier, array $response): string
    {
        if ($courier === CourierIdentifierEnum::ARAMEX->value) {
            if (isset($response['Shipments'][0]) && is_array($response['Shipments'][0])) {
                return $response['Shipments'][0]['ID'];
            }

            $jsonMessage = json_encode([
                'error' => 'Expected ID field does not exist in Aramex response',
                'courier' => $courier,
                'response_structure' => isset($response['Shipments'][0]) ? 'Shipments array exists' : 'Shipments array missing',
                'response' => $response,
            ], JSON_UNESCAPED_UNICODE);
            if ($jsonMessage === false) {
                throw new Exception('JSON encode failed: '.json_last_error_msg());
            }
            throw new Exception($jsonMessage);
        }
        if ($courier === CourierIdentifierEnum::BARQ->value) {
            return $response['id'];
        }
        if ($courier === CourierIdentifierEnum::TRANSCORP->value) {
            return $response['id'];
        }

        if ($courier === CourierIdentifierEnum::THABIT->value) {
            return $response['data']['id'];
        }
        if ($courier === CourierIdentifierEnum::JT->value) {
            if (! array_key_exists('data', $response)) {
                dd($response);
            }

            return $response['data']['billCode'];
        }

        if ($courier === CourierIdentifierEnum::SPL->value) {
            Log::info('SPL response: '.print_r($response, true));

            return $response['Items'][0]['ReferenceId'];
        }

        $jsonMessage = json_encode([
            'error' => 'Expected id field does not exist in response',
            'courier' => $courier,
            'response_keys' => array_keys($response),
        ], JSON_UNESCAPED_UNICODE);
        if ($jsonMessage === false) {
            throw new Exception('JSON encode failed: '.json_last_error_msg());
        }
        throw new Exception($jsonMessage);
    }

    /**
     * @param  array<mixed>  $response
     *
     * @throws Exception
     */
    private function getShipmentReference(string $courier, array $response): string
    {

        if ($courier === CourierIdentifierEnum::ARAMEX->value) {
            if (isset($response['Shipments'][0]) && is_array($response['Shipments'][0])) {
                if (! $response['Shipments'][0]['ID']) {
                    dd($response);
                }

                return $response['Shipments'][0]['ID'];
            }

            $jsonMessage = json_encode([
                'error' => 'Expected ID field does not exist in response',
                'courier' => $courier,
                'response_structure' => isset($response['Shipments'][0]) ? 'Shipments array exists' : 'Shipments array missing',
                'response' => $response,
            ], JSON_UNESCAPED_UNICODE);
            if ($jsonMessage === false) {
                throw new Exception('JSON encode failed: '.json_last_error_msg());
            }
            throw new Exception($jsonMessage);
        }
        if ($courier === CourierIdentifierEnum::BARQ->value) {
            return $response['id'];
        }
        if ($courier === CourierIdentifierEnum::TRANSCORP->value) {
            if (! array_key_exists('awb', $response)) {
                dd($response);
            }

            return $response['awb'];
        }

        if ($courier === CourierIdentifierEnum::THABIT->value) {
            return $response['data']['order_number'];
        }
        if ($courier === CourierIdentifierEnum::JT->value) {
            if (! array_key_exists('data', $response)) {
                dd($response);
            }

            return $response['data']['billCode'];
        }
        if ($courier === CourierIdentifierEnum::SPL->value) {
            if (isset($response['Items']) && is_array($response['Items'])) {
                return $response['Items'][0]['Barcode'];
            }

            $jsonMessage = json_encode([
                'error' => 'SPL response does not contain valid Barcodes',
                'courier' => $courier,
                'response_structure' => isset($response['Items']) ? 'Items array exists' : 'Items array missing',
                'items_count' => isset($response['Items']) ? count($response['Items']) : 0,
            ], JSON_UNESCAPED_UNICODE);
            if ($jsonMessage === false) {
                throw new Exception('JSON encode failed: '.json_last_error_msg());
            }
            throw new Exception($jsonMessage);
        }

        $jsonMessage = json_encode([
            'error' => 'Expected id field does not exist in response',
            'courier' => $courier,
            'response_keys' => array_keys($response),
        ], JSON_UNESCAPED_UNICODE);
        if ($jsonMessage === false) {
            throw new Exception('JSON encode failed: '.json_last_error_msg());
        }
        throw new Exception($jsonMessage);
    }

    /**
     * Create shipments for multiple orders using concurrent HTTP requests
     *
     * @param  Collection<int,Order>  $orders
     * @return array{successful: array<int, array{order_id: int, shipment_id: string}>, failed: array<int, array{order_id: int, error: string}>, total: int, successful_count: int, failed_count: int}
     *
     * @throws Exception
     */
    public function createBulkShipments(Collection $orders, bool $createdWithGlobalConfig): array
    {
        $successful = [];
        $failed = [];
        $total = $orders->count();
        $factory = app(ShippingServiceFactory::class);

        // TIMING: Start
        $startTime = microtime(true);
        Log::info("BULK SHIPMENT START: Processing {$total} orders");

        // Prepare all request data for concurrent processing
        $requestData = [];
        $orderMap = [];
        $serviceMap = [];

        foreach ($orders as $index => $order) {
            try {
                $courierName = $order->selected_shipment_company ?? 'aramex';
                $service = $factory->create(
                    serviceName: $courierName,
                    useGlobalConfig: $createdWithGlobalConfig,
                    order: $order
                );

                $requestDto = $service->createShipment($order);
                $requestData[$index] = $requestDto;
                $orderMap[$index] = $order;
                $serviceMap[$index] = ['service' => $service, 'courier' => $courierName];
            } catch (Exception $e) {
                $failed[] = [
                    'order_id' => $order->id,
                    'order_number' => $order->order_number,
                    'error' => 'Failed to prepare request: '.$e->getMessage(),
                ];
            }
        }

        // TIMING: Request preparation done
        $prepTime = microtime(true);
        Log::info('BULK SHIPMENT PREP: '.round(($prepTime - $startTime) * 1000, 2).'ms for '.count($requestData).' requests');

        // Initialize httpTime to ensure it's always defined
        $httpTime = microtime(true);

        // Execute concurrent HTTP requests using Http::pool
        if (! empty($requestData)) {
            $responses = Http::pool(function (Pool $pool) use ($requestData) {
                foreach ($requestData as $index => $dto) {
                    $request = $pool->as((string) $index)->withHeaders($dto->headers)->timeout(30);

                    if ($dto->contentType === 'form') {
                        $request = $request->asForm();
                    }

                    $request->{strtolower($dto->method)}($dto->url, $dto->payload);
                }
            });
            // TIMING: HTTP requests done
            $httpTime = microtime(true);
            Log::info('BULK SHIPMENT HTTP: '.round(($httpTime - $prepTime) * 1000, 2).'ms for '.count($requestData).' concurrent requests');
            Log::info('BULK SHIPMENT REQUEST DATA: '.print_r($requestData, true));
            Log::info('BULK SHIPMENT RESPONSES: '.print_r($responses, true));
            // Process responses
            foreach ($responses as $index => $response) {
                $order = $orderMap[$index];
                $serviceInfo = $serviceMap[$index];
                $service = $serviceInfo['service'];
                $courierName = $serviceInfo['courier'];

                try {
                    if ($response->successful()) {
                        $responseData = $response->json();
                        // Check for API-specific errors
                        $hasAramexError = isset($responseData['HasErrors']) && $responseData['HasErrors'];
                        $hasThabitError = isset($responseData['status']) && $responseData['status'] === 'error';

                        if ($hasAramexError || $hasThabitError) {
                            $failed[] = [
                                'order_id' => $order->id,
                                'order_number' => $order->order_number,
                                'error' => 'API Error: '.json_encode($responseData),
                            ];

                            continue;
                        }

                        // Extract shipment details and update order directly
                        $shipmentReference = $this->getShipmentReference($courierName, $responseData);
                        $shipmentExternalId = $this->getShipmentExternalId($courierName, $responseData);
                        $shipmentCredentialsType = $createdWithGlobalConfig ? 'application' : 'user';

                        // Get the courier service for volumetric divisor
                        $courierService = ShipmentCourierService::where('identifier', $courierName)->firstOrFail();
                        $volumetricDivisor = $courierService->getVolumetricDivisor();

                        // Update order directly
                        $order->update([
                            'shipment_reference' => $shipmentReference,
                            'shipment_company' => CourierIdentifierEnum::from($courierName)->value,
                            'shipment_tracking_link' => $service::SHIPMENT_TRACKING_URL.$shipmentReference,
                            'shipment_credentials_type' => $shipmentCredentialsType,
                            'shipment_credentials' => $factory->resolveConfig(
                                serviceName: $courierName,
                                useGlobalConfig: $createdWithGlobalConfig,
                                order: $order
                            ),
                            'shipment_cost' => $service->getRates(ShippingRateDto::fromOrder(order: $order)),
                            'shipment_cost_without_cod' => $service->getRates(ShippingRateDto::fromOrder(order: $order, ignoreCod: true)),
                            'shipment_external_id' => $shipmentExternalId,
                            'base_shipment_cost' => $service->getCosts(ShippingRateDto::fromOrder(order: $order)),
                            'base_shipment_cod_cost' => 0,
                            'volumetric_divisor' => $courierService->getVolumetricDivisor(),
                        ]);

                        // Update status using centralized method
                        $order->updateStatus(OrderStatusEnum::AWAITING_PICKUP->value);

                        // Handle Shopify fulfillment for bulk shipments via queue
                        if ($order->source === SalesChannelEnum::SHOPIFY->value && $order->external_id !== null) {
                            UpdateShopifyFulfillmentJob::dispatch(
                                orderId: $order->id,
                                trackingNumber: $shipmentReference,
                                trackingCompany: $courierName,
                                trackingUrl: $order->shipment_tracking_link
                            );

                            Log::info('Shopify fulfillment job dispatched for bulk operation', [
                                'order_id' => $order->id,
                                'order_number' => $order->order_number,
                                'tracking_number' => $shipmentReference,
                                'tracking_company' => $courierName,
                            ]);
                        }

                        // Dispatch WhatsApp message job for bulk shipment
                        SendShipmentWhatsAppJob::dispatch($order);

                        $successful[] = [
                            'order_id' => $order->id,
                            'order_number' => $order->order_number,
                            'shipment_id' => $shipmentReference,
                        ];

                    } else {
                        $failed[] = [
                            'order_id' => $order->id,
                            'order_number' => $order->order_number,
                            'error' => 'HTTP Error: '.$response->status().' - '.$response->body(),
                        ];
                    }

                } catch (Exception $e) {
                    $failed[] = [
                        'order_id' => $order->id,
                        'order_number' => $order->order_number,
                        'error' => 'Processing error: '.$e->getMessage(),
                    ];
                }
            }
        }

        // TIMING: Processing done
        $endTime = microtime(true);
        $totalTime = round(($endTime - $startTime) * 1000, 2);
        $processingTime = round(($endTime - $httpTime) * 1000, 2);
        Log::info("BULK SHIPMENT PROCESSING: {$processingTime}ms");
        Log::info("BULK SHIPMENT TOTAL: {$totalTime}ms");

        $successfulCount = count($successful);
        $failedCount = count($failed);

        // Send summary notification
        if ($successfulCount > 0 && $failedCount === 0) {
            Notification::make()
                ->title(__('translation.bulk_shipments_all_successful', ['count' => $successfulCount]))
                ->success()
                ->send();
        } elseif ($successfulCount > 0 && $failedCount > 0) {
            Notification::make()
                ->title(__('translation.bulk_shipments_partial', ['successful' => $successfulCount, 'failed' => $failedCount]))
                ->warning()
                ->send();
        } else {
            Notification::make()
                ->title(__('translation.bulk_shipments_all_failed', ['count' => $failedCount]))
                ->danger()
                ->send();
        }

        return [
            'successful' => $successful,
            'failed' => $failed,
            'total' => $total,
            'successful_count' => $successfulCount,
            'failed_count' => $failedCount,
        ];
    }
}
