<?php

namespace App\Services;

use App\Dto\PricingConfigDto;
use App\Dto\ShipmentRequestDto;
use App\Dto\ShippingRateDto;
use App\Enums\CostCodTypeEnum;
use App\Enums\CourierIdentifierEnum;
use App\Interfaces\ShipmentCourierInterface;
use App\Models\Order;
use Barryvdh\Snappy\Facades\SnappyPdf;
use Filament\Notifications\Notification;
use Illuminate\Http\Client\PendingRequest;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Picqer\Barcode\BarcodeGeneratorPNG;
use setasign\Fpdi\Fpdi;

class SPLApiService implements ShipmentCourierInterface
{
    protected string $baseUrl = 'https://apibusiness.splonline.com.sa/api';

    protected string $tokenUrl = 'https://apibusiness.splonline.com.sa/token';

    const string SHIPMENT_TRACKING_URL = 'https://splonline.com.sa/en/shipmentdetailsstatic/?tid=';

    protected ?string $token = null;

    /**
     * @var array<string, string|int>
     */
    protected array $clientInfo = [];

    /**
     * @var string[]
     */
    protected array $config = [];

    /**
     * @param  array<string, int|string|null>  $config
     */
    public function setConfig(array $config): void
    {
        $this->clientInfo = [
            'UserName' => $config['username'] ?? null,
            'Password' => $config['password'] ?? null,
            'AccountPin' => $config['account_pin'] ?? null,
            'CRMAccountId' => $config['crm_account_id'] ?? null,
            'ContractId' => $config['contract_id'] ?? null,
        ];

        // Allow overriding base URLs from database config
        if (isset($config['base_url'])) {
            $this->baseUrl = (string) $config['base_url'];
        }
        if (isset($config['token_url'])) {
            $this->tokenUrl = (string) $config['token_url'];
        }
    }

    /**
     * Ensure authentication and fetch token if not set.
     */
    private function ensureAuthenticated(): void
    {
        if ($this->token) {
            return;
        }

        $username = $this->clientInfo['UserName'] ?? null;
        $password = $this->clientInfo['Password'] ?? null;

        if (! $username || ! $password) {
            Log::error('SPL API credentials not configured', [
                'has_username' => ! empty($username),
                'has_password' => ! empty($password),
            ]);
            $jsonMessage = json_encode([
                'error' => 'SPL API credentials not configured',
                'message' => 'Please ensure username and password are set in merchant configuration',
                'username' => $username,
                'password_set' => ! empty($password),
            ], JSON_UNESCAPED_UNICODE);
            if ($jsonMessage === false) {
                throw new \Exception('JSON encode failed: '.json_last_error_msg());
            }
            throw new \Exception($jsonMessage);
        }

        try {
            $response = Http::asForm()->timeout(40)->post($this->tokenUrl, [
                'userName' => $username,
                'password' => $password,
                'grant_type' => 'password',
            ]);

            if ($response->successful()) {
                $data = $response->json();

                if (isset($data['access_token'])) {
                    $this->token = $data['access_token'];

                    Log::info('SPL API authentication successful', [
                        'expires_in' => $data['expires_in'] ?? null,
                        'token_type' => $data['token_type'] ?? null,
                        'username' => $username,
                    ]);

                    return;
                }
            }

            Log::error('SPL API authentication failed', [
                'status' => $response->status(),
                'response' => $response->json(),
                'username' => $username,
            ]);

            $jsonMessage = json_encode([
                'error' => 'SPL API authentication failed',
                'username' => $username,
                'response_body' => $response->body(),
                'status_code' => $response->status(),
            ], JSON_UNESCAPED_UNICODE);
            if ($jsonMessage === false) {
                throw new \Exception('JSON encode failed: '.json_last_error_msg());
            }
            throw new \Exception($jsonMessage);
        } catch (\Exception $e) {
            Log::error('SPL API authentication error', [
                'error' => $e->getMessage(),
                'username' => $username,
            ]);
            $jsonMessage = json_encode([
                'error' => 'Failed to authenticate with SPL API',
                'original_error' => $e->getMessage(),
                'username' => $username,
            ], JSON_UNESCAPED_UNICODE);
            if ($jsonMessage === false) {
                throw new \Exception('JSON encode failed: '.json_last_error_msg());
            }
            throw new \Exception($jsonMessage);
        }
    }

    /**
     * Common HTTP client with token authentication.
     *
     * @throws \Exception
     */
    private function httpClient(): PendingRequest
    {
        $this->ensureAuthenticated();

        return Http::withToken($this->token);
    }

    /**
     * Get Access Token (for debugging or testing)
     *
     * @return string[]
     *
     * @throws \Exception
     */
    public function getAccessToken(): array
    {
        $this->ensureAuthenticated();

        return ['access_token' => $this->token];
    }

    /**
     * Validate configuration before making API calls
     */
    private function validateConfiguration(): void
    {
        $crmAccountId = $this->clientInfo['CRMAccountId'] ?? null;
        $contractId = $this->clientInfo['ContractId'] ?? null;
        $username = $this->clientInfo['UserName'] ?? null;
        $password = $this->clientInfo['Password'] ?? null;

        if (! $username || ! $password) {
            $jsonMessage = json_encode([
                'error' => 'SPL API credentials not configured in merchant settings',
                'username' => $username,
                'password_set' => ! empty($password),
            ], JSON_UNESCAPED_UNICODE);
            if ($jsonMessage === false) {
                throw new \Exception('JSON encode failed: '.json_last_error_msg());
            }
            throw new \Exception($jsonMessage);
        }

        if (! $crmAccountId || ! $contractId) {
            $jsonMessage = json_encode([
                'error' => 'SPL CRM Account ID and Contract ID must be configured in merchant settings',
                'crm_account_id' => $crmAccountId,
                'contract_id' => $contractId,
            ], JSON_UNESCAPED_UNICODE);
            if ($jsonMessage === false) {
                throw new \Exception('JSON encode failed: '.json_last_error_msg());
            }
            throw new \Exception($jsonMessage);
        }

        // Validate CRMAccountId is numeric
        if (! is_numeric($crmAccountId)) {
            $jsonMessage = json_encode([
                'error' => 'SPL CRM Account ID must be a valid number',
                'value' => $crmAccountId,
                'type' => gettype($crmAccountId),
            ], JSON_UNESCAPED_UNICODE);
            if ($jsonMessage === false) {
                throw new \Exception('JSON encode failed: '.json_last_error_msg());
            }
            throw new \Exception($jsonMessage);
        }

        // Validate ContractId is not empty
        if (empty(trim((string) $contractId))) {
            $jsonMessage = json_encode([
                'error' => 'SPL Contract ID cannot be empty',
                'value' => $contractId,
            ], JSON_UNESCAPED_UNICODE);
            if ($jsonMessage === false) {
                throw new \Exception('JSON encode failed: '.json_last_error_msg());
            }
            throw new \Exception($jsonMessage);
        }
    }

    /**
     * Create shipment according to SPL API documentation
     */
    public function createShipment(Order $order): ShipmentRequestDto
    {
        if (auth()->user()->id !== 33) {
            Notification::make()
                ->title('SPL Shipment is under maintenance')
                ->warning()
                ->send();
            throw new \Exception('SPL Shipment is under maintenance');
        }

        $orderboxes = $order->boxes;

        // if ($orderboxes->isEmpty()) {
        //     $jsonMessage = json_encode([
        //         'error' => 'No boxes found for the order',
        //         'order_id' => $order->id,
        //         'order_number' => $order->order_number,
        //     ], JSON_UNESCAPED_UNICODE);
        //     if ($jsonMessage === false) {
        //         throw new \Exception('JSON encode failed: '.json_last_error_msg());
        //     }
        //     throw new \Exception($jsonMessage);
        // }

        // Validate configuration first
        $this->validateConfiguration();

        // Generate unique reference ID
        $referenceId = $order->order_number.'-'.time();

        // Get validated configuration
        $crmAccountId = $this->clientInfo['CRMAccountId'];
        $contractId = $this->clientInfo['ContractId'];

        // Validate required order data
        if (! $order->receiver_first_name && ! $order->receiver_last_name) {
            $jsonMessage = json_encode([
                'error' => 'Customer name is required for SPL shipment',
                'order_id' => $order->id,
                'receiver_first_name' => $order->receiver_first_name,
                'receiver_last_name' => $order->receiver_last_name,
            ], JSON_UNESCAPED_UNICODE);
            if ($jsonMessage === false) {
                throw new \Exception('JSON encode failed: '.json_last_error_msg());
            }
            throw new \Exception($jsonMessage);
        }

        $customerName = trim(($order->receiver_first_name ?? '').' '.($order->receiver_last_name ?? ''));
        if (empty($customerName)) {
            $customerName = 'Unknown Customer';
        }

        $senderName = $order->shipper_name;
        if (empty($senderName)) {
            $senderName = 'Unknown Sender';
        }

        // Ensure we have valid coordinates or set defaults
        $senderLatitude = ! empty($order->shipper_latitude) && $order->shipper_latitude != 0 ? (string) $order->shipper_latitude : '24.7136';
        $senderLongitude = ! empty($order->shipper_longitude) && $order->shipper_longitude != 0 ? (string) $order->shipper_longitude : '46.6753';

        // Validate receiver coordinates - ensure lat and lng are different and valid
        $receiverLat = $order->receiver_latitude;
        $receiverLng = $order->receiver_longitude;

        // If coordinates are invalid, missing, or the same (which is geographically impossible), use defaults
        if (empty($receiverLat) || empty($receiverLng) || $receiverLat == 0 || $receiverLng == 0 || $receiverLat == $receiverLng) {
            $receiverLatitude = '24.7136';  // Default Riyadh latitude
            $receiverLongitude = '46.6753'; // Default Riyadh longitude
        } else {
            $receiverLatitude = (string) $receiverLat;
            $receiverLongitude = (string) $receiverLng;
        }

        // Get city IDs or use defaults
        $senderLocationId = ! empty($order->shipper_city_id) ? (string) $order->shipper_city_id : '3';
        $receiverLocationId = ! empty($order->receiver_city_id) ? (string) $order->receiver_city_id : '3';

        $receiverCity = $order->receiverCity ? $order->receiverCity->name_ar ?? ($order->receiverCity->name ?? '') : '';
        $receiverCountry = $order->receiverCountry ? $order->receiverCountry->name_ar ?? ($order->receiverCountry->name ?? '') : '';
        $receiverStreetName = $order->receiver_street_name ?? '';
        $receiverBlock = $order->receiver_block ?? '';

        // Process receiver building number - check if it's numeric and 4 digits or less
        $receiverBuildingNumber = null;
        if (! $order->receiver_block) {
            $receiverBuildingNumber = null;
        }
        if ((is_numeric($order->receiver_block)) && (strlen($order->receiver_block) <= 4)) {
            $receiverBuildingNumber = (int) $order->receiver_block;
        }

        // Prepare items array according to API documentation - map all order items
        $items = [[
            'ReferenceId' => $referenceId,
            'Barcode' => null,
            'PaymentType' => $order->isCod() ? 2 : 1, // 1 = paid, 2 = Cash on delivery
            'ContentPrice' => (float) ($order->order_grand_total ?: 0),
            'ContentDescription' => $order->description ?: 'Package delivery',
            'Weight' => $order->getMaxWeight() ?: 1, // Weight in KG (Max 30 KG)
            'TotalAmount' => $order->isCod() ? (float) ($order->order_grand_total ?: 0) : 0, // Mandatory if PaymentType = 2
            'SenderAddressDetail' => [
                'AddressTypeID' => '7', // MapCoordinates = 7 - should be string according to docs
                'LocationID' => $senderLocationId,
                'Latitude' => $senderLatitude,
                'Longitude' => $senderLongitude,
            ],
            'ReceiverAddressDetail' => [
                'AddressTypeID' => '6', // MapCoordinates = 7 - should be string according to docs
                'LocationID' => $receiverLocationId,
                'Latitude' => $receiverLatitude,
                'Longitude' => $receiverLongitude,
                'BuildingNo' => $receiverBuildingNumber,
                'ZipCode' => $order->receiver_postal_code,
                // TODO: AddressLine1 legnth must be 100 characters or less
                'AddressLine1' => $order->receiver_address_line ?? $receiverCountry.', '.$receiverCity.', '.$receiverStreetName.', '.$receiverBlock,
            ],
            // 'PiecesCount' => (int) ($item->quantity ?: 1),
        ]];

        // Construct the data payload according to API documentation
        $data = [
            'CRMAccountId' => (int) $crmAccountId, // Should be long/int according to documentation
            // 'ContractId' => (string) $contractId,
            'BranchId' => null, // Optional - but explicitly set to null
            'PickupType' => 0, // 0 = client will give items himself to POS, 1 = to be picked up
            'RequestTypeId' => 1, // Package Delivery = 1, Reverse Logistic = 2
            'CustomerName' => $customerName,
            'CustomerMobileNumber' => $this->formatPhoneNumber($order->receiver_phone),
            'SenderName' => $senderName,
            'SenderMobileNumber' => $this->formatPhoneNumber($order->shipper_phone),
            'Items' => $items,
        ];

        // Log the payload for debugging
        Log::info('SPL AddUPDSPickupDelivery Payload', [
            'order_id' => $order->id,
            'reference_id' => $referenceId,
            'original_coordinates' => [
                'sender_lat' => $order->shipper_latitude,
                'sender_lng' => $order->shipper_longitude,
                'receiver_lat' => $order->receiver_latitude,
                'receiver_lng' => $order->receiver_longitude,
            ],
            'processed_coordinates' => [
                'sender_lat' => $senderLatitude,
                'sender_lng' => $senderLongitude,
                'receiver_lat' => $receiverLatitude,
                'receiver_lng' => $receiverLongitude,
            ],
            'payload' => $data,
        ]);

        $url = "{$this->baseUrl}/CreditSale/AddUPDSPickupDelivery";
        $this->ensureAuthenticated();
        $headers = [
            'Authorization' => 'Bearer '.$this->token,
            'Content-Type' => 'application/json',
        ];

        return ShipmentRequestDto::create($url, $data, $headers);
    }

    /**
     * Format phone number according to SPL requirements
     * Min Length = 10, Max Length = 15 (numbers only)
     */
    private function formatPhoneNumber(?string $phone): string
    {
        if (! $phone) {
            return '966509208529'; // Default fallback
        }

        // Remove all non-numeric characters
        $phone = preg_replace('/[^0-9]/', '', $phone);

        // Ensure it's between 10-15 digits
        if (strlen($phone) < 10) {
            return '966509208529'; // Default fallback
        }

        if (strlen($phone) > 15) {
            $phone = substr($phone, 0, 15);
        }

        return $phone;
    }

    public function printLabel(Order $order): ShipmentRequestDto
    {
        // SPL service generates labels locally using views, not via API
        // Return a dummy request since no actual HTTP call is needed
        return ShipmentRequestDto::create('local://spl-label', ['order_id' => $order->id], [], 'LOCAL');
    }

    public function extractPrintLabelUrl($response, ?Order $order = null): string
    {
        if (! $order) {
            throw new \Exception(json_encode([
                'error' => 'SPL service requires Order object for local label generation',
            ], JSON_UNESCAPED_UNICODE) ?: 'Failed to encode error');
        }

        $boxes = $order->boxes()->get();

        if ($boxes->isEmpty()) {
            throw new \Exception(json_encode([
                'error' => 'No boxes found for label generation',
                'order_id' => $order->id,
            ], JSON_UNESCAPED_UNICODE) ?: 'Failed to encode error');
        }

        $tempFiles = [];

        $generator = new BarcodeGeneratorPNG;
        $barcodeImage = $generator->getBarcode($order->shipment_reference, $generator::TYPE_CODE_128);

        $barcodePath = "barcodes/{$order->shipment_reference}.png";
        Storage::disk('public')->put($barcodePath, $barcodeImage);

        $barcodeImagePath = public_path("storage/{$barcodePath}");

        try {
            foreach ($boxes as $index => $box) {

                $pdf = SnappyPdf::loadView('pdfs.spl_awb', [
                    'order' => $order,
                    'barcodeImagePath' => $barcodeImagePath,
                    'numAwb' => $index + 1,
                ]);

                $tempPath = Storage::disk('public')->path("temp/temp_{$order->shipment_reference}_{$index}.pdf");
                file_put_contents($tempPath, $pdf->output());
                $tempFiles[] = $tempPath;
            }

            if (empty($tempFiles)) {
                throw new \Exception('No label PDFs generated');
            }

            $mergedPdf = new Fpdi;

            foreach ($tempFiles as $file) {
                $pageCount = $mergedPdf->setSourceFile($file);

                for ($pageNo = 1; $pageNo <= $pageCount; $pageNo++) {
                    $tplIdx = $mergedPdf->importPage($pageNo);
                    $size = $mergedPdf->getTemplateSize($tplIdx);

                    if (! is_array($size)) {
                        throw new \Exception("Failed to get template size for file: {$file}");
                    }

                    $orientation = $size['orientation'] ?? 'P';
                    $width = $size['width'] ?? 210;
                    $height = $size['height'] ?? 297;

                    $mergedPdf->AddPage($orientation, [$width, $height]);
                    $mergedPdf->useTemplate($tplIdx);
                }
            }

            $mergedPath = "labels/{$order->id}_merged_labels.pdf";
            Storage::disk('public')->put($mergedPath, $mergedPdf->Output('S'));

            foreach ($tempFiles as $file) {
                @unlink($file);
            }

            Storage::disk('public')->delete("barcodes/{$order->shipment_reference}.png");

            return Storage::disk('public')->url($mergedPath);

        } catch (\Exception $e) {
            Log::error('SPL Failed to print multiple labels', [
                'order_id' => $order->id,
                'error_message' => $e->getMessage(),
            ]);

            throw new \Exception(json_encode([
                'error' => 'Failed to print multiple labels',
                'original_error' => $e->getMessage(),
                'order_id' => $order->id,
            ], JSON_UNESCAPED_UNICODE) ?: 'Failed to encode error');
        }
    }

    /**
     * Get item events for tracking - using dynamic barcode from order
     *
     * @return array<mixed>
     */
    public function getItemEvents(?string $barcode = null): array
    {
        if (! $barcode) {
            $jsonMessage = json_encode([
                'error' => 'Barcode is required for tracking',
                'barcode' => $barcode,
            ], JSON_UNESCAPED_UNICODE);
            if ($jsonMessage === false) {
                throw new \Exception('JSON encode failed: '.json_last_error_msg());
            }
            throw new \Exception($jsonMessage);
        }

        $response = $this->httpClient()
            ->post("{$this->baseUrl}/Tracking/GetItemEvents", [
                'ItemBarCode' => $barcode,
            ])
            ->json();

        // Log the response for debugging
        Log::info('SPL API getItemEvents response', [
            'barcode' => $barcode,
            'response' => $response,
        ]);

        return $response ?? [];
    }

    /**
     * Track shipment using shipment reference
     */
    public function trackShipments(array $trackingNumbers): array
    {
        $results = [];

        foreach ($trackingNumbers as $trackingNumber) {
            try {
                $results[$trackingNumber] = $this->getItemEvents((string) $trackingNumber);
            } catch (\Exception $e) {
                Log::error('SPL tracking failed', [
                    'tracking_number' => $trackingNumber,
                    'error' => $e->getMessage(),
                ]);
                $results[$trackingNumber] = ['error' => $e->getMessage()];
            }
        }

        return $results;
    }

    public function createReverseShipment(Order $order): array
    {
        // For reverse shipments, we need to use RequestTypeId = 2 (Reverse Logistic)
        // This would require a similar structure to createShipment but with reverse logic
        $jsonMessage = json_encode([
            'error' => 'Reverse shipment functionality needs to be implemented based on business requirements',
            'order_id' => $order->id,
            'order_number' => $order->order_number,
        ], JSON_UNESCAPED_UNICODE);
        if ($jsonMessage === false) {
            throw new \Exception('JSON encode failed: '.json_last_error_msg());
        }
        throw new \Exception($jsonMessage);
    }

    public function cancelShipment(string $orderNumber): array
    {
        // SPL API documentation doesn't include cancel shipment endpoint
        // This would need to be clarified with SPL support
        $jsonMessage = json_encode([
            'error' => 'Cancel shipment functionality is not available in current SPL API version',
            'order_number' => $orderNumber,
        ], JSON_UNESCAPED_UNICODE);
        if ($jsonMessage === false) {
            throw new \Exception('JSON encode failed: '.json_last_error_msg());
        }
        throw new \Exception($jsonMessage);
    }

    public function getRates(ShippingRateDto $shippingRateDto): int
    {
        if ($shippingRateDto->isInternational) {
            $jsonMessage = json_encode([
                'error' => 'International shipping rates are not supported',
                'order_id' => $shippingRateDto->order_id ?? 'unknown',
                'is_international' => $shippingRateDto->isInternational,
            ], JSON_UNESCAPED_UNICODE);
            if ($jsonMessage === false) {
                throw new \Exception('JSON encode failed: '.json_last_error_msg());
            }
            throw new \Exception($jsonMessage);
        }

        // Get pricing configuration from factory
        $factory = app(ShippingServiceFactory::class);
        $pricingConfig = $factory->getPricingConfig(
            CourierIdentifierEnum::SPL->value,
            $shippingRateDto
        );

        return $this->calculateRateFromConfig($pricingConfig, $shippingRateDto);
    }

    /**
     * Calculate shipping cost from configuration data for rates
     */
    private function calculateRateFromConfig(PricingConfigDto $pricingConfig, ShippingRateDto $shippingRateDto): int
    {
        // Calculate total weight from order boxes
        $totalWeight = ($shippingRateDto->calculateWeightFromBoxes($pricingConfig->volumetricDivisor) * 100);

        $basePrice = $pricingConfig->basePrice;
        $additionalWeightCost = 0;
        $distanceCost = 0;

        // Calculate additional weight cost
        if ($totalWeight > $pricingConfig->extraWeightFrom) {
            $additionalWeight = $totalWeight - $pricingConfig->extraWeightFrom;
            $additionalWeightCost = ($additionalWeight / 100) * $pricingConfig->additionalWeightCost;
        }

        // Add cash on delivery cost if applicable
        if ($shippingRateDto->isCod && ! $shippingRateDto->ignoreCod) {
            $basePrice = $basePrice + $pricingConfig->cashOnDeliveryCost;
        }

        // Calculate distance cost if applicable
        if ($pricingConfig->distanceCost > 0) {
            // TODO: Implement distance calculation logic
            // $distanceCost = $this->calculateDistanceCost($shippingRateDto, $pricingConfig->distanceCost);
        }

        // Calculate the total cost
        return $basePrice + $additionalWeightCost + $distanceCost;
    }

    public function getCosts(ShippingRateDto $shippingRateDto): int
    {
        if ($shippingRateDto->isInternational) {
            return 999999999999; // Very high cost for unsupported international shipping
        }

        // Get pricing configuration from factory
        $factory = app(ShippingServiceFactory::class);
        $pricingConfig = $factory->getCostPricingConfig(
            CourierIdentifierEnum::SPL->value,
            $shippingRateDto
        );

        return $this->calculateCostFromConfig($pricingConfig, $shippingRateDto);
    }

    /**
     * Calculate shipping cost from configuration data for costs
     */
    private function calculateCostFromConfig(PricingConfigDto $pricingConfig, ShippingRateDto $shippingRateDto): int
    {
        // Calculate total weight from order boxes
        $totalWeight = ($shippingRateDto->calculateWeightFromBoxes($pricingConfig->volumetricDivisor) * 100);

        $basePrice = $pricingConfig->basePrice;
        $additionalWeightCost = 0;

        // Calculate additional weight cost
        if ($totalWeight > $pricingConfig->extraWeightFrom) {
            $additionalWeight = $totalWeight - $pricingConfig->extraWeightFrom;
            $additionalWeightCost = ($additionalWeight / 100) * $pricingConfig->additionalWeightCost;
        }

        // Add cash on delivery cost if applicable (VALUE type)
        if ($shippingRateDto->isCod && ! $shippingRateDto->ignoreCod && $pricingConfig->codType === CostCodTypeEnum::VALUE->value) {
            $basePrice = $basePrice + $pricingConfig->cashOnDeliveryCost;
        }

        // Add cash on delivery cost if applicable (PERCENTAGE type)
        if ($shippingRateDto->isCod && ! $shippingRateDto->ignoreCod && $pricingConfig->codType === CostCodTypeEnum::PERCENTAGE->value) {
            $codValue = (int) (($basePrice * $pricingConfig->cashOnDeliveryCost) / 100);
            $basePrice = $basePrice + $codValue;
        }

        // Add additional weight cost to base price
        $basePrice = $basePrice + $additionalWeightCost;

        // Calculate fuel cost
        $fuelCost = ($basePrice * $pricingConfig->fuel) / 100;

        // Calculate the total cost
        return $basePrice + $fuelCost;
    }

    /**
     * Get countries - API endpoint from documentation
     *
     * @return array<mixed>
     */
    public function getCountries(?int $countryId = null): array
    {
        $data = [];
        if ($countryId) {
            $data['CountryId'] = $countryId;
        }

        return $this->httpClient()
            ->post("{$this->baseUrl}/Location/GetCountries", $data)
            ->json();
    }

    /**
     * Get all regions - API endpoint from documentation
     *
     * @return array<mixed>
     */
    public function getAllRegions(): array
    {
        return $this->httpClient()
            ->post("{$this->baseUrl}/GIS/GetAllRegions", [
                'language' => 'E', // E for English, A for Arabic
            ])
            ->json();
    }

    /**
     * Get cities by region - API endpoint from documentation
     *
     * @return array<mixed>
     */
    public function getCitiesByRegion(int $regionId): array
    {
        return $this->httpClient()
            ->post("{$this->baseUrl}/GIS/GetCitiesByRegion", [
                'language' => 'E',
                'RegionId' => $regionId,
            ])
            ->json();
    }

    /**
     * Get districts - API endpoint from documentation
     *
     * @return array<mixed>
     */
    public function getDistricts(int $gisCityId): array
    {
        return $this->httpClient()
            ->get("{$this->baseUrl}/Location/GetDistricts", [
                'gisCityid' => $gisCityId,
            ])
            ->json();
    }

    /**
     * Get postal office by city - API endpoint from documentation
     *
     * @return array<mixed>
     */
    public function getPostalOfficeByCity(int $gisCityId): array
    {
        return $this->httpClient()
            ->post("{$this->baseUrl}/ServiceProvider/GetPostalOfficeByCity", [
                'GISCityId' => $gisCityId,
            ])
            ->json();
    }
}
