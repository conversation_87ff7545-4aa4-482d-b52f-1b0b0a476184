<?php

namespace App\Services;

use App\Dto\PricingConfigDto;
use App\Dto\ShipmentRequestDto;
use App\Dto\ShippingRateDto;
use App\Enums\AlthabitEnums\ChargeTypeEnum;
use App\Enums\AlthabitEnums\PayerEnum;
use App\Enums\CostCodTypeEnum;
use App\Enums\CourierIdentifierEnum;
use App\Interfaces\ShipmentCourierInterface;
use App\Models\Order;
use Illuminate\Http\Client\ConnectionException;
use Illuminate\Http\Client\RequestException;
use Illuminate\Support\Facades\Http;

class AlthabitService implements ShipmentCourierInterface
{
    protected string $baseUrl = 'https://prodgw.thabbit.shipox.com/api';

    const string SHIPMENT_TRACKING_URL = 'https://thabit-logistics.com/track-shipment?id=';

    protected string $token = '';

    /**
     * @var array<string, string|int>
     */
    protected array $clientInfo;

    /**
     * @var string[]
     */
    protected array $config = [];

    /**
     * @param  string[]  $config
     */
    public function setConfig(array $config): void
    {
        $this->clientInfo = [
            'username' => $config['username'] ?? $this->clientInfo['username'] ?? null,
            'password' => $config['password'] ?? $this->clientInfo['password'] ?? null,
        ];
    }

    /**
     * @return string[]
     */
    private function headers(): array
    {
        return [
            'Authorization' => 'Bearer '.$this->token,
            'Content-Type' => 'application/json',
            'Accept' => 'application/json',
        ];
    }

    /**
     * @throws RequestException
     */
    public function login(): void
    {
        $response = Http::post("{$this->baseUrl}/v1/customer/authenticate", [
            'username' => $this->clientInfo['username'],
            'password' => $this->clientInfo['password'],
        ]);

        if ($response->successful()) {
            $this->token = $response->json('data.id_token');

            return;
        }
        $response->throw();
    }

    //    /**
    //     * Retrieve the country ID by its name using the Althabit API.
    //     *
    //     *
    //     * @param  string  $countryName  The name of the country to search for.
    //     * @return int|null The country ID if a match is found, or null if not.
    //     *
    //     * @throws \Exception If there is an error during the API request, null will be returned.
    //     */
    //    public function getCountryIdByName(string $countryName): ?int
    //    {
    //        try {
    //            if (! $this->token) {
    //                $this->login();
    //            }
    //            $response = Http::withHeaders($this->headers())
    //                ->get($this->baseUrl.'/v2/customer/countries', [
    //                    'search' => $countryName,
    //                    'size' => 5,
    //                ]);
    //
    //            if ($response->successful()) {
    //                $countries = $response->json()['data']['list'];
    //
    //                foreach ($countries as $country) {
    //                    if (strcasecmp($country['name'], $countryName) === 0) {
    //                        return $country['id'];
    //                    }
    //                }
    //
    //                return null;
    //            }
    //
    //            return null;
    //
    //        } catch (\Exception $e) {
    //            return null;
    //        }
    //    }

    /**
     * Retrieve the city ID by its name using the Althabit API.
     *
     *
     * @param  string  $cityName  The name of the city to search for.
     * @return int|null The city ID if a match is found, or null if not.
     */
    public function getCityIdByName(string $cityName): ?int
    {
        try {
            if (! $this->token) {
                $this->login();
            }
            $response = Http::withHeaders($this->headers())
                ->get($this->baseUrl.'/v2/customer/cities', [
                    'search' => $cityName,
                    'size' => 5,
                ]);

            if ($response->successful()) {
                $cities = $response->json()['data']['list'];

                foreach ($cities as $city) {
                    if (strcasecmp($city['name'], $cityName) === 0) {
                        return $city['id'];
                    }
                }

                return null;
            }

            return null;

        } catch (\Exception $e) {
            return null;
        }
    }

    //    /**
    //     * Retrieve the list of available package types (service types) from the Althabit API.
    //     *
    //     *
    //     * @return array An array of package types (service types) containing their id, code, and name.
    //     *
    //     * @throws \Exception If there is an error during the API request, an empty array will be returned.
    //     */
    //    public function getPackageTypes(): array
    //    {
    //        try {
    //            if (! $this->token) {
    //                $this->login();
    //            }
    //            $response = Http::withHeaders($this->headers())
    //                ->get($this->baseUrl.'/v1/service_types');
    //
    //            if ($response->successful()) {
    //                return $response->json()['data']['list'];
    //            }
    //
    //            return [];
    //
    //        } catch (\Exception $e) {
    //            return [];
    //        }
    //    }

    //    /**
    //     * Retrieve package price details based on the provided parameters from the Althabit API.
    //     *
    //     *
    //     * @param  float  $fromLatitude  The latitude from which the parcel is being picked up.
    //     * @param  float  $fromLongitude  The longitude from which the parcel is being picked up.
    //     * @param  float  $toLatitude  The latitude where the parcel is going to be delivered to.
    //     * @param  float  $toLongitude  The longitude where the parcel is going to be delivered to.
    //     * @param  int  $toCountryId  The country ID where the parcel is going to be delivered to.
    //     * @param  int  $fromCountryId  The country ID where the parcel is going to be picked up from.
    //     * @param  float  $dimensionsLength  The length of the parcel.
    //     * @param  float  $dimensionsWeight  The weight of the parcel.
    //     * @param  float  $dimensionsWidth  The width of the parcel.
    //     * @param  string  $dimensionsUnit  The unit of the dimensions (e.g., METRIC).
    //     * @param  string  $logisticType  The logistic type (e.g., REGULAR, REVERSE, FOOD_DELIVERY).
    //     * @param  int  $page  The page number of the results to retrieve.
    //     * @param  int  $size  The size of the page (max 200).
    //     * @return array The package price details, or an empty array if the request fails.
    //     *
    //     * @throws \Exception If there is an error during the API request, an empty array will be returned.
    //     */
    //    public function getPackagePrices(
    //        float $fromLatitude,
    //        float $fromLongitude,
    //        float $toLatitude,
    //        float $toLongitude,
    //        int $toCountryId,
    //        int $fromCountryId,
    //        float $dimensionsLength,
    //        float $dimensionsWeight,
    //        float $dimensionsWidth,
    //        string $dimensionsUnit = 'METRIC',
    //        string $logisticType = 'REGULAR',
    //        int $page = 0,
    //        int $size = 20,
    //    ): array {
    //        try {
    //            $params = [
    //                'from_latitude' => $fromLatitude,
    //                'from_longitude' => $fromLongitude,
    //                'to_latitude' => $toLatitude,
    //                'to_longitude' => $toLongitude,
    //                'to_country_id' => $toCountryId,
    //                'from_country_id' => $fromCountryId,
    //                'dimensions_length' => $dimensionsLength,
    //                'dimensions_weight' => $dimensionsWeight,
    //                'dimensions_width' => $dimensionsWidth,
    //                'dimensions_unit' => $dimensionsUnit,
    //                'logistic_type' => $logisticType,
    //                'page' => $page,
    //                'size' => $size,
    //            ];
    //
    //            $response = Http::withHeaders($this->headers())
    //                ->get($this->baseUrl.'/v2/customer/packages/prices/starting_from', $params);
    //
    //            if ($response->successful()) {
    //                return $response->json()['data']['list'];
    //            }
    //
    //            return [];
    //
    //        } catch (\Exception $e) {
    //            return [];
    //        }
    //    }

    /**
     *  Create a shipment order using the Althabit API (v2).
     *
     * @throws ConnectionException
     */
    public function createShipment(Order $order): ShipmentRequestDto
    {
        if (! $this->token) {
            $this->login();
        }
        $endpoint = '/v3/customer/order';

        $description = '';
        foreach ($order->items as $item) {
            $description .= $item->name.$item->sku.', ';
        }

        $countryId = 191;

        //        $packageTypes = $this->getPackageTypes();
        //        if (empty($packageTypes)) {
        //            return ['error' => 'No package types found'];
        //        }
        //
        //        $packageType = $packageTypes[0];

        //        $packagePrices = $this->getPackagePrices(
        //            $order->shipper_latitude,
        //            $order->shipper_longitude,
        //            $order->receiver_latitude,
        //            $order->receiver_longitude,
        //            $countryId,
        //            $countryId,
        //            1,
        //            $order->shipment_total_weight,
        //            1
        //        );
        //
        //        if (empty($packagePrices)) {
        //            return ['error' => 'No package prices found'];
        //        }
        //
        //        $packagePrice = $packagePrices[0];
        // dd($order->receiver_city_id->name);
        $shipmentData = [
            'sender_data' => [
                'address_type' => 'business',
                'name' => $order->shipper_name,
                'email' => $order->shipper_email,
                'street' => $order->shipper_address_line,
                'building' => $order->warehouse->building_no_name ?? '',
                'city' => [
                    'name' => $order->shipper_city,
                ],
                'country' => [
                    'id' => $countryId,
                ],
                'phone' => $order->shipper_phone,
            ],
            'recipient_data' => [
                'address_type' => 'residential',
                'name' => $order->receiver_first_name.' '.$order->receiver_last_name,
                'street' => $order->receiver_address_line,
                'building' => $order->receiver_block ?? '',
                'email' => $order->receiver_email ?? '',
                'city' => [
                    'name' => $order->getReceiverCityWithFallback(CourierIdentifierEnum::THABIT->value),
                ],
                'phone' => $order->receiver_phone,
            ],
            'dimensions' => [
                'weight' => $order->getMaxWeight(),
            ],
            'package_type' => [
                'id' => 731630277,
                'package_price' => [
                    'id' => 2074910090,
                ],
            ],
            'charge_items' => [
                [
                    'charge' => $order->order_grand_total,
                    'charge_type' => $order->isCod() ? ChargeTypeEnum::COD->value : ChargeTypeEnum::SERVICE_CUSTOM->value,
                    'payer' => PayerEnum::SENDER->value,
                ],
            ],
            'recipient_not_available' => 'do_not_deliver',
            'payment_type' => $order->isCod() ? 'cash' : 'credit_balance',
            'parcel_value' => $order->order_grand_total,
            'payer' => PayerEnum::SENDER->value,
            'force_create' => true,
            'reference_id' => $order->order_number,
            'boxes' => $order->boxes()->get()->map(function ($box) {
                return [
                    'height' => $box->height,
                    'length' => $box->length,
                    'width' => $box->width,
                    'weight' => $box->weight,
                ];
            })->toArray(),
        ];

        $url = $this->baseUrl.$endpoint;
        $headers = $this->headers();

        return ShipmentRequestDto::create($url, $shipmentData, $headers);
    }

    public function trackShipments(array $trackingNumbers): array
    {
        return [];
    }

    /**
     * @return array<mixed>
     *
     * @throws ConnectionException|RequestException
     */
    public function trackShipment(string $trackingNumber): array
    {
        if (! $this->token) {
            $this->login();
        }
        $response = Http::withHeaders($this->headers())->get($this->baseUrl."/v1/public/order/$trackingNumber/history_items");

        return $response->json();
    }

    public function printLabel(Order $order): ShipmentRequestDto
    {
        if (! $this->token) {
            $this->login();
        }

        $url = $this->baseUrl.'/v1/customer/orders/airwaybill_mini?ids='.$order->shipment_external_id;
        $headers = $this->headers();

        return ShipmentRequestDto::create($url, [], $headers, 'GET');
    }

    public function extractPrintLabelUrl($response, ?Order $order = null): string
    {
        // Handle JSON response array
        if (is_array($response) && ! empty($response['data']['value'])) {
            return $response['data']['value'];
        }

        $jsonMessage = json_encode([
            'error' => 'Label URL not found in Thabit response',
            'response_type' => gettype($response),
            'response_data' => $response,
            'order_id' => $order ? $order->id : 'unknown',
        ], JSON_UNESCAPED_UNICODE);
        if ($jsonMessage === false) {
            throw new \Exception('JSON encode failed: '.json_last_error_msg());
        }
        throw new \Exception($jsonMessage);
    }

    public function createReverseShipment(Order $order): array
    {

        if (! $this->token) {
            $this->login();
        }
        $endpoint = '/v3/customer/order';

        $description = '';
        foreach ($order->items as $item) {
            $description .= $item->name.$item->sku.', ';
        }

        $countryId = 191;

        $shipmentData = [
            'sender_data' => [
                'address_type' => 'residential',
                'name' => $order->receiver_first_name.' '.$order->receiver_last_name,
                'email' => $order->receiver_email,
                'street' => $order->receiver_address_line,
                'building' => $order->receiver_block ?? '',
                'city' => [
                    'name' => $order->getReceiverCityWithFallback(CourierIdentifierEnum::THABIT->value),
                ],
                'country' => [
                    'id' => $countryId,
                ],
                'phone' => $order->receiver_phone,
            ],
            'recipient_data' => [
                'address_type' => 'business',
                'name' => $order->shipper_name,
                'street' => $order->shipper_address_line,
                'email' => $order->shipper_email,
                'building' => $order->warehouse->building_no_name ?? '',
                'city' => [
                    'name' => $order->shipper_city,
                ],
                'phone' => $order->shipper_phone,
            ],
            'dimensions' => [
                'weight' => $order->getMaxWeight(),
            ],
            'package_type' => [
                'id' => 744975193,
                'package_price' => [
                    'id' => 2074910272,
                ],
            ],
            'charge_items' => [
                [
                    'charge' => $order->order_grand_total,
                    'charge_type' => ChargeTypeEnum::SERVICE_CUSTOM->value,
                    'paid' => true,
                    'payer' => PayerEnum::SENDER->value,
                ],
            ],
            'recipient_not_available' => 'do_not_deliver',
            'payment_type' => 'credit_balance',
            'parcel_value' => $order->order_grand_total,
            'payer' => PayerEnum::SENDER->value,
            'force_create' => true,
            'reference_id' => $order->order_number,
            'boxes' => $order->boxes()->get()->map(function ($box) {
                return [
                    'height' => $box->height,
                    'length' => $box->length,
                    'width' => $box->width,
                    'weight' => $box->weight,
                ];
            })->toArray(),
        ];
        $response = Http::withHeaders($this->headers())->post($this->baseUrl.$endpoint, $shipmentData);

        return $response->json();
    }

    /**
     * Calculate the shipping cost for the given order.
     *
     * @return int Calculated shipping cost
     */
    public function getRates(ShippingRateDto $shippingRateDto): int
    {
        if ($shippingRateDto->isInternational) {
            throw new \Exception('International shipping rates are not supported');
        }

        // Get pricing configuration from factory
        $factory = app(ShippingServiceFactory::class);
        $pricingConfig = $factory->getPricingConfig(
            CourierIdentifierEnum::THABIT->value,
            $shippingRateDto
        );

        return $this->calculateRateFromConfig($pricingConfig, $shippingRateDto);
    }

    /**
     * Calculate shipping cost from configuration data for rates
     */
    private function calculateRateFromConfig(PricingConfigDto $pricingConfig, ShippingRateDto $shippingRateDto): int
    {
        // Calculate total weight from order boxes
        $totalWeight = ($shippingRateDto->calculateWeightFromBoxes($pricingConfig->volumetricDivisor) * 100);

        $basePrice = $pricingConfig->basePrice;
        $additionalWeightCost = 0;
        $distanceCost = 0;

        // Calculate additional weight cost
        if ($totalWeight > $pricingConfig->extraWeightFrom) {
            $additionalWeight = $totalWeight - $pricingConfig->extraWeightFrom;
            $additionalWeightCost = ($additionalWeight / 100) * $pricingConfig->additionalWeightCost;
        }

        // Add cash on delivery cost if applicable
        if ($shippingRateDto->isCod && ! $shippingRateDto->ignoreCod) {
            $basePrice = $basePrice + $pricingConfig->cashOnDeliveryCost;
        }

        // Calculate distance cost if applicable
        if ($pricingConfig->distanceCost > 0) {
            // TODO: Implement distance calculation logic
            // $distanceCost = $this->calculateDistanceCost($shippingRateDto, $pricingConfig->distanceCost);
        }

        // Calculate the total cost
        return $basePrice + $additionalWeightCost + $distanceCost;
    }

    /**
     * @return array|string[]
     *
     * @throws ConnectionException
     * @throws RequestException
     *                          TODO: THIS shipment reference is different of original signature which is orderNumber
     */
    public function cancelShipment(string $shipmentExternalId): array
    {
        if (! $this->token) {
            $this->login();
        }
        $response = Http::withHeaders($this->headers())->put($this->baseUrl."/v1/customer/order/$shipmentExternalId/status?status=cancelled");

        return $response->json();
    }

    /**
     * Calculate the shipping cost for the given order.
     *
     * @return int Calculated shipping cost
     */
    public function getCosts(ShippingRateDto $shippingRateDto): int
    {
        if ($shippingRateDto->isInternational) {
            // TODO: FINISH THIS LATER
            return 999999999999;
        }

        // Get pricing configuration from factory
        $factory = app(ShippingServiceFactory::class);
        $pricingConfig = $factory->getCostPricingConfig(
            CourierIdentifierEnum::THABIT->value,
            $shippingRateDto
        );

        return $this->calculateCostFromConfig($pricingConfig, $shippingRateDto);
    }

    /**
     * Calculate shipping cost from configuration data for costs
     */
    private function calculateCostFromConfig(PricingConfigDto $pricingConfig, ShippingRateDto $shippingRateDto): int
    {
        // Calculate total weight from order boxes
        $totalWeight = ($shippingRateDto->calculateWeightFromBoxes($pricingConfig->volumetricDivisor) * 100);

        $basePrice = $pricingConfig->basePrice;
        $additionalWeightCost = 0;

        // Calculate additional weight cost
        if ($totalWeight > $pricingConfig->extraWeightFrom) {
            $additionalWeight = $totalWeight - $pricingConfig->extraWeightFrom;
            $additionalWeightCost = ($additionalWeight / 100) * $pricingConfig->additionalWeightCost;
        }

        // Add cash on delivery cost if applicable (VALUE type)
        if ($shippingRateDto->isCod && ! $shippingRateDto->ignoreCod && $pricingConfig->codType === CostCodTypeEnum::VALUE->value) {
            $basePrice = $basePrice + $pricingConfig->cashOnDeliveryCost;
        }

        // Add cash on delivery cost if applicable (PERCENTAGE type)
        if ($shippingRateDto->isCod && ! $shippingRateDto->ignoreCod && $pricingConfig->codType === CostCodTypeEnum::PERCENTAGE->value) {
            $codValue = (int) (($basePrice * $pricingConfig->cashOnDeliveryCost) / 100);
            $basePrice = $basePrice + $codValue;
        }

        // Add additional weight cost to base price
        $basePrice = $basePrice + $additionalWeightCost;

        // Calculate fuel cost
        $fuelCost = ($basePrice * $pricingConfig->fuel) / 100;

        // Calculate the total cost
        return $basePrice + $fuelCost;
    }
}
