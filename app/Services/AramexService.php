<?php

namespace App\Services;

use App\Dto\PricingConfigDto;
use App\Dto\ShipmentRequestDto;
use App\Dto\ShippingRateDto;
use App\Enums\CostCodTypeEnum;
use App\Enums\CourierIdentifierEnum;
use App\Interfaces\ShipmentCourierInterface;
use App\Models\Country;
use App\Models\Order;
use App\Models\ShipmentCountriesPrice;
use App\Models\ShipmentCourierService;
use Illuminate\Http\Client\ConnectionException;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Http;

class AramexService implements ShipmentCourierInterface
{
    protected string $baseUrl = 'https://ws.aramex.net/ShippingAPI.V2/';

    const string SHIPMENT_TRACKING_URL = 'https://www.aramex.com/sa/ar/track/results?ShipmentNumber=';

    /**
     * @var array<string, string|int>
     */
    protected array $clientInfo;

    /**
     * @var string[]
     */
    protected array $config = [];

    /**
     * @param  array<string, int|string|null>  $config
     */
    public function setConfig(array $config): void
    {
        $this->clientInfo = [
            'UserName' => $config['username'] ?? $this->clientInfo['UserName'] ?? null,
            'Password' => $config['password'] ?? $this->clientInfo['Password'] ?? null,
            'Version' => $config['Version'] ?? $this->clientInfo['Version'] ?? 'v1',
            'AccountNumber' => $config['account_number'] ?? $this->clientInfo['AccountNumber'] ?? null,
            'AccountPin' => $config['account_pin'] ?? $this->clientInfo['AccountPin'] ?? null,
            'AccountEntity' => 'JED',
            'AccountCountryCode' => $config['AccountCountryCode'] ?? $this->clientInfo['AccountCountryCode'] ?? 'SA',
            'Source' => 24,
        ];
    }

    /**
     * @throws ConnectionException
     */
    public function createShipment(Order $order): ShipmentRequestDto
    {

        $description = '';
        foreach ($order->items as $item) {
            $description .= $item->name.$item->sku.', ';
        }
        $phone = $order->shipper_phone;
        $receiverPhone = $order->receiver_phone;
        $receiverPhone = str_replace('+', '', $receiverPhone);
        $cod = null;
        if ($order->isCod()) {
            $cod = [
                'CurrencyCode' => 'SAR',
                'Value' => $order->order_grand_total,
            ];
        }
        $shipmentData = [
            'LabelInfo' => null,
            'Shipments' => [
                [
                    'Reference1' => $order->order_number,
                    'Reference2' => '',
                    'Reference3' => '',
                    'Shipper' => [
                        'Reference1' => $order->order_number,
                        'Reference2' => '',
                        'AccountNumber' => $this->clientInfo['AccountNumber'],
                        'PartyAddress' => [
                            'Line1' => $order->shipper_address_line,
                            'Line2' => '',
                            'Line3' => '',
                            'City' => $order->shipper_city,
                            'StateOrProvinceCode' => '',
                            'PostCode' => '',
                            'CountryCode' => $order->shipperCountry->code_country,
                            'Longitude' => 0,
                            'Latitude' => 0,
                            'BuildingNumber' => null,
                            'BuildingName' => null,
                            'Floor' => null,
                            'Apartment' => null,
                            'POBox' => null,
                            'Description' => '',
                        ],
                        'Contact' => [
                            'Department' => '',
                            'PersonName' => $order->shipper_name,
                            'Title' => '',
                            'CompanyName' => 'متجر',
                            'PhoneNumber1' => $phone,
                            'PhoneNumber1Ext' => '',
                            'PhoneNumber2' => '',
                            'PhoneNumber2Ext' => '',
                            'FaxNumber' => '',
                            'CellPhone' => $phone,
                            'EmailAddress' => '',
                            'Type' => '',
                        ],
                    ],
                    'Consignee' => [
                        'Reference1' => $order->order_number,
                        'Reference2' => $order->order_number,
                        'AccountNumber' => $this->clientInfo['AccountNumber'],
                        'PartyAddress' => [
                            'Line1' => $order->receiver_address_line,
                            'Line2' => '',
                            'Line3' => '',
                            'City' => $order->getReceiverCityWithFallback(CourierIdentifierEnum::ARAMEX->value),
                            'StateOrProvinceCode' => '',
                            'PostCode' => '',
                            'CountryCode' => $order->receiverCountry->code_country ?? $order->receiver_country_code,
                            'Longitude' => 0,
                            'Latitude' => 0,
                            'BuildingNumber' => '',
                            'BuildingName' => '',
                            'Floor' => '',
                            'Apartment' => '',
                            'POBox' => null,
                            'Description' => 'box test',
                        ],
                        'Contact' => [
                            'Department' => '',
                            'PersonName' => $order->receiver_last_name,
                            'Title' => '',
                            'CompanyName' => $order->receiver_first_name,
                            'PhoneNumber1' => $receiverPhone,
                            'PhoneNumber1Ext' => '',
                            'PhoneNumber2' => '',
                            'PhoneNumber2Ext' => '',
                            'FaxNumber' => '',
                            'CellPhone' => $receiverPhone,
                            'EmailAddress' => $order->receiver_email,
                            'Type' => '',
                        ],
                    ],
                    'ThirdParty' => [
                        'Reference1' => $order->order_number,
                        'Reference2' => $order->order_number,
                        'AccountNumber' => $this->clientInfo['AccountNumber'],
                        'PartyAddress' => [
                            'Line1' => $order->shipper_address_line,
                            'Line2' => '',
                            'Line3' => '',
                            'City' => $order->shipper_city,
                            'StateOrProvinceCode' => '',
                            'PostCode' => '',
                            'CountryCode' => $order->shipperCountry->code_country,
                            'Longitude' => 0,
                            'Latitude' => 0,
                            'BuildingNumber' => null,
                            'BuildingName' => null,
                            'Floor' => null,
                            'Apartment' => null,
                            'POBox' => null,
                            'Description' => null,
                        ],
                        'Contact' => [
                            'Department' => '',
                            'PersonName' => $order->shipper_name,
                            'Title' => $order->shipper_name,
                            'CompanyName' => $order->shipper_name,
                            'PhoneNumber1' => $phone,
                            'PhoneNumber1Ext' => '',
                            'PhoneNumber2' => $phone,
                            'PhoneNumber2Ext' => '',
                            'FaxNumber' => '',
                            'CellPhone' => $phone,
                            'EmailAddress' => '<EMAIL>',
                            'Type' => '',
                        ],
                    ],
                    'ShippingDateTime' => '/Date('.((int) Carbon::now()->timestamp * 1000).'+0300)/',
                    'DueDate' => '/Date('.((int) Carbon::now()->addWeek()->timestamp * 1000).'+0300)/',
                    'Comments' => $description,
                    'PickupLocation' => '',
                    'OperationsInstructions' => '',
                    'AccountingInstrcutions' => '',
                    'Details' => [
                        'Dimensions' => null,
                        'ActualWeight' => [
                            'Unit' => 'KG',
                            'Value' => $order->getMaxWeight() > 0 ? $order->getMaxWeight() : 1,
                        ],
                        'ChargeableWeight' => null,
                        'DescriptionOfGoods' => $description,
                        'GoodsOriginCountry' => 'SA',
                        'NumberOfPieces' => $order->boxes()->count() > 0 ? $order->boxes()->count() : 1,
                        'ProductGroup' => $order->isInternational() ? 'EXP' : 'DOM',
                        'ProductType' => $order->isInternational() ? 'GPX' : 'ONP',
                        'PaymentType' => $order->isSameCity() ? 'P' : '3',
                        'PaymentOptions' => '',
                        'CustomsValueAmount' => $order->isInternational() ?
                            [
                                'CurrencyCode' => 'SAR',
                                'Value' => $order->order_grand_total,
                            ] : null,
                        'CashOnDeliveryAmount' => $cod,
                        'InsuranceAmount' => null,
                        'CashAdditionalAmount' => null,
                        'CashAdditionalAmountDescription' => '',
                        'CollectAmount' => null,
                        'Services' => '',
                        'Items' => [],
                    ],
                    'Attachments' => [],
                    'ForeignHAWB' => '',
                    'TransportType ' => 0,
                    'PickupGUID' => '',
                    'Number' => null,
                    'ScheduledDelivery' => null,
                ],
            ],
            'Transaction' => [
                'Reference1' => '',
                'Reference2' => '',
                'Reference3' => '',
                'Reference4' => '',
                'Reference5' => '',
            ],
        ];
        $url = $this->baseUrl.'Shipping/Service_1_0.svc/json/CreateShipments';
        // Combine ClientInfo with shipmentData
        $requestData = array_merge(['ClientInfo' => $this->clientInfo], $shipmentData);
        $headers = [
            'Content-Type' => 'application/json',
            'Accept' => 'application/json',
        ];

        return ShipmentRequestDto::create($url, $requestData, $headers);
    }

    /**
     * @param  array<int,string>  $trackingNumbers
     *
     * @throws ConnectionException
     */
    public function trackShipments(array $trackingNumbers): array
    {
        $url = $this->baseUrl.'Tracking/Service_1_0.svc/json/TrackShipments';

        $requestData = [
            'ClientInfo' => $this->clientInfo,
            'GetLastTrackingUpdateOnly' => false,
            'Shipments' => $trackingNumbers,
            'Transaction' => [
                'Reference1' => '',
                'Reference2' => '',
                'Reference3' => '',
                'Reference4' => '',
                'Reference5' => '',
            ],
        ];

        $response = Http::withHeaders([
            'Content-Type' => 'application/json',
            'Accept' => 'application/json',
        ])->post($url, $requestData);

        return $response->json();
    }

    public function printLabel(Order $order): ShipmentRequestDto
    {
        $url = $this->baseUrl.'Shipping/Service_1_0.svc/json/PrintLabel';
        $headers = [
            'accept' => 'application/json',
            'content-type' => 'application/json; charset=UTF-8',
        ];

        $data = [
            'LabelInfo' => [
                'ReportID' => 9729,
                'ReportType' => 'URL',
            ],
            'ShipmentNumber' => $order->shipment_reference,
        ];
        $requestData = array_merge(['ClientInfo' => $this->clientInfo], $data);

        return ShipmentRequestDto::create($url, $requestData, $headers);
    }

    public function extractPrintLabelUrl($response, ?Order $order = null): string
    {
        // Handle JSON response array
        if (is_array($response) && ! empty($response['ShipmentLabel']['LabelURL'])) {
            return $response['ShipmentLabel']['LabelURL'];
        }

        throw new \Exception('Label URL not found in the response.');
    }

    /**
     * Calculate the shipping cost for the given order.
     *
     * @return int Calculated shipping cost
     */
    public function getRates(ShippingRateDto $shippingRateDto): int
    {
        if ($shippingRateDto->isInternational) {
            return $this->getInternationalRates(weight: $shippingRateDto->calculateWeightFromBoxes(app(SettingsService::class)->getVolumetricDivisor()), receiverCountryCode: $shippingRateDto->receiverCountryCode);
        }

        // Get pricing configuration from factory
        $factory = app(ShippingServiceFactory::class);
        $pricingConfig = $factory->getPricingConfig(
            CourierIdentifierEnum::ARAMEX->value,
            $shippingRateDto
        );

        return $this->calculateRateFromConfig($pricingConfig, $shippingRateDto);
    }

    /**
     * Calculate shipping cost from configuration data for rates
     */
    private function calculateRateFromConfig(PricingConfigDto $pricingConfig, ShippingRateDto $shippingRateDto): int
    {
        // Calculate total weight from order boxes
        $totalWeight = ($shippingRateDto->calculateWeightFromBoxes($pricingConfig->volumetricDivisor) * 100);
        $basePrice = $pricingConfig->basePrice;
        $additionalWeightCost = 0;
        $distanceCost = 0;

        // Calculate additional weight cost
        if ($totalWeight > $pricingConfig->extraWeightFrom) {
            $additionalWeight = $totalWeight - $pricingConfig->extraWeightFrom;
            $additionalWeightCost = ($additionalWeight / 100) * $pricingConfig->additionalWeightCost;
        }

        // Add cash on delivery cost if applicable
        if ($shippingRateDto->isCod && ! $shippingRateDto->ignoreCod) {
            $basePrice = $basePrice + $pricingConfig->cashOnDeliveryCost;
        }

        // Calculate distance cost if applicable
        if ($pricingConfig->distanceCost > 0) {
            // TODO: Implement distance calculation logic
            // $distanceCost = $this->calculateDistanceCost($shippingRateDto, $pricingConfig->distanceCost);
        }

        // Calculate the total cost
        return $basePrice + $additionalWeightCost + $distanceCost;
    }

    /**
     * Calculate the shipping cost for the given order.
     *
     * @return int Calculated shipping cost
     */
    public function getCosts(ShippingRateDto $shippingRateDto): int
    {
        if ($shippingRateDto->isInternational) {
            // TODO: FINISH THIS LATER
            return $this->getInternationalCosts(weight: $shippingRateDto->calculateWeightFromBoxes(app(SettingsService::class)->getVolumetricDivisor()), receiverCountryCode: $shippingRateDto->receiverCountryCode);
        }

        // Fall back to global pricing
        $service = ShipmentCourierService::where('identifier', CourierIdentifierEnum::ARAMEX->value)->firstOrFail();
        // Calculate total weight from order boxes
        $totalWeight = ($shippingRateDto->calculateWeightFromBoxes($service->getVolumetricDivisor()) * 100);
        $basePrice = $service->cost_base_price;
        $additionalWeightCost = 0;
        // Calculate additional weight cost
        if ($totalWeight > $service->cost_extra_weight_from) {
            $additionalWeight = $totalWeight - $service->cost_extra_weight_from;
            $additionalWeightCost = ($additionalWeight / 100) * $service->cost_additional_weight_cost;
        }

        if ($shippingRateDto->isCod && ! $shippingRateDto->ignoreCod && $service->cost_cod_type === CostCodTypeEnum::VALUE->value) {
            $basePrice = $basePrice + $service->cost_cash_on_delivery_cost;
        }
        if ($shippingRateDto->isCod && ! $shippingRateDto->ignoreCod && $service->cost_cod_type === CostCodTypeEnum::PERCENTAGE->value) {
            $codValue = (int) (($basePrice * $service->cost_cash_on_delivery_cost) / 100);
            $basePrice = $basePrice + $codValue;
        }
        $basePrice = $basePrice + $additionalWeightCost;
        $fuelCost = ($basePrice * $service->cost_fuel) / 100;

        // Calculate the total cost
        return $basePrice + $fuelCost;
    }

    public function createReverseShipment(Order $order): array
    {
        $description = '';
        foreach ($order->items as $item) {
            $description .= $item->name.$item->sku.', ';
        }
        $phone = $order->receiver_phone;
        $cod = null;
        $shipmentData = [
            'LabelInfo' => null,
            'Shipments' => [
                [
                    'Reference1' => $order->order_number.'-R',
                    'Reference2' => '',
                    'Reference3' => '',
                    'Shipper' => [
                        'Reference1' => $order->order_number.'-R',
                        'Reference2' => '',
                        'AccountNumber' => $this->clientInfo['AccountNumber'],
                        'PartyAddress' => [
                            'Line1' => $order->receiver_address_line,
                            'Line2' => $phone,
                            'Line3' => $phone,
                            'City' => $order->getReceiverCityWithFallback(CourierIdentifierEnum::ARAMEX->value),
                            'StateOrProvinceCode' => '',
                            'PostCode' => '',
                            'CountryCode' => $order->receiverCountry->code_country ?? $order->receiver_country_code,
                            'Longitude' => 0,
                            'Latitude' => 0,
                            'BuildingNumber' => null,
                            'BuildingName' => null,
                            'Floor' => null,
                            'Apartment' => null,
                            'POBox' => null,
                            'Description' => $phone,
                        ],
                        'Contact' => [
                            'Department' => $phone,
                            'PersonName' => $order->receiver_first_name.' '.$order->receiver_last_name,
                            'Title' => $order->receiver_first_name,
                            'CompanyName' => $order->receiver_address_line,
                            'PhoneNumber1' => $phone,
                            'PhoneNumber1Ext' => '',
                            'PhoneNumber2' => '',
                            'PhoneNumber2Ext' => '',
                            'FaxNumber' => '',
                            'CellPhone' => $phone,
                            'EmailAddress' => '',
                            'Type' => '',
                        ],
                    ],
                    'Consignee' => [
                        'Reference1' => $order->order_number,
                        'Reference2' => $order->order_number,
                        'AccountNumber' => $this->clientInfo['AccountNumber'],
                        'PartyAddress' => [
                            'Line1' => $order->shipper_address_line,
                            'Line2' => '',
                            'Line3' => '',
                            'City' => $order->shipper_city,
                            'StateOrProvinceCode' => '',
                            'PostCode' => '',
                            'CountryCode' => $order->shipperCountry->code_country,
                            'Longitude' => 0,
                            'Latitude' => 0,
                            'BuildingNumber' => '',
                            'BuildingName' => '',
                            'Floor' => '',
                            'Apartment' => '',
                            'POBox' => null,
                            'Description' => 'box test',
                        ],
                        'Contact' => [
                            'Department' => '',
                            'PersonName' => $order->shipper_name,
                            'Title' => '',
                            'CompanyName' => 'متجر',
                            'PhoneNumber1' => $order->shipper_phone,
                            'PhoneNumber1Ext' => '',
                            'PhoneNumber2' => '',
                            'PhoneNumber2Ext' => '',
                            'FaxNumber' => '',
                            'CellPhone' => '966'.$order->shipper_phone,
                            'EmailAddress' => $order->shipper_email,
                            'Type' => '',
                        ],
                    ],
                    'ThirdParty' => [
                        'Reference1' => $order->order_number,
                        'Reference2' => $order->order_number,
                        'AccountNumber' => $this->clientInfo['AccountNumber'],
                        'PartyAddress' => [
                            'Line1' => $order->shipper_address_line,
                            'Line2' => '',
                            'Line3' => '',
                            'City' => $order->shipper_city,
                            'StateOrProvinceCode' => '',
                            'PostCode' => '',
                            'CountryCode' => $order->receiverCountry->code_country ?? $order->receiver_country_code,
                            'Longitude' => 0,
                            'Latitude' => 0,
                            'BuildingNumber' => null,
                            'BuildingName' => null,
                            'Floor' => null,
                            'Apartment' => null,
                            'POBox' => null,
                            'Description' => 'Return to shipper',
                        ],
                        'Contact' => [
                            'Department' => '',
                            'PersonName' => $order->shipper_name,
                            'Title' => '',
                            'CompanyName' => $order->shipper_address_line,
                            'PhoneNumber1' => $phone,
                            'PhoneNumber1Ext' => '',
                            'PhoneNumber2' => $phone,
                            'PhoneNumber2Ext' => '',
                            'FaxNumber' => '',
                            'CellPhone' => $phone,
                            'EmailAddress' => '',
                            'Type' => '',
                        ],
                    ],
                    'ShippingDateTime' => '/Date('.((int) Carbon::now()->timestamp * 1000).'+0300)/',
                    'DueDate' => '/Date('.((int) Carbon::now()->addWeek()->timestamp * 1000).'+0300)/',
                    'Comments' => $description,
                    'PickupLocation' => '',
                    'OperationsInstructions' => '',
                    'AccountingInstrcutions' => '',
                    'Details' => [
                        'Dimensions' => null,
                        'ActualWeight' => [
                            'Unit' => 'KG',
                            'Value' => $order->getMaxWeight() > 0 ? $order->getMaxWeight() : 1,
                        ],
                        'ChargeableWeight' => null,
                        'DescriptionOfGoods' => $description,
                        'GoodsOriginCountry' => 'SA',
                        'NumberOfPieces' => 1,
                        'ProductGroup' => $order->isInternational() ? 'EXP' : 'DOM',
                        'ProductType' => $order->isInternational() ? 'GPX' : 'ONP',
                        'PaymentType' => $order->isSameCity() ? 'P' : '3',
                        'PaymentOptions' => '',
                        'CustomsValueAmount' => null,
                        'CashOnDeliveryAmount' => $cod,
                        'InsuranceAmount' => null,
                        'CashAdditionalAmount' => null,
                        'CashAdditionalAmountDescription' => '',
                        'CollectAmount' => null,
                        'Services' => '',
                        'Items' => [],
                    ],
                    'Attachments' => [],
                    'ForeignHAWB' => '',
                    'TransportType ' => 0,
                    'PickupGUID' => '',
                    'Number' => null,
                    'ScheduledDelivery' => null,
                ],
            ],
            'Transaction' => [
                'Reference1' => '',
                'Reference2' => '',
                'Reference3' => '',
                'Reference4' => '',
                'Reference5' => '',
            ],
        ];

        $url = $this->baseUrl.'Shipping/Service_1_0.svc/json/CreateShipments';
        // Combine ClientInfo with shipmentData
        $requestData = array_merge(['ClientInfo' => $this->clientInfo], $shipmentData);
        $response = Http::withHeaders([
            'Content-Type' => 'application/json',
            'Accept' => 'application/json',
        ])->post($url, $requestData);

        return $response->json();
    }

    private function getInternationalRates(int $weight, string $receiverCountryCode): int
    {
        $country = Country::where('code_country', $receiverCountryCode)->firstOrFail();
        $shipmentCountriesPrice = ShipmentCountriesPrice::where('country_id', $country->id)->firstOrFail();
        $cost = $shipmentCountriesPrice->initial_price;
        $remainingWeight = $weight - 1;
        if ($remainingWeight > 0) {
            $cost += $remainingWeight * $shipmentCountriesPrice->extra_weight_price;
        }

        return (int) $cost;
    }

    private function getInternationalCosts(int $weight, string $receiverCountryCode): int
    {
        $pricingTable = [
            'AE' => [29, 37, 49, 57, 65, 73, 81, 89, 97],
            'KW' => [36, 44, 57, 66, 75, 84, 93, 102, 111],
            'BH' => [33, 41, 54, 63, 72, 81, 90, 99, 108],
            'QA' => [33, 41, 54, 63, 72, 81, 90, 99, 108],
            'OM' => [33, 41, 54, 63, 72, 81, 90, 99, 108],
        ];

        $additionalHalfKgRate = [
            'AE' => 8,
            'KW' => 9,
            'BH' => 9,
            'QA' => 9,
            'OM' => 9,
        ];

        $country = strtoupper($receiverCountryCode);

        if (! isset($pricingTable[$country])) {
            throw new \InvalidArgumentException("No pricing found for country: $receiverCountryCode");
        }

        // Convert grams to kg (as float)
        $kgWeight = $weight / 1000;

        // Round up to the nearest 0.5 KG
        $roundedStep = ceil($kgWeight * 2) / 2;

        // Calculate index in pricing table
        $index = (int) (($roundedStep - 1) * 2);

        // Prevent negative index
        if ($index < 0) {
            \Log::warning("Weight too low ({$weight}g) for pricing table. Defaulting index to 0.");
            $index = 0;
        }

        $basePrices = $pricingTable[$country];

        if ($index < count($basePrices)) {
            return $basePrices[$index];
        }

        // Over 5KG — apply additional cost per 0.5 KG
        $extraSteps = $index - (count($basePrices) - 1);
        $baseCost = $basePrices[count($basePrices) - 1];
        $extraCost = $extraSteps * $additionalHalfKgRate[$country];

        return (int) ($baseCost + $extraCost);
    }

    public function cancelShipment(string $orderNumber): array
    {
        // TODO: Implement cancelShipment() method.
        throw new \Exception('Cancel shipment not implemented');
    }
}
