<?php

namespace App\Services;

use App\Dto\SallaOrderDto;
use App\Enums\OrderStatusEnum;
use App\Enums\SalesChannelEnum;
use App\Interfaces\EcommerceInterface;
use App\Models\ApiJob;
use App\Models\Merchant;
use App\Models\Order;
use App\Models\OrderItem;
use App\Models\Warehouse;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Traits\ForwardsCalls;
use League\OAuth2\Client\Provider\Exception\IdentityProviderException;
use League\OAuth2\Client\Provider\ResourceOwnerInterface;
use League\OAuth2\Client\Token\AccessToken;
use Salla\OAuth2\Client\Provider\Salla;
use Salla\OAuth2\Client\Provider\SallaUser;

/**
 * @mixin Salla
 */
class SallaAuthService implements EcommerceInterface
{
    use ForwardsCalls;

    /**
     * @var Salla
     */
    protected $provider;

    public Merchant $merchant;

    public function __construct()
    {
        $this->provider = new Salla([
            'clientId' => config('services.salla.client_id'), // The client ID assigned to you by Salla
            'clientSecret' => config('services.salla.client_secret'), // The client password assigned to you by Salla
            'redirectUri' => config('services.salla.redirect'), // the url for current page in your service
        ]);
    }

    /**
     * Get the token from the user model.
     *
     *
     * @return $this
     */
    public function forUser(Merchant $merchant)
    {
        $this->merchant = $merchant;

        return $this;
    }

    public function getProvider(): Salla
    {
        return $this->provider;
    }

    /**
     * Get the details of store for the current token.
     *
     *  {
     *      "id": 181690847,
     *      "name": "eman elsbay",
     *      "email": "<EMAIL>",
     *      "mobile": "555454545",
     *      "role": "user",
     *      "created_at": "2018-04-28 17:46:25",
     *      "store": {
     *        "id": 633170215,
     *        "owner_id": 181690847,
     *        "owner_name": "eman elsbay",
     *        "username": "good-store",
     *        "name": "متجر الموضة",
     *        "avatar": "https://cdn.salla.sa/XrXj/g2aYPGNvafLy0TUxWiFn7OqPkKCJFkJQz4Pw8WsS.jpeg",
     *        "store_location": "26.989000873354787,49.62477639657287",
     *        "plan": "special",
     *        "status": "active",
     *        "created_at": "2019-04-28 17:46:25"
     *      }
     *    }
     *
     * @return \League\OAuth2\Client\Provider\ResourceOwnerInterface|SallaUser
     */
    public function getStoreDetail()
    {
        return $this->provider->getResourceOwner(new AccessToken($this->merchant->toArray()));
    }

    /**
     * @return array<mixed>
     *
     * @throws IdentityProviderException
     */
    public function getOrders(): array
    {
        return $this->request('GET', 'https://api.salla.dev/admin/v2/orders')['data'];
    }

    /**
     * @return array<mixed>
     *
     * @throws IdentityProviderException
     */
    public function getOrderById(int $id): array
    {
        return $this->request('GET', 'https://api.salla.dev/admin/v2/orders/'.$id)['data'];
    }

    /**
     * @return array<mixed>
     *
     * @throws IdentityProviderException
     */
    public function getOrderByOrderNumber(int $orderNumber): array
    {
        $sallaOrderId = $this->request('GET', 'https://api.salla.dev/admin/v2/orders?reference_id='.$orderNumber)['data'][0]['id'];

        return $this->getOrderById($sallaOrderId);
    }

    /**
     * Get A new access token via refresh token.
     *
     *
     * @throws IdentityProviderException
     */
    public function getNewAccessToken(): void
    {
        if (! $this->merchant->hasExpired()) {
            Log::info('not expired');

            return;
            //
            //            return new AccessToken($this->merchant->toArray());
        }
        // let's request a new access token via refresh token.
        $token = $this->provider->fetchResource('POST', 'https://api.salla.dev/admin/v2/auth/refresh', $this->merchant->refresh_token);
        Log::info($token);
        // lets update user tokens
        $this->merchant->update([
            'access_token' => $token['data']['token'],
            'expires_in' => strtotime('-1 day', strtotime($token['data']['expire_at'])),
            'refresh_token' => $token['data']['refresh_token'],
        ]);

    }

    /**
     * @param  array<mixed>  $options
     *
     * @throws IdentityProviderException
     */
    public function request(string $method, string $url, array $options = []): mixed
    {
        // you need always to check the token before made a request
        // If the token expired, lets request a new one and save it to the database
        $this->getNewAccessToken();

        return $this->provider->fetchResource($method, $url, $this->merchant->access_token, $options);
    }

    /**
     * As shortcut to call the functions of provider class.
     *
     *
     * @param  array<mixed>  $arguments
     * @return mixed
     */
    public function __call(string $name, array $arguments)
    {
        return $this->forwardCallTo($this->provider, $name, $arguments);
    }

    /**
     * Determine if the authorization mode is easy
     */
    public function isEasyMode(): bool
    {
        return config('services.salla.authorization_mode') === 'easy';
    }

    /**
     * Requests and returns the resource owner of given access token.
     */
    public function getResourceOwner(?AccessToken $token): ResourceOwnerInterface
    {
        return $this->provider->getResourceOwner($token ?: new AccessToken($this->merchant->toArray()));
    }

    public function testConnection(): void
    {
        // TODO: Implement testConnection() method.
    }

    public function changeStatus(Order $order): void
    {
        $status = $this->getSallaStatus($order);
        $statusMessage = 'not handled status';
        if ($status === 'in_progress') {
            $statusMessage = 'Shipped by';
        } elseif ($status === 'delivered') {
            $statusMessage = 'Delivered by';
        } elseif ($status === 'shipped') {
            $statusMessage = 'In delivery by';
        } else {
            $jsonMessage = json_encode([
                'error' => 'Unexpected status',
                'order_id' => $order->id,
                'order_status' => $order->status,
            ], JSON_UNESCAPED_UNICODE);
            if ($jsonMessage === false) {
                throw new \Exception('JSON encode failed: '.json_last_error_msg());
            }
            throw new \Exception($jsonMessage);
        }
        $orderExternalId = $order->external_id;
        $shipmentReference = $order->shipment_reference;
        $trackingUrl = $order->shipment_tracking_link;
        // If App\Models\ApiJob does not exist, comment out or replace the call to ApiJob::create() with a TODO or appropriate logic.
        //        ApiJob::create([
        //            'event_type' => 'shipment_created',
        //            'endpoint' => "https://api.salla.dev/admin/v2/orders/$orderExternalId/status",
        //            'order_id' => $order->id,
        //            'payload' => json_encode(
        //                [
        //                    'slug' => $status,
        //                    'note' => "$statusMessage Aramex with tracking number $shipmentReference \n Tracking URL: $trackingUrl",
        //                ]
        //            ),
        //            'status' => 'pending',
        //        ]);
        //        TODO: The following code has been commented because of 401 issue on server
        //        $options = ['body' => json_encode(
        //            [
        //                'slug' => $status,
        //                'note' => " Treek- $statusMessage Aramex with tracking number $shipmentReference \n Tracking URL: $trackingUrl",
        //            ]
        //        ), 'headers' => [
        //            'content-type' => 'application/json',
        //        ]];
        //
        //        $this->request('POST', "https://api.salla.dev/admin/v2/orders/$orderExternalId/status", $options);
    }

    public function handleOrderCreated(SallaOrderDto $orderDto): void
    {
        $alreadyExist = Order::withoutGlobalScopes()->where('order_number', $orderDto->orderNumber)->exists();
        if ($alreadyExist) {
            Log::info('Order already Exist'.$orderDto->orderNumber);

            return;
        }
        $warehouse = $this->getWarehouse(Merchant::withoutGlobalScopes()->findOrFail($orderDto->merchantId), $orderDto);
        try {
            /** @var Order $createdOrder */
            $createdOrder = Order::create([
                'external_id' => $orderDto->externalId,
                'order_number' => $orderDto->orderNumber,
                'status' => $orderDto->status,
                'date' => $orderDto->date,
                'order_grand_total' => $orderDto->orderGrandTotal,
                'description' => $orderDto->description,
                'payment_method' => $orderDto->paymentMethod,
                'receiver_first_name' => $orderDto->receiverFirstName,
                'receiver_last_name' => $orderDto->receiverLastName,
                'receiver_phone' => phoneCorrector(receivedPhone: $orderDto->receiverPhone, correctWithCountry: $orderDto->receiverCountryId),
                'receiver_email' => $orderDto->receiverEmail,
                'receiver_country_id' => $orderDto->receiverCountryId,
                'receiver_country' => $orderDto->receiverCountry,
                'receiver_address_line' => $orderDto->receiverAddressLine,
                'receiver_city_id' => $orderDto->receiverCityId,
                'receiver_city' => $orderDto->receiverCity,
                'receiver_country_code' => $orderDto->receiverCountryCode,
                'receiver_postal_code' => $orderDto->receiverPostalCode,
                'receiver_latitude' => $orderDto->receiverLatitude,
                'receiver_longitude' => $orderDto->receiverLongitude,
                'warehouse_id' => $orderDto->warehouseId,
                'merchant_id' => $orderDto->merchantId,
                'webhook_id' => $orderDto->webhookId,
                'shipment_total_weight' => $orderDto->shipmentTotalWeight,
                'tax' => $orderDto->tax,
                'source' => SalesChannelEnum::SALLA->value,
                ...$warehouse,
            ]);
            $createdOrder->logOrderCreation();
            foreach ($orderDto->items as $itemDto) {
                OrderItem::create([
                    'order_id' => $createdOrder->id,
                    'name' => $itemDto->name,
                    'sku' => $itemDto->sku,
                    'quantity' => $itemDto->quantity,
                    'weight' => $itemDto->weight,
                    'weight_unit' => 'g',
                    'price' => $itemDto->price,
                    'total_price' => $itemDto->totalPrice,
                    'tax' => $itemDto->tax,
                ]);
            }

        } catch (\Exception $e) {
            // Log errors for debugging
            logger('Error creating order', [
                'error' => $e->getMessage(),
                'payload' => $orderDto,
            ]);
            throw $e;
        }
    }

    private function getSallaStatus(Order $order): string
    {
        return match ($order->status) {
            OrderStatusEnum::AWAITING_PICKUP->value => 'in_progress',
            OrderStatusEnum::CURRENTLY_SHIPPING->value => 'shipped',
            OrderStatusEnum::DELIVERED->value => 'delivered',
            default => 'in_progress',
        };
    }

    /**
     * @return array<string, int|string|null>
     */
    private function getWarehouse(Merchant $merchant, SallaOrderDto $sallaOrderDto): array
    {
        if ($merchant->warehouse_id) {
            $warehouse = Warehouse::withoutGlobalScopes()->findOrFail($merchant->warehouse_id);

            return [
                'shipper_phone' => $warehouse->sender_phone,
                'shipper_email' => $warehouse->sender_email,
                'shipper_name' => $warehouse->name,
                'shipper_city' => $warehouse->city?->name,
                'shipper_address_line' => $warehouse->address,
                'shipper_latitude' => 0,
                'shipper_longitude' => 0,
            ];
        }

        return [
            'shipper_phone' => $sallaOrderDto->shipperPhone,
            'shipper_name' => $sallaOrderDto->shipperName,
            'shipper_city' => $sallaOrderDto->shipperCity,
            'shipper_address_line' => $sallaOrderDto->shipperAddressLine,
            'shipper_latitude' => $sallaOrderDto->shipperLatitude,
            'shipper_longitude' => $sallaOrderDto->shipperLongitude,
        ];
    }
}
