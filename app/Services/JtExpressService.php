<?php

namespace App\Services;

use App\Dto\PricingConfigDto;
use App\Dto\ShipmentRequestDto;
use App\Dto\ShippingRateDto;
use App\Enums\CostCodTypeEnum;
use App\Enums\CourierIdentifierEnum;
use App\Interfaces\ShipmentCourierInterface;
use App\Models\Order;
use App\Models\ShipmentCourierService;
use Illuminate\Http\Client\ConnectionException;
use Illuminate\Http\Client\RequestException;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Storage;

class JtExpressService implements ShipmentCourierInterface
{
    protected string $baseUrl = 'https://openapi.jtjms-sa.com/webopenplatformapi/api';

    const string SHIPMENT_TRACKING_URL = 'https://www.jtexpress.me/KSA/trajectoryQuery?waybillNo=';

    const string BUSINESS_DIGEST = '9lyxX1Sjf0V1trcJcPqeEQ==';

    /**
     * @var array<string, string|int>
     */
    protected array $clientInfo;

    protected string $secretKey = 'abd45c75003044d1bfeafcb2e711df23';

    /**
     * @var string[]
     */
    protected array $config = [];

    /**
     * @param  array<mixed>  $post
     */
    private function getHeaderDigest(array $post): string
    {
        return base64_encode(pack('H*', strtoupper(md5(json_encode($post).$this->secretKey))));
    }

    //    private function getContentDigest(): string
    //    {
    //        $str = strtoupper('J0086024173'.md5('R4tMZp15'.'jadada236t2')).'a0a1047cce70493c9d5d29704f05d0d9';
    //
    //        return base64_encode(pack('H*', strtoupper(md5($str))));
    //    }

    /**
     * @param  array<string, int|string|null>  $config
     */
    public function setConfig(array $config): void
    {
        $this->clientInfo = [
            'customerCode' => $config['customerCode'] ?? $this->clientInfo['customerCode'] ?? null,
            'apiAccount' => $config['apiAccount'] ?? $this->clientInfo['apiAccount'] ?? null,
        ];
    }

    /**
     * @throws ConnectionException
     */
    public function createShipment(Order $order): ShipmentRequestDto
    {
        $timestamp = (string) round(microtime(true) * 1000);
        $customerCode = $this->clientInfo['customerCode'];
        //        TODO: FIX ITEMS ARRAY
        $orderData = [
            //            'customerCode' => 'J0086024173',
            //            'digest' => '',
            'serviceType' => '01',
            'orderType' => '2',
            'deliveryType' => '04',
            'countryCode' => 'KSA',
            'receiver' => [
                'address' => $order->receiver_address_line,
                'street' => $order->receiver_street_name ?? '',
                'city' => $order->receiverCity->name,
                'area' => $order->receiverCity->name,
                'mobile' => $order->receiver_phone,
                'mailBox' => $order->receiver_email,
                'phone' => '',
                'countryCode' => $order->receiverCountry->code_country ?? $order->receiver_country_code,
                'name' => $order->receiver_first_name.' '.$order->receiver_last_name,
                'company' => $order->receiver_first_name.' '.$order->receiver_last_name,
                'postCode' => '',
                'prov' => $order->receiverCity->name,
            ],
            'expressType' => 'EZKSA',
            'length' => 0,
            'weight' => $order->getMaxWeight() > 0 ? $order->getMaxWeight() : 1,
            'remark' => '',
            'txlogisticId' => $order->order_number,
            'goodsType' => 'ITN1',
            'priceCurrency' => 'SAR',
            'totalQuantity' => $order->boxes()->count(),
            'sender' => [
                'address' => $order->shipper_address_line,
                'street' => $order->shipper_address_line,
                'city' => $order->shipper_city,
                'area' => $order->shipper_city,
                'mobile' => $order->shipper_phone,
                'mailBox' => $order->shipper_email,
                'phone' => '',
                'countryCode' => $order->shipperCountry->code_country,
                'name' => $order->shipper_name,
                'company' => $order->shipper_name,
                'postCode' => '',
                'prov' => $order->shipper_city,
            ],
            'itemsValue' => $order->isCod() ? $order->order_grand_total : 0,
            'merchNo' => $order->order_number,
            'offerFee' => 0,
            'items' => [],
            'operateType' => 1,
            'isUnpackEnabled' => 0,
        ];
        $orderData['digest'] = self::BUSINESS_DIGEST;
        $orderData['customerCode'] = $customerCode;
        // Generate header digest
        $headerDigest = $this->getHeaderDigest($orderData);

        $url = $this->baseUrl.'/order/addOrder';
        $headers = [
            'apiAccount' => (string) $this->clientInfo['apiAccount'],
            'digest' => $headerDigest,
            'timestamp' => $timestamp,
            'Content-Type' => 'application/x-www-form-urlencoded',
        ];
        $payload = ['bizContent' => json_encode($orderData)];

        return ShipmentRequestDto::create($url, $payload, $headers, 'POST', 'form');
    }

    /**
     * @param  array<int,string>  $trackingNumbers
     *
     * @throws ConnectionException
     */
    public function trackShipments(array $trackingNumbers): array
    {
        //        TODO: FIX this
        $url = $this->baseUrl.'Tracking/Service_1_0.svc/json/TrackShipments';

        $requestData = [
            'ClientInfo' => $this->clientInfo,
            'GetLastTrackingUpdateOnly' => false,
            'Shipments' => $trackingNumbers,
            'Transaction' => [
                'Reference1' => '',
                'Reference2' => '',
                'Reference3' => '',
                'Reference4' => '',
                'Reference5' => '',
            ],
        ];

        $response = Http::withHeaders([
            'Content-Type' => 'application/json',
            'Accept' => 'application/json',
        ])->post($url, $requestData);

        return $response->json();
    }

    /**
     * @return array<mixed>
     *
     * @throws ConnectionException|RequestException
     */
    public function trackShipment(string $trackingNumber): array
    {
        $timestamp = (string) round(microtime(true) * 1000);
        $customerCode = $this->clientInfo['customerCode'];
        $orderData['digest'] = self::BUSINESS_DIGEST;
        $orderData['customerCode'] = $customerCode;
        $orderData['billCodes'] = $trackingNumber;
        $headerDigest = $this->getHeaderDigest($orderData);
        $response = Http::withHeaders([
            'apiAccount' => $this->clientInfo['apiAccount'],
            'digest' => $headerDigest,
            'timestamp' => $timestamp,
            'Content-Type' => 'application/x-www-form-urlencoded',
        ])->asForm()->post($this->baseUrl.'/logistics/trace', [
            'bizContent' => json_encode($orderData),
        ]);

        return $response->json();
    }

    /**
     * @return array<mixed>
     *
     * @throws ConnectionException|RequestException
     */
    public function checkShipment(string $trackingNumber): array
    {
        $timestamp = (string) round(microtime(true) * 1000);
        $customerCode = $this->clientInfo['customerCode'];
        $orderData['digest'] = self::BUSINESS_DIGEST;
        $orderData['customerCode'] = $customerCode;
        $orderData['serialNumber'] = $trackingNumber;
        $orderData['command'] = 1;
        $headerDigest = $this->getHeaderDigest($orderData);

        $response = Http::withHeaders([
            'apiAccount' => $this->clientInfo['apiAccount'],
            'digest' => $headerDigest,
            'timestamp' => $timestamp,
            'Content-Type' => 'application/x-www-form-urlencoded',
        ])->asForm()->post($this->baseUrl.'/order/getOrders?uuid=8d1c9db974ff4c1489fe3fed34ba43bb', [
            'bizContent' => json_encode($orderData),
        ]);

        return $response->json();
    }

    /**
     * @return array<string, int|string>
     *
     * @throws ConnectionException
     */
    public function printLabelBase64(Order $order): array|string
    {
        sleep(3);
        $timestamp = (string) round(microtime(true) * 1000);
        $customerCode = $this->clientInfo['customerCode'];
        $orderData['digest'] = self::BUSINESS_DIGEST;
        $orderData['customerCode'] = $customerCode;
        $orderData['billCode'] = $order->shipment_reference;
        $headerDigest = $this->getHeaderDigest($orderData);

        $response = Http::withHeaders([
            'apiAccount' => $this->clientInfo['apiAccount'],
            'digest' => $headerDigest,
            'timestamp' => $timestamp,
            'Content-Type' => 'application/x-www-form-urlencoded',
        ])->asForm()->post($this->baseUrl.'/order/printOrder', [
            'bizContent' => json_encode($orderData),
        ]);

        if ($response->successful()) {
            $originalFileName = "airwaybill_{$order->shipment_reference}.pdf";

            Storage::disk('public')->put($originalFileName, $response->body());

            return Storage::disk('public')->url($originalFileName);
        }

        return '';

    }

    public function printLabel(Order $order): ShipmentRequestDto
    {
        $timestamp = (string) round(microtime(true) * 1000);
        $customerCode = $this->clientInfo['customerCode'];
        $orderData['digest'] = self::BUSINESS_DIGEST;
        $orderData['customerCode'] = $customerCode;
        $orderData['billCode'] = $order->shipment_reference;
        $headerDigest = $this->getHeaderDigest($orderData);

        $url = $this->baseUrl.'/order/printOrderUrl';
        $headers = [
            'apiAccount' => (string) $this->clientInfo['apiAccount'],
            'digest' => $headerDigest,
            'timestamp' => $timestamp,
            'Content-Type' => 'application/x-www-form-urlencoded',
        ];
        $payload = ['bizContent' => json_encode($orderData)];

        return ShipmentRequestDto::create($url, $payload, $headers, 'POST', 'form');
    }

    public function extractPrintLabelUrl($response, ?Order $order = null): string
    {
        // Handle JSON response array
        if (is_array($response) && ! empty($response['data'])) {
            return $response['data'];
        }

        $jsonMessage = json_encode([
            'error' => 'Label URL not found in JT Express response',
            'response_type' => gettype($response),
            'response_data' => $response,
            'order_id' => $order ? $order->id : 'unknown',
        ], JSON_UNESCAPED_UNICODE);
        if ($jsonMessage === false) {
            throw new \Exception('JSON encode failed: '.json_last_error_msg());
        }
        throw new \Exception($jsonMessage);
    }

    /**
     * Calculate the shipping cost for the given order.
     *
     * @return int Calculated shipping cost
     */
    public function getRates(ShippingRateDto $shippingRateDto): int
    {
        if ($shippingRateDto->isInternational) {
            $jsonMessage = json_encode([
                'error' => 'International shipping rates are not supported',
                'order_id' => $shippingRateDto->order_id ?? 'unknown',
                'is_international' => $shippingRateDto->isInternational,
            ], JSON_UNESCAPED_UNICODE);
            if ($jsonMessage === false) {
                throw new \Exception('JSON encode failed: '.json_last_error_msg());
            }
            throw new \Exception($jsonMessage);
        }

        // Get pricing configuration from factory
        $factory = app(ShippingServiceFactory::class);
        $pricingConfig = $factory->getPricingConfig(
            CourierIdentifierEnum::JT->value,
            $shippingRateDto
        );

        return $this->calculateRateFromConfig($pricingConfig, $shippingRateDto);
    }

    /**
     * Calculate shipping cost from configuration data for rates
     */
    private function calculateRateFromConfig(PricingConfigDto $pricingConfig, ShippingRateDto $shippingRateDto): int
    {
        // Calculate total weight from order boxes
        $totalWeight = ($shippingRateDto->calculateWeightFromBoxes($pricingConfig->volumetricDivisor) * 100);

        $basePrice = $pricingConfig->basePrice;
        $additionalWeightCost = 0;
        $distanceCost = 0;

        // Calculate additional weight cost
        if ($totalWeight > $pricingConfig->extraWeightFrom) {
            $additionalWeight = $totalWeight - $pricingConfig->extraWeightFrom;
            $additionalWeightCost = ($additionalWeight / 100) * $pricingConfig->additionalWeightCost;
        }

        // Add cash on delivery cost if applicable
        if ($shippingRateDto->isCod && ! $shippingRateDto->ignoreCod) {
            $basePrice = $basePrice + $pricingConfig->cashOnDeliveryCost;
        }

        // Calculate distance cost if applicable
        if ($pricingConfig->distanceCost > 0) {
            // TODO: Implement distance calculation logic
            // $distanceCost = $this->calculateDistanceCost($shippingRateDto, $pricingConfig->distanceCost);
        }

        // Calculate the total cost
        return $basePrice + $additionalWeightCost + $distanceCost;
    }

    public function createReverseShipment(Order $order): array
    {
        $timestamp = (string) round(microtime(true) * 1000);
        $customerCode = $this->clientInfo['customerCode'];
        //        TODO: FIX ITEMS ARRAY
        $orderData = [
            'serviceType' => '01',
            'orderType' => '2',
            'deliveryType' => '04',
            'countryCode' => 'KSA',
            'receiver' => [
                'address' => $order->shipper_address_line,
                'street' => $order->shipper_address_line,
                'city' => $order->shipper_city,
                'area' => $order->shipper_city,
                'mobile' => $order->shipper_phone,
                'mailBox' => $order->shipper_email,
                'phone' => '',
                'countryCode' => $order->shipperCountry->code_country,
                'name' => $order->shipper_name,
                'company' => $order->shipper_name,
                'postCode' => '',
                'prov' => $order->receiverCity->name,
            ],
            'expressType' => 'EZKSA',
            'length' => 0,
            'weight' => $order->getMaxWeight() > 0 ? $order->getMaxWeight() : 1,
            'remark' => '',
            'txlogisticId' => $order->order_number,
            'goodsType' => 'ITN1',
            'priceCurrency' => 'SAR',
            'totalQuantity' => $order->boxes()->count(),
            'sender' => [
                'address' => $order->receiver_address_line,
                'street' => $order->receiver_address_line,
                'city' => $order->receiverCity->name,
                'area' => $order->receiverCity->name,
                'mobile' => $order->receiver_phone,
                'mailBox' => $order->receiver_email,
                'phone' => '',
                'countryCode' => $order->shipperCountry->code_country,
                'name' => $order->receiver_first_name.' '.$order->receiver_last_name,
                'company' => $order->receiver_first_name.' '.$order->receiver_last_name,
                'postCode' => '',
                'prov' => $order->shipper_city,
            ],
            'itemsValue' => $order->isCod() ? $order->order_grand_total : 0,
            'merchNo' => $order->order_number,
            'offerFee' => 0,
            'items' => [],
            'operateType' => 1,
            'isUnpackEnabled' => 0,
        ];
        $orderData['digest'] = self::BUSINESS_DIGEST;
        $orderData['customerCode'] = $customerCode;
        // Generate header digest
        $headerDigest = $this->getHeaderDigest($orderData);
        $response = Http::withHeaders([
            'apiAccount' => $this->clientInfo['apiAccount'],
            'digest' => $headerDigest,
            'timestamp' => $timestamp,
            'Content-Type' => 'application/x-www-form-urlencoded',
        ])->asForm()->post($this->baseUrl.'/order/addOrder', [
            'bizContent' => json_encode($orderData),
        ]);

        return $response->json();
    }

    public function cancelShipment(string $orderNumber): array
    {
        $timestamp = (string) round(microtime(true) * 1000);
        $customerCode = $this->clientInfo['customerCode'];
        $orderData['digest'] = self::BUSINESS_DIGEST;
        $orderData['customerCode'] = $customerCode;
        $orderData['txlogisticId'] = $orderNumber;
        $orderData['reason'] = 'non';
        $orderData['orderType'] = 1;
        $headerDigest = $this->getHeaderDigest($orderData);

        $response = Http::withHeaders([
            'apiAccount' => $this->clientInfo['apiAccount'],
            'digest' => $headerDigest,
            'timestamp' => $timestamp,
            'Content-Type' => 'application/x-www-form-urlencoded',
        ])->asForm()->post($this->baseUrl.'/order/cancelOrder', [
            'bizContent' => json_encode($orderData),
        ]);

        return $response->json();
    }

    /**
     * Return the order : https://open.jtjms-sa.com/#/apiDoc/orderserve/returnAndExchange
     *
     * @return array<mixed>
     *
     * @throws ConnectionException
     */
    public function returnAndExchange(Order $order): array
    {
        $timestamp = (string) round(microtime(true) * 1000);
        $customerCode = $this->clientInfo['customerCode'];
        $orderData['digest'] = 'VdlpKaoq64AZ0yEsBkvt1A==';
        $orderData['customerCode'] = $customerCode;
        $orderData['billCode'] = $order->shipment_reference;
        $orderData['returnAndExchangeType'] = '1';
        $orderData['itemDescription'] = 'test';
        $headerDigest = $this->getHeaderDigest($orderData);

        $response = Http::withHeaders([
            'apiAccount' => $this->clientInfo['apiAccount'],
            'digest' => $headerDigest,
            'timestamp' => $timestamp,
            'Content-Type' => 'application/x-www-form-urlencoded',
        ])->asForm()->post($this->baseUrl.'/order/returnAndExchange', [
            'bizContent' => json_encode($orderData),
        ]);

        return $response->json();
    }

    /**
     * Return the order : https://open.jtjms-sa.com/#/apiDoc/orderserve/returnAndExchange
     *
     * @return array<mixed>
     *
     * @throws ConnectionException
     */
    public function returnParcelAfterPickedUp(Order $order): array
    {
        $timestamp = (string) round(microtime(true) * 1000);
        $customerCode = $this->clientInfo['customerCode'];
        $orderData['digest'] = 'VdlpKaoq64AZ0yEsBkvt1A==';
        $orderData['customerCode'] = $customerCode;
        $orderData['billCode'] = $order->shipment_reference;
        $orderData['rebackTransferReason'] = 'test';
        $orderData['txlogisticId'] = $order->order_number;
        $headerDigest = $this->getHeaderDigest($orderData);

        $response = Http::withHeaders([
            'apiAccount' => $this->clientInfo['apiAccount'],
            'digest' => $headerDigest,
            'timestamp' => $timestamp,
            'Content-Type' => 'application/x-www-form-urlencoded',
        ])->asForm()->post($this->baseUrl.'/returnParcel/applyReturn?uuid=8d1c9db974ff4c1489fe3fed34ba43bb', [
            'bizContent' => json_encode($orderData),
        ]);

        return $response->json();
    }

    /**
     * Return the order : https://open.jtjms-sa.com/#/apiDoc/orderserve/returnAndExchange
     *
     * @return array<mixed>
     *
     * @throws ConnectionException
     */
    public function trackingSubscription(Order $order): array
    {
        $timestamp = (string) round(microtime(true) * 1000);
        $customerCode = $this->clientInfo['customerCode'];
        $orderData['digest'] = 'VdlpKaoq64AZ0yEsBkvt1A==';
        $orderData['customerCode'] = $customerCode;
        $orderData['billCode'] = $order->shipment_reference;
        $orderData['rebackTransferReason'] = 'test';
        $orderData['txlogisticId'] = $order->order_number;
        $headerDigest = $this->getHeaderDigest($orderData);

        $response = Http::withHeaders([
            'apiAccount' => $this->clientInfo['apiAccount'],
            'digest' => $headerDigest,
            'timestamp' => $timestamp,
            'Content-Type' => 'application/x-www-form-urlencoded',
        ])->asForm()->post($this->baseUrl.'/returnParcel/applyReturn?uuid=8d1c9db974ff4c1489fe3fed34ba43bb', [
            'bizContent' => json_encode($orderData),
        ]);

        return $response->json();
    }

    /**
     * Return the order : https://open.jtjms-sa.com/#/apiDoc/orderserve/returnAndExchange
     *
     * @return array<mixed>
     *
     * @throws ConnectionException
     */
    public function shipmentForecast(Order $order): array
    {
        $timestamp = (string) round(microtime(true) * 1000);
        $customerCode = $this->clientInfo['customerCode'];
        $orderData['digest'] = 'VdlpKaoq64AZ0yEsBkvt1A==';
        $orderData['customerCode'] = $customerCode;
        $orderData['billCode'] = $order->shipment_reference;
        $orderData['rebackTransferReason'] = 'test';
        $orderData['txlogisticId'] = $order->order_number;
        $headerDigest = $this->getHeaderDigest($orderData);

        $response = Http::withHeaders([
            'apiAccount' => $this->clientInfo['apiAccount'],
            'digest' => $headerDigest,
            'timestamp' => $timestamp,
            'Content-Type' => 'application/x-www-form-urlencoded',
        ])->asForm()->post($this->baseUrl.'/order/reservationDelivery?uuid=8d1c9db974ff4c1489fe3fed34ba43bb', [
            'bizContent' => json_encode($orderData),
        ]);

        return $response->json();
    }

    /**
     * Calculate the shipping cost for the given order.
     *
     * @return int Calculated shipping cost
     */
    public function getCosts(ShippingRateDto $shippingRateDto): int
    {
        if ($shippingRateDto->isInternational) {
            // TODO: FINISH THIS LATER
            return ************;
        }

        // Get pricing configuration from factory
        $factory = app(ShippingServiceFactory::class);
        $pricingConfig = $factory->getCostPricingConfig(
            CourierIdentifierEnum::JT->value,
            $shippingRateDto
        );

        return $this->calculateCostFromConfig($pricingConfig, $shippingRateDto);
    }

    /**
     * Calculate shipping cost from configuration data for costs
     */
    private function calculateCostFromConfig(PricingConfigDto $pricingConfig, ShippingRateDto $shippingRateDto): int
    {
        // Calculate total weight from order boxes
        $totalWeight = ($shippingRateDto->calculateWeightFromBoxes($pricingConfig->volumetricDivisor) * 100);

        $basePrice = $pricingConfig->basePrice;
        $additionalWeightCost = 0;

        // Calculate additional weight cost
        if ($totalWeight > $pricingConfig->extraWeightFrom) {
            $additionalWeight = $totalWeight - $pricingConfig->extraWeightFrom;
            $additionalWeightCost = ($additionalWeight / 100) * $pricingConfig->additionalWeightCost;
        }

        // Add cash on delivery cost if applicable (VALUE type)
        if ($shippingRateDto->isCod && ! $shippingRateDto->ignoreCod && $pricingConfig->codType === CostCodTypeEnum::VALUE->value) {
            $basePrice = $basePrice + $pricingConfig->cashOnDeliveryCost;
        }

        // Add cash on delivery cost if applicable (PERCENTAGE type)
        if ($shippingRateDto->isCod && ! $shippingRateDto->ignoreCod && $pricingConfig->codType === CostCodTypeEnum::PERCENTAGE->value) {
            $codValue = (int) (($basePrice * $pricingConfig->cashOnDeliveryCost) / 100);
            $basePrice = $basePrice + $codValue;
        }

        // Add additional weight cost to base price
        $basePrice = $basePrice + $additionalWeightCost;

        // Calculate fuel cost
        $fuelCost = ($basePrice * $pricingConfig->fuel) / 100;

        // Calculate the total cost
        return $basePrice + $fuelCost;
    }
}
