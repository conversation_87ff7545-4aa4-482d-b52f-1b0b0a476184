<?php

namespace App\Services;

use App\Models\Order;
use ArPHP\I18N\Arabic;
use Barryvdh\DomPDF\Facade\Pdf;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Storage;

class InvoiceService
{
    /**
     * <PERSON><PERSON> combined HTML for given orders (with Arabic shaping).
     *
     * @param  Collection<int, Order>  $orders
     */
    public function renderProformaInvoiceHtml(Collection $orders): string
    {
        $arabic = new Arabic;
        $fullReportHtml = '';

        foreach ($orders as $order) {
            $data = [
                'order' => $order,
                'date' => now()->format('Y-m-d'),
            ];

            $reportHtml = view('pdfs.proforma-invoice', $data)->render();

            // Arabic shaping
            $p = $arabic->arIdentify($reportHtml);
            for ($i = count($p) - 1; $i >= 0; $i -= 2) {
                $utf8ar = $arabic->utf8Glyphs(substr($reportHtml, $p[$i - 1], $p[$i] - $p[$i - 1]));
                $reportHtml = substr_replace($reportHtml, $utf8ar, $p[$i - 1], $p[$i] - $p[$i - 1]);
            }

            $fullReportHtml .= $reportHtml;
        }

        return $fullReportHtml;
    }

    /**
     * Generate PDF file from given HTML, store it, and return storage path.
     */
    public function generatePdfFromHtml(string $html): string
    {
        $pdf = PDF::loadHTML($html);

        $filePath = 'invoices/proforma_invoice_'.now()->timestamp.'.pdf';
        Storage::disk('public')->put($filePath, $pdf->output());

        return $filePath;
    }

    /**
     * Combines the two steps: render and generate PDF from orders.
     *
     * @param  Collection<int, Order>  $orders
     */
    public function generateProformaInvoice(Collection $orders): string
    {
        $html = $this->renderProformaInvoiceHtml($orders);

        return $this->generatePdfFromHtml($html);
    }
}
