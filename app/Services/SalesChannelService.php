<?php

namespace App\Services;

use App\Enums\SalesChannelEnum;
use App\Http\Controllers\WebhookController;
use App\Models\Merchant;
use App\Models\Order;
use Filament\Notifications\Notification;
use Log;

class SalesChannelService
{
    private Merchant $merchant;

    public function __construct(Merchant $merchant)
    {
        $this->merchant = $merchant;
    }

    public function synchronizeOrder(string|int $orderNumber): void
    {
        if (! $this->merchant->warehouse_id) {
            Notification::make()
                ->title(__('translation.no_warehouse_selected'))
                ->body(__('translation.no_warehouse_selected_description'))
                ->warning()
                ->send();

            return;
        }
        if ($this->merchant->type === SalesChannelEnum::SALLA->value) {
            $orderNumber = (int) $orderNumber;
            $sallaAuthService = new SallaAuthService;
            $sallaAuthService->forUser($this->merchant);
            $ordersData = $sallaAuthService->getOrderByOrderNumber($orderNumber);
            $webhookController = new WebhookController;
            $sallaDto = $webhookController->handleOrderEvent(payload: $ordersData, merchant: $this->merchant, webhookId: null);
            $sallaAuthService->handleOrderCreated($sallaDto);
        }
        if ($this->merchant->type === SalesChannelEnum::SHOPIFY->value) {
            $shopifyAuthService = new ShopifyAuthService($this->merchant);
            $shopifyAuthService->forUser($this->merchant);
            $OrdersData = $shopifyAuthService->getOrderByOrderNumber($orderNumber);
            $webhookController = new WebhookController;
            $shopifyDto = $webhookController->handleShopifyOrderEvent(payload: $OrdersData, merchant: $this->merchant, webhookId: null);
            $shopifyAuthService->handleOrderCreated($shopifyDto);
        }
        if ($this->merchant->type === SalesChannelEnum::ZID->value) {
            $zidAuthService = new ZidAuthService;
            $zidAuthService->forUser($this->merchant);
            $OrdersData = $zidAuthService->getOrderByOrderNumber($orderNumber);
            $webhookController = new WebhookController;
            $zidDto = $webhookController->handleZidOrderEvent(payload: $OrdersData, merchant: $this->merchant, webhookId: null);
            $zidAuthService->handleZidOrderCreated($zidDto);
        }
    }

    public function updateStatus(Order $order): void
    {

        if ($this->merchant->type === SalesChannelEnum::SALLA->value) {
            try {
                // Update Salla if the merchant has an access token
                if ($this->merchant->access_token) {
                    Log::info("Updating order #{$order->id} in Salla...");
                    $sallaAuthProvider = new SallaAuthService;
                    $sallaAuthProvider->forUser($this->merchant);
                    $sallaAuthProvider->changeStatus($order);
                }
            } catch (\Exception $e) {
                Log::info($e->getMessage());
            }
        }

        if ($this->merchant->type === SalesChannelEnum::WOOCOMMERCE->value) {
            try {
                if ($this->merchant->domain && $this->merchant->api_key && $this->merchant->api_secret_key) {
                    Log::info("Updating order #{$order->id} in WooCommerce...");
                    $woocommerceAuthService = new WoocommerceAuthService;
                    $woocommerceAuthService->forUser($this->merchant);
                    $woocommerceAuthService->changeStatus($order);
                }
            } catch (\Exception $e) {
                Log::info($e->getMessage());
            }
        }

        if ($this->merchant->type === SalesChannelEnum::ZID->value) {
            try {
                if ($this->merchant->authorization && $this->merchant->access_token) {
                    Log::info("Updating order #{$order->id} status in Zid...");
                    $woocommerceAuthService = new ZidAuthService;
                    $woocommerceAuthService->forUser($this->merchant);
                    $woocommerceAuthService->changeStatus($order);
                }
            } catch (\Exception $e) {
                Log::info($e->getMessage());
            }
        }

    }

    public function sendLabel(Order $order, string $url): void
    {
        if ($this->merchant->type === SalesChannelEnum::ZID->value) {
            try {
                if ($this->merchant->authorization && $this->merchant->access_token) {
                    Log::info("Updating order #{$order->id} shipping status in Zid...");
                    $woocommerceAuthService = new ZidAuthService;
                    $woocommerceAuthService->forUser($this->merchant);
                    $woocommerceAuthService->sendLabel($order, $url);
                }
            } catch (\Exception $e) {
                Log::info($e->getMessage());
            }
        }
    }
}
