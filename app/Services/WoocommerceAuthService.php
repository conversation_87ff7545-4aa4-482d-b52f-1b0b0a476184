<?php

namespace App\Services;

use App\Dto\WoocommerceOrderDto;
use App\Enums\OrderStatusEnum;
use App\Enums\SalesChannelEnum;
use App\Interfaces\EcommerceInterface;
use App\Models\City;
use App\Models\Country;
use App\Models\Merchant;
use App\Models\Order;
use App\Models\OrderItem;
use App\Models\Warehouse;
use Http;
use Illuminate\Support\Facades\Log;
use Salla\OAuth2\Client\Provider\Salla;

/**
 * @mixin Salla
 */
class WoocommerceAuthService implements EcommerceInterface
{
    public Merchant $merchant;

    public string $baseUrl;

    public string $consumerKey;

    public string $consumerSecret;

    /**
     * Get the token from the user model.
     *
     *
     * @return $this
     */
    public function forUser(Merchant $merchant): static
    {
        $this->merchant = $merchant;
        $this->baseUrl = rtrim($merchant->domain, '/');
        $this->consumerKey = $merchant->api_key;
        $this->consumerSecret = $merchant->api_secret_key;

        return $this;
    }

    public function handleOrderCreated(WoocommerceOrderDto $orderDto): void
    {
        $alreadyExist = Order::withoutGlobalScopes()->where('order_number', $orderDto->orderNumber)->exists();
        if ($alreadyExist) {
            Log::info('Order already Exist'.$orderDto->orderNumber);

            return;
        }
        $warehouse = $this->getWarehouse(Merchant::withoutGlobalScopes()->findOrFail($orderDto->merchantId), $orderDto);
        try {
            /** @var Order $createdOrder */
            $createdOrder = Order::create([
                'external_id' => $orderDto->externalId,
                'order_number' => $orderDto->orderNumber,
                'status' => $this->getWooCommerceStatus($orderDto),
                'date' => $orderDto->date,
                'order_grand_total' => $orderDto->orderGrandTotal,
                'description' => $orderDto->description,
                'payment_method' => $orderDto->paymentMethod,
                'receiver_first_name' => $orderDto->receiverFirstName,
                'receiver_last_name' => $orderDto->receiverLastName,
                'receiver_phone' => phoneCorrector(receivedPhone: $orderDto->receiverPhone, correctWithCountry: Country::findByString(code: $orderDto->receiverCountryCode)),
                'receiver_email' => $orderDto->receiverEmail,
                'receiver_country_id' => Country::findByString(code: $orderDto->receiverCountryCode),
                'receiver_country' => $orderDto->receiverCountry,
                'receiver_address_line' => $orderDto->receiverAddressLine,
                'receiver_street_name' => $orderDto->receiverStreetName,
                'receiver_city' => $orderDto->receiverCity,
                'receiver_country_code' => $orderDto->receiverCountryCode,
                'receiver_postal_code' => $orderDto->receiverPostalCode,
                'receiver_latitude' => $orderDto->receiverLatitude,
                'receiver_longitude' => $orderDto->receiverLongitude,
                'merchant_id' => $orderDto->merchantId,
                'webhook_id' => $orderDto->webhookId,
                'shipment_total_weight' => $orderDto->shipmentTotalWeight,
                'tax' => $orderDto->tax,
                'receiver_city_id' => City::findByString($orderDto->receiverCity),
                'source' => SalesChannelEnum::WOOCOMMERCE->value,
                ...$warehouse,
            ]);
            $createdOrder->logOrderCreation();
            foreach ($orderDto->items as $itemDto) {
                OrderItem::create([
                    'order_id' => $createdOrder->id,
                    'name' => $itemDto->name,
                    'sku' => $itemDto->sku,
                    'quantity' => $itemDto->quantity,
                    'weight' => $itemDto->weight,
                    'weight_unit' => 'g',
                    'price' => $itemDto->price,
                    'total_price' => $itemDto->totalPrice,
                    'tax' => $itemDto->tax,
                ]);
            }

        } catch (\Exception $e) {
            // Log errors for debugging
            logger('Error creating order', [
                'error' => $e->getMessage(),
                'payload' => $orderDto,
            ]);
            throw $e;
        }
    }

    /**
     * @return array<string, int|string|null>
     */
    private function getWarehouse(Merchant $merchant, WoocommerceOrderDto $woocommerceOrderDto): array
    {
        if ($merchant->warehouse_id) {
            $warehouse = Warehouse::withoutGlobalScopes()->findOrFail($merchant->warehouse_id);

            return [
                'warehouse_id' => $merchant->warehouse_id,
                'shipper_phone' => $warehouse->sender_phone,
                'shipper_email' => $warehouse->sender_email,
                'shipper_name' => $warehouse->name,
                'shipper_city' => $warehouse->city->name ?? $warehouse->city->name_ar ?? '',
                'shipper_address_line' => $warehouse->address,
                'shipper_latitude' => 0,
                'shipper_longitude' => 0,
            ];
        }

        return [
            'shipper_phone' => $woocommerceOrderDto->shipperPhone,
            'shipper_name' => $woocommerceOrderDto->shipperName,
            'shipper_city' => $woocommerceOrderDto->shipperCity,
            'shipper_address_line' => $woocommerceOrderDto->shipperAddressLine,
            'shipper_latitude' => $woocommerceOrderDto->shipperLatitude,
            'shipper_longitude' => $woocommerceOrderDto->shipperLongitude,
        ];
    }

    public function changeStatus(Order $order): void
    {

        $orderId = $order->external_id;
        $status = $this->getWooCommerceStatus($order);

        $response = Http::withBasicAuth($this->consumerKey, $this->consumerSecret)
            ->withHeaders([
                'Content-Type' => 'application/json',
            ])->put($this->baseUrl.'/wp-json/wc/v3/orders/'.$orderId, [
                'status' => $status,
            ]);
    }

    private function getWooCommerceStatus(Order|WoocommerceOrderDto $order): string
    {
        return match ($order->status) {
            OrderStatusEnum::PENDING->value => 'pending',
            OrderStatusEnum::CURRENTLY_SHIPPING->value => 'processing',
            OrderStatusEnum::DELIVERED->value => 'completed',
            OrderStatusEnum::SHIPMENT_ON_HOLD->value => 'on-hold',
            OrderStatusEnum::CANCELED->value => 'cancelled',
            OrderStatusEnum::FAILED->value => 'failed',
            default => 'pending',
        };
    }
}
