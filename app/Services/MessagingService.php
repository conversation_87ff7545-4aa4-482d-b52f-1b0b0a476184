<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;

class MessagingService
{
    protected string $baseUrl;

    protected string $instanceId;

    protected string $token;

    public function __construct()
    {
        $this->baseUrl = 'https://user.4whats.net/api';
        $this->instanceId = '139523';
        $this->token = '25a1bc17-f154-46d5-a89b-ba6e2ccf28ad';
    }

    /**
     * Send a message using the 4Whats API.
     *
     * @param  string  $phone  The recipient's phone number.
     * @param  string  $message  The message to send.
     * @return array<string, mixed>|string The response from the API.
     */
    public function sendMessage(string $phone, string $message): array|string
    {
        try {

            $url = "{$this->baseUrl}/sendMessage";

            $response = Http::get($url, [
                'instanceid' => $this->instanceId,
                'token' => $this->token,
                'phone' => $phone,
                'body' => $message,
            ]);

            if ($response->successful()) {
                return $response->json();
            }

            return $response->body();
        } catch (\Exception $exception) {
            return ['error' => $exception->getMessage()];
        }
    }
}
