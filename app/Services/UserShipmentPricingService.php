<?php

namespace App\Services;

use App\Dto\PricingConfigDto;
use App\Models\ShipmentCourierService;
use App\Models\User;
use App\Models\UserShipmentPrice;

class UserShipmentPricingService
{
    /**
     * Get the pricing configuration for a user and courier service.
     * Falls back to global pricing if no custom pricing is found.
     *
     * @throws \Exception
     */
    public function getPricingConfig(User $user, string $courierIdentifier): PricingConfigDto
    {
        // Try to find custom user pricing
        $userPricing = UserShipmentPrice::where('user_id', $user->id)
            ->whereHas('shipmentCourierService', function ($query) use ($courierIdentifier) {
                $query->where('identifier', $courierIdentifier);
            })
            ->active()
            ->first();

        if ($userPricing) {
            return new PricingConfigDto(
                basePrice: (int) $userPricing->base_price,
                extraWeightFrom: (int) $userPricing->extra_weight_from,
                additionalWeightCost: (int) $userPricing->additional_weight_cost,
                cashOnDeliveryCost: (int) $userPricing->cash_on_delivery_cost,
                distanceCost: (int) $userPricing->distance_cost,
                volumetricDivisor: (int) $userPricing->volumetric_divisor
            );
        }

        // Fall back to global pricing
        $globalService = ShipmentCourierService::where('identifier', $courierIdentifier)->first();

        if (! $globalService) {
            throw new \Exception("No pricing configuration found for courier: {$courierIdentifier}");
        }

        return new PricingConfigDto(
            basePrice: $globalService->base_price,
            extraWeightFrom: $globalService->extra_weight_from,
            additionalWeightCost: $globalService->additional_weight_cost,
            cashOnDeliveryCost: $globalService->cash_on_delivery_cost,
            distanceCost: $globalService->distance_cost,
            volumetricDivisor: $globalService->getVolumetricDivisor()
        );
    }
}
