<?php

namespace App\Services;

use App\Actions\Shared\ShopifySessionStorage;
use App\Dto\ShopifyOrderDto;
use App\Enums\SalesChannelEnum;
use App\Interfaces\EcommerceInterface;
use App\Models\City;
use App\Models\Country;
use App\Models\Merchant;
use App\Models\Order;
use App\Models\OrderItem;
use App\Models\Warehouse;
use Filament\Notifications\Notification;
use Illuminate\Http\Client\ConnectionException;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use League\OAuth2\Client\Provider\Exception\IdentityProviderException;
use Salla\OAuth2\Client\Provider\Salla;
use Shopify\Auth\Session;
use Shopify\Clients\Rest;
use Shopify\Context;

/**
 * @mixin Salla
 */
class ShopifyAuthService implements EcommerceInterface
{
    private Session $session;

    public function __construct(private Merchant $merchant, bool $isNew = false)
    {
        Context::initialize(
            'a3a41f6bae261d4eacf7d9cc9f6a91c5',
            '626b2bd44c80207148679004026c6ec1',
            ['*'],
            $this->merchant->name,
            new ShopifySessionStorage,
            '2024-07',
            false,
        );

        $this->session = new Session(
            id: 'test',
            shop: $isNew ? $this->merchant->name : 'test',
            isOnline: true,
            state: 'test'
        );
        if (! $merchant->access_token) {
            $jsonMessage = json_encode([
                'error' => 'Missing access token',
                'merchant_id' => $merchant->id,
                'merchant_name' => $merchant->name,
            ], JSON_UNESCAPED_UNICODE);
            if ($jsonMessage === false) {
                throw new \Exception('JSON encode failed: '.json_last_error_msg());
            }
            throw new \Exception($jsonMessage);
        }

        $this->session->setAccessToken($merchant->access_token);
    }

    /**
     * Get the token from the user model.
     *
     *
     * @return $this
     */
    public function forUser(Merchant $merchant): static
    {
        $this->merchant = $merchant;

        return $this;
    }

    /**
     * @return array<mixed>
     *
     * @throws IdentityProviderException
     */
    public function getOrderByOrderNumber(string|int $orderNumber): array
    {
        $orderNumber = ltrim((string) $orderNumber, '#');

        $response = Http::withHeaders([
            'X-Shopify-Access-Token' => $this->merchant->access_token,
            'Content-Type' => 'application/json',
        ])->get(
            'https://'.$this->merchant->domain.'/admin/api/2025-04/orders.json?status=any&name=%23'.$orderNumber
        );

        if (! $response->successful()) {
            $errorMessage = $response->json('errors') ?? 'Unknown error from Shopify API';
            $jsonMessage = json_encode([
                'error' => 'Shopify API error',
                'api_error' => $errorMessage,
                'order_number' => $orderNumber,
                'status_code' => $response->status(),
            ], JSON_UNESCAPED_UNICODE);
            if ($jsonMessage === false) {
                throw new \Exception('JSON encode failed: '.json_last_error_msg());
            }
            throw new \Exception($jsonMessage);
        }

        $orders = $response->json('orders');
        if (empty($orders)) {
            $jsonMessage = json_encode([
                'error' => 'No orders found for order number',
                'order_number' => $orderNumber,
                'merchant_domain' => $this->merchant->domain,
            ], JSON_UNESCAPED_UNICODE);
            if ($jsonMessage === false) {
                throw new \Exception('JSON encode failed: '.json_last_error_msg());
            }
            throw new \Exception($jsonMessage);
        }

        return $orders[0];
    }

    public function handleOrderCreated(ShopifyOrderDto $orderDto): void
    {
        $alreadyExist = Order::withoutGlobalScopes()
            ->where('external_id', $orderDto->externalId)
            ->where('source', SalesChannelEnum::SHOPIFY->value)
            ->first();
        if ($alreadyExist) {
            Log::info('Order already Exist'.$orderDto->orderNumber);

            $merchantExistOrder = Merchant::withoutGlobalScopes()->where('id', $alreadyExist->merchant_id)->first();
            $orderInThisAccount = $merchantExistOrder?->user_id === auth()->id();
            $inAccountStr = $orderInThisAccount ? 'in this account' : 'in other account';
            Notification::make()
                ->title('order exist')
                ->body("order $orderDto->orderNumber already exist".' '.$inAccountStr)
                ->warning()
                ->send();

            return;
        }
        $warehouse = $this->getWarehouse(Merchant::withoutGlobalScopes()->findOrFail($orderDto->merchantId), $orderDto);
        try {
            /** @var Order $createdOrder */
            $createdOrder = Order::create([
                'external_id' => $orderDto->externalId,
                'order_number' => $orderDto->merchantId.'-'.$orderDto->orderNumber,
                'status' => $orderDto->status,
                'date' => $orderDto->date,
                'order_grand_total' => $orderDto->orderGrandTotal,
                'description' => $orderDto->description,
                'payment_method' => $orderDto->paymentMethod,
                'receiver_first_name' => $orderDto->receiverFirstName,
                'receiver_last_name' => $orderDto->receiverLastName,
                'receiver_phone' => phoneCorrector(receivedPhone: $orderDto->receiverPhone, correctWithCountry: Country::findByString($orderDto->receiverCountry, $orderDto->receiverCountryCode)),
                'receiver_email' => $orderDto->receiverEmail,
                'receiver_country_id' => Country::findByString($orderDto->receiverCountry, $orderDto->receiverCountryCode),
                'receiver_country' => $orderDto->receiverCountry,
                'receiver_address_line' => $orderDto->receiverAddressLine,
                'receiver_city' => $orderDto->receiverCity,
                'receiver_country_code' => $orderDto->receiverCountryCode,
                'receiver_postal_code' => $orderDto->receiverPostalCode,
                'receiver_latitude' => $orderDto->receiverLatitude,
                'receiver_longitude' => $orderDto->receiverLongitude,
                'merchant_id' => $orderDto->merchantId,
                'webhook_id' => $orderDto->webhookId,
                'shipment_total_weight' => $orderDto->shipmentTotalWeight,
                'tax' => $orderDto->tax,
                'receiver_city_id' => City::findByString($orderDto->receiverCity),
                'source' => SalesChannelEnum::SHOPIFY->value,
                ...$warehouse,
            ]);
            $createdOrder->logOrderCreation();
            foreach ($orderDto->items as $itemDto) {
                OrderItem::create([
                    'order_id' => $createdOrder->id,
                    'name' => $itemDto->name,
                    'sku' => $itemDto->sku,
                    'quantity' => $itemDto->quantity,
                    'weight' => $itemDto->weight,
                    'weight_unit' => 'g',
                    'price' => $itemDto->price,
                    'total_price' => $itemDto->totalPrice,
                    'tax' => $itemDto->tax,
                ]);
            }
            Notification::make()
                ->title(__('translation.order_synchronized'))
                ->success()
                ->send();

        } catch (\Exception $e) {
            // Log errors for debugging
            logger('Error creating order', [
                'error' => $e->getMessage(),
                'payload' => $orderDto,
            ]);
            throw $e;
        }
    }

    //    public function handleOrderUpdated(mixed $orderData): void
    //    {
    //        $order = Order::where('external_id', $orderData['id'])->first();
    //        if (! $orderData) {
    //            throw new \Exception('Order not found');
    //        }
    //        $order->status = $orderData['financial_status'];
    //        $order->save();
    //    }

    public function testConnection(): void
    {
        // TODO: Implement testConnection() method.
    }

    /**
     * @return array<mixed>
     */
    public function getShop(): array
    {
        $client = new Rest(
            $this->session->getShop(),
            $this->session->getAccessToken()
        );

        return json_decode($client->get('shop')->getBody(), true);
    }

    /**
     * @return array<string, int|string|null>
     */
    private function getWarehouse(Merchant $merchant, ShopifyOrderDto $shopifyOrderDto): array
    {
        if ($merchant->warehouse_id) {
            $warehouse = Warehouse::withoutGlobalScopes()->findOrFail($merchant->warehouse_id);

            return [
                'shipper_phone' => $warehouse->sender_phone,
                'shipper_email' => $warehouse->sender_email,
                'shipper_name' => $warehouse->name,
                'shipper_city' => $warehouse->city->name ?? $warehouse->city->name_ar ?? '',
                'shipper_address_line' => $warehouse->address,
                'shipper_latitude' => 0,
                'shipper_longitude' => 0,
            ];
        }

        return [
            'shipper_phone' => $shopifyOrderDto->shipperPhone,
            'shipper_name' => $shopifyOrderDto->shipperName,
            'shipper_city' => $shopifyOrderDto->shipperCity,
            'shipper_address_line' => $shopifyOrderDto->shipperAddressLine,
            'shipper_latitude' => $shopifyOrderDto->shipperLatitude,
            'shipper_longitude' => $shopifyOrderDto->shipperLongitude,
        ];
    }

    /**
     * Fetch fulfillment orders for a given order
     *
     * @return array<int, mixed>
     *
     * @throws ConnectionException
     */
    public function getFulfillmentOrders(string $orderId): array
    {
        $response = Http::withHeaders([
            'X-Shopify-Access-Token' => $this->merchant->access_token,
            'Content-Type' => 'application/json',
        ])->get(
            "https://{$this->merchant->domain}/admin/api/2024-01/orders/{$orderId}/fulfillment_orders.json"
        );

        if (! $response->successful()) {
            Log::error('Failed to fetch Shopify fulfillment orders', [
                'order_id' => $orderId,
                'response' => $response->body(),
            ]);
            $jsonMessage = json_encode([
                'error' => 'Failed to fetch Shopify fulfillment orders',
                'order_id' => $orderId,
                'response_body' => $response->body(),
                'status_code' => $response->status(),
            ], JSON_UNESCAPED_UNICODE);
            if ($jsonMessage === false) {
                throw new \Exception('JSON encode failed: '.json_last_error_msg());
            }
            throw new \Exception($jsonMessage);
        }

        return $response->json()['fulfillment_orders'] ?? [];
    }

    /**
     * Create fulfillments for all fulfillment orders of a Shopify order
     *
     * @param  string[]  $trackingInfo
     *
     * @throws ConnectionException
     */
    public function updateOrderStatus(string $orderId, array $trackingInfo = []): void
    {
        // First, fetch fulfillment orders for this order
        $fulfillmentOrders = $this->getFulfillmentOrders($orderId);
        sleep(2);
        if (empty($fulfillmentOrders)) {
            Log::warning('No fulfillment orders found for Shopify order', [
                'order_id' => $orderId,
            ]);

            return;
        }

        $successfulFulfillments = [];
        $failedFulfillments = [];

        // Loop through all fulfillment orders and create fulfillments for each
        foreach ($fulfillmentOrders as $fulfillmentOrder) {
            try {
                // Skip if already fulfilled or closed
                if (in_array($fulfillmentOrder['status'], ['cancelled', 'incomplete', 'closed'])) {
                    Log::info('Skipping fulfillment order with status: '.$fulfillmentOrder['status'], [
                        'order_id' => $orderId,
                        'fulfillment_order_id' => $fulfillmentOrder['id'],
                    ]);

                    continue;
                }

                // Only process if there are unfulfilled line items
                $unfulfilled_line_items = array_filter($fulfillmentOrder['line_items'], function ($lineItem) {
                    return $lineItem['fulfillable_quantity'] > 0;
                });

                if (empty($unfulfilled_line_items)) {
                    Log::info('No unfulfilled items in fulfillment order', [
                        'order_id' => $orderId,
                        'fulfillment_order_id' => $fulfillmentOrder['id'],
                    ]);

                    continue;
                }

                $fulfillmentOrderId = $fulfillmentOrder['id'];

                // Prepare line items for fulfillment (only unfulfilled quantities)
                $lineItems = [];
                foreach ($unfulfilled_line_items as $lineItem) {
                    $fulfillableQuantity = $lineItem['fulfillable_quantity'];
                    if ($fulfillableQuantity > 0) {
                        $lineItems[] = [
                            'id' => $lineItem['id'],
                            'quantity' => $fulfillableQuantity,
                        ];
                    }
                }

                if (empty($lineItems)) {
                    continue;
                }

                // Create fulfillment data using the new fulfillment orders workflow
                $fulfillmentData = [
                    'fulfillment' => [
                        'line_items_by_fulfillment_order' => [
                            [
                                'fulfillment_order_id' => $fulfillmentOrderId,
                                'fulfillment_order_line_items' => $lineItems,
                            ],
                        ],
                        'notify_customer' => true,
                    ],
                ];

                // Add the same tracking information to all fulfillments (same shipment)
                if (! empty($trackingInfo)) {
                    $fulfillmentData['fulfillment']['tracking_info'] = [
                        'number' => $trackingInfo['tracking_number'] ?? null,
                        'company' => $trackingInfo['tracking_company'] ?? null,
                        'url' => $trackingInfo['tracking_url'] ?? null,
                    ];

                    Log::info('Adding tracking info to fulfillment order', [
                        'order_id' => $orderId,
                        'fulfillment_order_id' => $fulfillmentOrderId,
                        'tracking_number' => $trackingInfo['tracking_number'] ?? null,
                        'tracking_company' => $trackingInfo['tracking_company'] ?? null,
                        'tracking_url' => $trackingInfo['tracking_url'] ?? null,
                    ]);
                }

                // Create the fulfillment using the new endpoint
                $response = Http::withHeaders([
                    'X-Shopify-Access-Token' => $this->merchant->access_token,
                    'Content-Type' => 'application/json',
                ])->post(
                    "https://{$this->merchant->domain}/admin/api/2024-01/fulfillments.json",
                    $fulfillmentData
                );

                if (! $response->successful()) {
                    $errorMessage = 'Failed to create Shopify fulfillment: '.$response->body();
                    Log::error('Failed to create Shopify fulfillment', [
                        'order_id' => $orderId,
                        'fulfillment_order_id' => $fulfillmentOrderId,
                        'response' => $response->body(),
                    ]);
                    $failedFulfillments[] = [
                        'fulfillment_order_id' => $fulfillmentOrderId,
                        'error' => $errorMessage,
                    ];

                    continue;
                }

                $successfulFulfillments[] = $fulfillmentOrderId;
                Log::info('Successfully created Shopify fulfillment', [
                    'order_id' => $orderId,
                    'fulfillment_order_id' => $fulfillmentOrderId,
                    'tracking_number' => $trackingInfo['tracking_number'] ?? null,
                ]);

            } catch (\Exception $e) {
                Log::error('Exception while creating fulfillment', [
                    'order_id' => $orderId,
                    'fulfillment_order_id' => $fulfillmentOrder['id'] ?? 'unknown',
                    'error' => $e->getMessage(),
                ]);
                $failedFulfillments[] = [
                    'fulfillment_order_id' => $fulfillmentOrder['id'] ?? 'unknown',
                    'error' => $e->getMessage(),
                ];
            }
        }

        // Log summary
        Log::info('Shopify fulfillment processing completed', [
            'order_id' => $orderId,
            'successful_fulfillments' => count($successfulFulfillments),
            'failed_fulfillments' => count($failedFulfillments),
            'successful_ids' => $successfulFulfillments,
            'failed_details' => $failedFulfillments,
            'tracking_applied_to_all' => [
                'tracking_number' => $trackingInfo['tracking_number'] ?? null,
                'tracking_company' => $trackingInfo['tracking_company'] ?? null,
                'tracking_url' => $trackingInfo['tracking_url'] ?? null,
            ],
        ]);

        // Throw exception only if ALL fulfillments failed
        if (! empty($failedFulfillments) && empty($successfulFulfillments)) {
            $errorDetails = collect($failedFulfillments)->pluck('error')->join('; ');
            $jsonMessage = json_encode([
                'error' => 'All fulfillment orders failed',
                'order_id' => $orderId,
                'failed_details' => $failedFulfillments,
                'successful_count' => count($successfulFulfillments),
                'failed_count' => count($failedFulfillments),
            ], JSON_UNESCAPED_UNICODE);
            if ($jsonMessage === false) {
                throw new \Exception('JSON encode failed: '.json_last_error_msg());
            }
            throw new \Exception($jsonMessage);
        }
    }

    public function createWebhooksForMerchant(): void
    {
        $webhooks = [
            [
                'topic' => 'orders/create',
                'address' => 'https://www.gotreek.com/api/webhook/shopify',
            ],
        ];
        $merchant = $this->merchant;
        foreach ($webhooks as $webhook) {
            $response = Http::withHeaders([
                'X-Shopify-Access-Token' => $merchant->access_token,
                'Content-Type' => 'application/json',
            ])->post("https://{$merchant->domain}/admin/api/2024-01/webhooks.json", [
                'webhook' => [
                    'topic' => $webhook['topic'],
                    'address' => $webhook['address'],
                    'format' => 'json',
                ],
            ]);
            if ($response->successful()) {
                $merchant->update([
                    'webhook_is_linked' => true,
                ]);
            }
        }
    }

    /**
     * Update Shopify order payment status to paid using GraphQL
     */
    public function updateOrderToPaid(string $orderId): void
    {
        // Convert numeric order ID to Shopify GID format
        $shopifyGid = "gid://shopify/Order/{$orderId}";

        $mutation = <<<'GRAPHQL'
        mutation orderMarkAsPaid($input: OrderMarkAsPaidInput!) {
            orderMarkAsPaid(input: $input) {
                order {
                    id
                    displayFinancialStatus
                    totalPrice
                }
                userErrors {
                    field
                    message
                }
            }
        }
        GRAPHQL;

        $variables = [
            'input' => [
                'id' => $shopifyGid,
            ],
        ];

        $response = Http::withHeaders([
            'X-Shopify-Access-Token' => $this->merchant->access_token,
            'Content-Type' => 'application/json',
        ])->post(
            "https://{$this->merchant->domain}/admin/api/2024-01/graphql.json",
            [
                'query' => $mutation,
                'variables' => $variables,
            ]
        );

        if (! $response->successful()) {
            $errorMessage = $response->json('errors') ?? 'Unknown error from Shopify GraphQL API';
            throw new \Exception("Failed to update Shopify order payment status: {$errorMessage}");
        }

        $data = $response->json('data');

        // Check for GraphQL user errors
        /** @var array<array{message: string}> $userErrorsArray */
        $userErrorsArray = $data['orderMarkAsPaid']['userErrors'];

        if (! empty($userErrorsArray)) {
            $userErrors = collect($userErrorsArray)
                ->pluck('message')
                ->join('; ');
            throw new \Exception("GraphQL user errors: {$userErrors}");
        }

        // Verify the order was marked as paid
        $order = $data['orderMarkAsPaid']['order'] ?? null;
        if (! $order) {
            throw new \Exception('No order data returned from GraphQL mutation');
        }

        Log::info('Successfully marked Shopify order as paid via GraphQL', [
            'order_id' => $orderId,
            'shopify_gid' => $shopifyGid,
            'financial_status' => $order['displayFinancialStatus'] ?? 'unknown',
            'total_price' => $order['totalPrice'] ?? 'unknown',
        ]);
    }
}
