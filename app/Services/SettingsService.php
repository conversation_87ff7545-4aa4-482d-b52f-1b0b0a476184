<?php

namespace App\Services;

use Illuminate\Support\Facades\DB;

class SettingsService
{
    /**
     * @return string[]
     */
    public function getPlatformConfig(string $serviceName): array
    {
        $settings = DB::table('settings')
            ->where('key', 'like', "{$serviceName}.%")
            ->pluck('value', 'key')
            ->toArray();

        return [
            'username' => $settings["{$serviceName}.userName"] ?? null,
            'password' => $settings["{$serviceName}.password"] ?? null,
            'Version' => $settings["{$serviceName}.version"] ?? null,
            'account_number' => $settings["{$serviceName}.accountNumber"] ?? null,
            'account_pin' => $settings["{$serviceName}.accountPin"] ?? null,
            'AccountEntity' => $settings["{$serviceName}.accountEntity"] ?? null,
            'AccountCountryCode' => $settings["{$serviceName}.countryCode"] ?? null,
            'Source' => $settings["{$serviceName}.source"] ?? null,
            'customerCode' => $settings["{$serviceName}.customerCode"] ?? null,
            'apiAccount' => $settings["{$serviceName}.apiAccount"] ?? null,
            // SPL-specific configuration
            'crm_account_id' => $settings["{$serviceName}.crmAccountId"] ?? null,
            'contract_id' => $settings["{$serviceName}.contractId"] ?? null,
            'base_url' => $settings["{$serviceName}.baseUrl"] ?? null,
            'token_url' => $settings["{$serviceName}.tokenUrl"] ?? null,
        ];
    }

    /**
     * Get the volumetric divisor from settings
     */
    public function getVolumetricDivisor(): int
    {
        $divisor = DB::table('settings')
            ->where('key', 'volumetric_divisor')
            ->value('value');

        return (int) $divisor;
    }

    public function getCouponCode(): string
    {
        $couponCode = DB::table('settings')
            ->where('key', 'coupon_code')
            ->value('value');

        return (string) $couponCode;
    }
}
