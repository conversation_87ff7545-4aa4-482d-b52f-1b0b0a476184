<?php

namespace App\Services;

use App\Dto\PricingConfigDto;
use App\Dto\ShipmentRequestDto;
use App\Dto\ShippingRateDto;
use App\Enums\CostCodTypeEnum;
use App\Enums\CourierIdentifierEnum;
use App\Interfaces\ShipmentCourierInterface;
use App\Models\Order;
use App\Models\ShipmentCourierService;
use Carbon\Carbon;
use Exception;
use Illuminate\Http\Client\ConnectionException;
use Illuminate\Http\Client\PendingRequest;
use Illuminate\Http\Client\Response;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use setasign\Fpdi\Fpdi;

class TranscorpSBService implements ShipmentCourierInterface
{
    const string SHIPMENT_TRACKING_URL = 'https://transcorp.suitefleet.com/task/tracking/';

    protected string $baseUrl = 'https://api.suitefleet.com/api';

    protected ?string $token = null;

    protected string $userName;

    protected string $password;

    /**
     * @var string[]
     */
    protected array $config = [];

    /**
     * @param  string[]  $config
     */
    public function setConfig(array $config): void
    {
        $this->userName = $config['username'];
        $this->password = $config['password'];
    }

    private function setToken(string $token): void
    {
        $this->token = $token;
    }

    private function ensureAuthenticated(): void
    {
        if (! $this->token) {
            $authResponse = $this->login();
            // Try both 'token' and 'accessToken' field names for compatibility
            $accessToken = $authResponse['token'] ?? $authResponse['accessToken'] ?? null;

            if (! $accessToken) {
                Log::error('TranscorpSB authentication failed', [
                    'response' => $authResponse,
                    'username' => $this->userName,
                ]);
                $jsonMessage = json_encode([
                    'error' => 'Access token not found in authentication response',
                    'username' => $this->userName,
                    'response_keys' => array_keys($authResponse),
                ], JSON_UNESCAPED_UNICODE);
                if ($jsonMessage === false) {
                    throw new Exception('JSON encode failed: '.json_last_error_msg());
                }
                throw new Exception($jsonMessage);
            }

            $this->setToken($accessToken);
        }
    }

    /**
     * @return array<string, bool|string>
     */
    /**
     * @return array<string, string>
     */
    private function headers(): array
    {
        $headers = [
            'Content-Type' => 'application/json',
            'Clientid' => (string) config('services.transcorpsb.clientid'),
        ];

        if ($this->token) {
            $authPrefix = 'Bearer';
            $headers['Authorization'] = "{$authPrefix} {$this->token}";
        }

        return $headers;
    }

    /**
     * @return array<string, string>
     */
    private function headersForPatch(): array
    {
        $headers = [
            'Content-Type' => 'application/merge-patch+json',
            'Clientid' => (string) config('services.transcorpsb.clientid'),
        ];

        if ($this->token) {
            $authPrefix = 'Bearer';
            $headers['Authorization'] = "{$authPrefix} {$this->token}";
        }

        return $headers;
    }

    private function httpClient(): PendingRequest
    {
        return Http::withHeaders($this->headers());
    }

    private function httpClientForPatch(): PendingRequest
    {
        return Http::withHeaders($this->headersForPatch());
    }

    /**
     * @return string[]
     *
     * @throws ConnectionException
     * @throws \Exception
     */
    public function login(): array
    {
        $url = "{$this->baseUrl}/auth/authenticate";
        $queryParams = 'username='.$this->userName.'&password='.$this->password;

        Log::info('TranscorpSB login attempt', [
            'url' => $url,
            'username' => $this->userName,
        ]);

        $response = Http::withHeaders($this->headers())
            ->post("{$url}?{$queryParams}");

        if ($response->successful()) {
            $data = $response->json();

            Log::info('TranscorpSB login successful', [
                'response_keys' => array_keys($data),
                'has_token' => isset($data['token']),
                'has_accessToken' => isset($data['accessToken']),
            ]);

            $token = $data['token'] ?? null;
            if ($token) {
                $this->setToken($token);
            }

            return $data;
        }

        Log::error('TranscorpSB login failed', [
            'status' => $response->status(),
            'response' => $response->body(),
        ]);

        $jsonMessage = json_encode([
            'error' => 'TranscorpSB login failed',
            'username' => $this->userName,
            'status_code' => $response->status(),
            'response_body' => $response->body(),
        ], JSON_UNESCAPED_UNICODE);
        if ($jsonMessage === false) {
            throw new Exception('JSON encode failed: '.json_last_error_msg());
        }
        throw new Exception($jsonMessage);
    }

    /**
     * @throws \Exception
     */
    public function createShipment(Order $order): ShipmentRequestDto
    {
        $this->ensureAuthenticated();

        $shipFromContactPhone = $order->shipper_phone ?: '0000000000';
        $shipFromCity = $order->shipper_city;
        $deliveryDate = Carbon::parse($order->date)->isFuture()
            ? Carbon::parse($order->date)->format('Y-m-d')
            : now()->addDay()->format('Y-m-d');

        $data = [
            'companyId' => '',
            'awb' => $order->order_number,
            'smsNotifications' => true,
            'highValueTask' => false,
            'notReceived' => false,
            'customerId' => '47',
            'type' => 'DELIVERY',
            'deliveryType' => 'EXPRESS',
            'deliveryDate' => $deliveryDate,
            'deliveryStartTime' => now()->format('H:i'),
            'deliveryEndTime' => '19:00',
            'shipFrom' => [
                'name' => $order->shipper_name,
                'addressLine1' => $order->shipper_address_line,
                'city' => $shipFromCity, // Fallback for city
                'countryCode' => $order->shipperCountry->code_country,
                'contactPhone' => $shipFromContactPhone,
                'district' => $order->shipper_city,
            ],
            'consignee' => [
                'name' => "{$order->receiver_first_name} {$order->receiver_last_name}",
                'location' => [
                    'addressLine1' => $order->receiver_address_line,
                    'district' => $order->getReceiverCityWithFallback(CourierIdentifierEnum::ARAMEX->value),
                    'city' => $order->getReceiverCityWithFallback(CourierIdentifierEnum::ARAMEX->value),
                    'countryCode' => $order->receiverCountry->code_country,
                    'contactPhone' => $order->receiver_phone ?: '0000000000',
                ],
            ],
            'totalShipmentQuantity' => 1, // Static value
            'notes' => $order->description ?: 'No description provided',
            'remoteArea' => false, // Static value
            'codAmount' => $order->isCod() ? $order->order_grand_total : 0,
        ];
        $url = "{$this->baseUrl}/tasks";
        $headers = $this->headers();

        return ShipmentRequestDto::create($url, $data, $headers);
    }

    /**
     * @throws \Exception
     */
    public function createReverseShipment(Order $order): array
    {
        $this->ensureAuthenticated();

        $shipFromContactPhone = $order->shipper_phone ?: '0000000000';
        $shipFromCity = $order->shipper_city;
        $deliveryDate = Carbon::parse($order->date)->isFuture()
            ? Carbon::parse($order->date)->format('Y-m-d')
            : now()->addDay()->format('Y-m-d');

        $data = [
            'companyId' => '', // Static value
            'awb' => $order->order_number.'-RR',
            'smsNotifications' => true,
            'highValueTask' => false,
            'notReceived' => false,
            'customerId' => '47',
            'type' => 'DELIVERY',
            'deliveryType' => 'EXPRESS',
            'deliveryDate' => $deliveryDate,
            'deliveryStartTime' => now()->format('H:i'),
            'deliveryEndTime' => '19:00',
            'shipFrom' => [
                'name' => "{$order->receiver_first_name} {$order->receiver_last_name}",
                'addressLine1' => $order->receiver_address_line,
                'city' => $order->getReceiverCityWithFallback(CourierIdentifierEnum::ARAMEX->value),
                'countryCode' => $order->receiver_country_code,
                'contactPhone' => $shipFromContactPhone,
                'district' => $order->getReceiverCityWithFallback(CourierIdentifierEnum::ARAMEX->value),
            ],
            'consignee' => [
                'name' => $order->shipper_name,
                'location' => [
                    'addressLine1' => $order->shipper_address_line,
                    'district' => $order->getReceiverCityWithFallback(CourierIdentifierEnum::ARAMEX->value),
                    'city' => $shipFromCity,
                    'countryCode' => $order->shipperCountry->code_country,
                    'contactPhone' => $shipFromContactPhone,
                ],
            ],
            'totalShipmentQuantity' => 1, // Static value
            'notes' => $order->description ?: 'No description provided',
            'remoteArea' => false, // Static value
            'codAmount' => 0,
        ];

        $url = "{$this->baseUrl}/tasks";

        $response = $this->httpClient()
            ->post($url, $data)
            ->json();

        return $response;
    }

    //    /**
    //     * @return string[]
    //     *
    //     * @throws ConnectionException
    //     * @throws \Exception
    //     */
    //    public function updateTransactionStatus($transactionId, array $data): array
    //    {
    //        $this->ensureAuthenticated();
    //
    //        $url = "{$this->baseUrl}/tasks/{$transactionId}";
    //
    //        return $this->httpClient()
    //            ->withBody(json_encode($data), 'application/merge-patch+json')
    //            ->patch($url)
    //            ->json();
    //    }

    //    /**
    //     * @throws ConnectionException
    //     * @throws \Exception
    //     */
    //    public function getShipmentPackages()
    //    {
    //        $this->ensureAuthenticated();
    //
    //        $order = DB::table('orders')->whereNotNull('external_id')->first();
    //
    //        if (! $order || ! $order->external_id) {
    //            throw new \Exception('No orders with a valid external_id found.');
    //        }
    //
    //        $taskId = $order->external_id;
    //
    //        $url = "{$this->baseUrl}/tasks/{$taskId}/shipment-packages";
    //
    //        return $this->httpClient()
    //            ->get($url)
    //            ->json();
    //    }

    public function printLabel(Order $order): ShipmentRequestDto
    {
        $this->ensureAuthenticated();

        // Validate that the order has a shipment_external_id
        if (! $order->shipment_external_id) {
            throw new Exception("TranscorpSB printLabel: Order {$order->id} does not have a shipment_external_id. Cannot generate label.");
        }

        // Ensure we have a valid token
        if (! $this->token) {
            $jsonMessage = json_encode([
                'error' => 'TranscorpSB printLabel: Authentication token is missing',
                'order_id' => $order->id,
                'order_number' => $order->order_number,
            ], JSON_UNESCAPED_UNICODE);
            if ($jsonMessage === false) {
                throw new Exception('JSON encode failed: '.json_last_error_msg());
            }
            throw new Exception($jsonMessage);
        }

        // Construct the URL for the label
        $clientId = config('services.transcorpsb.clientid');
        $url = 'https://shipment-label.suitefleet.com/generate-label';
        $queryParams = [
            'taskId' => $order->shipment_external_id,
            'type' => 'big',
            'tz_offset' => 1,
            'token' => $this->token,
            'clientId' => $clientId,
        ];

        $urlWithParams = $url.'?'.http_build_query($queryParams);

        // Log the request for debugging
        Log::info('TranscorpSB printLabel request', [
            'order_id' => $order->id,
            'shipment_external_id' => $order->shipment_external_id,
            'url' => $urlWithParams,
            'token_present' => ! empty($this->token),
        ]);

        // For label generation, we don't need additional headers since token is in URL
        // Remove Authorization header to avoid conflicts
        $headers = [
            'Clientid' => (string) config('services.transcorpsb.clientid'),
        ];

        return ShipmentRequestDto::create($urlWithParams, [], $this->headers(), 'GET');
    }

    public function extractPrintLabelUrl($response, ?Order $order = null): string
    {
        // If a URL string is provided, download the PDF and process it
        if (is_string($response) && filter_var($response, FILTER_VALIDATE_URL)) {
            $pdfContent = @file_get_contents($response);
            if ($pdfContent === false) {
                $jsonMessage = json_encode([
                    'error' => 'Failed to download PDF from URL',
                    'url' => $response,
                    'order_id' => $order ? $order->id : 'unknown',
                ], JSON_UNESCAPED_UNICODE);
                if ($jsonMessage === false) {
                    throw new Exception('JSON encode failed: '.json_last_error_msg());
                }
                throw new Exception($jsonMessage);
            }
            $originalFileName = "airwaybill_{$order->shipment_reference}.pdf";
            $modifiedFileName = "airwaybill_modified_{$order->shipment_reference}.pdf";
            Storage::disk('public')->put($originalFileName, $pdfContent);
            $originalPath = Storage::disk('public')->path($originalFileName);
            $modifiedPath = Storage::disk('public')->path($modifiedFileName);

            $this->removeSecondPage($originalPath, $modifiedPath);

            return Storage::disk('public')->url($modifiedFileName);
        }

        // Handle HTTP response object with binary PDF data
        if ($response instanceof Response) {
            if ($response->successful()) {
                $originalFileName = "airwaybill_{$order->shipment_reference}.pdf";
                $modifiedFileName = "airwaybill_modified_{$order->shipment_reference}.pdf";
                Storage::disk('public')->put($originalFileName, $response->body());
                $originalPath = Storage::disk('public')->path($originalFileName);
                $modifiedPath = Storage::disk('public')->path($modifiedFileName);

                $this->removeSecondPage($originalPath, $modifiedPath);

                return Storage::disk('public')->url($modifiedFileName);
            } else {
                // Better error handling for failed responses
                $jsonMessage = json_encode([
                    'error' => 'TranscorpSB label generation failed',
                    'order_id' => $order->id,
                    'shipment_external_id' => $order->shipment_external_id,
                    'status_code' => $response->status(),
                    'response_body' => $response->body(),
                ], JSON_UNESCAPED_UNICODE);
                if ($jsonMessage === false) {
                    throw new Exception('JSON encode failed: '.json_last_error_msg());
                }
                throw new Exception($jsonMessage);
            }
        }

        $jsonMessage = json_encode([
            'error' => 'TranscorpSB extractPrintLabelUrl: Invalid response object provided',
            'order_id' => $order ? $order->id : 'unknown',
            'response_type' => gettype($response),
        ], JSON_UNESCAPED_UNICODE);
        if ($jsonMessage === false) {
            throw new Exception('JSON encode failed: '.json_last_error_msg());
        }
        throw new Exception($jsonMessage);
    }

    public function removeSecondPage(string $inputFilePath, string $outputFilePath): void
    {
        $pdf = new Fpdi;
        $pageCount = $pdf->setSourceFile($inputFilePath);

        for ($i = 1; $i <= $pageCount; $i++) {
            if ($i === 2) {
                continue; // Skip the second page
            }

            $templateId = $pdf->importPage($i);

            $size = $pdf->getTemplateSize($templateId);

            // Ensure $size is an array and contains the required keys
            if (! is_array($size) || ! isset($size['orientation'], $size['width'], $size['height'])) {
                $jsonMessage = json_encode([
                    'error' => 'Failed to retrieve valid size information for PDF page',
                    'page_number' => $i,
                    'input_file_path' => $inputFilePath,
                    'size_info' => $size,
                ], JSON_UNESCAPED_UNICODE);
                if ($jsonMessage === false) {
                    throw new Exception('JSON encode failed: '.json_last_error_msg());
                }
                throw new Exception($jsonMessage);
            }

            $pdf->AddPage($size['orientation'], [$size['width'], $size['height']]);
            $pdf->useTemplate($templateId);
        }

        $pdf->Output($outputFilePath, 'F');
    }

    //    /**
    //     * @throws \Exception
    //     */
    //    public function updateStatusesFromApi(): string
    //    {
    //        $this->ensureAuthenticated();
    //
    //        $url = "{$this->baseUrl}/tasks";
    //        $params = [
    //            'page' => 0,
    //            'size' => 50,
    //        ];
    //
    //        try {
    //            // Log the request for debugging
    //            Log::info('Fetching tasks from API', ['url' => $url, 'params' => $params]);
    //
    //            $response = $this->httpClient()->get($url, $params);
    //
    //            if ($response->failed()) {
    //                Log::error('API Error Response', ['response' => $response->body()]);
    //                throw new \Exception('Failed to fetch tasks from API: '.$response->body());
    //            }
    //
    //            $tasks = $response->json()['content'] ?? [];
    //
    //            if (empty($tasks)) {
    //                throw new \Exception('No tasks found in the API response.');
    //            }
    //
    //            // Process tasks
    //            foreach ($tasks as $task) {
    //                $externalId = $task['id'] ?? null;
    //                $status = $task['status'] ?? null;
    //
    //                if ($externalId && $status) {
    //                    /** @var Order|null $order */
    //                    $order = DB::table('orders')->where('external_id', $externalId)->first();
    //
    //                    if ($order) {
    //                        DB::table('orders')
    //                            ->where('id', $order->id)
    //                            ->update(['status' => $status]);
    //                    }
    //                }
    //            }
    //
    //            return 'Statuses updated successfully.';
    //        } catch (\Exception $e) {
    //            Log::error('Error updating statuses', ['error' => $e->getMessage()]);
    //            throw new \Exception('Failed to update statuses: '.$e->getMessage());
    //        }
    //    }

    /**
     * Calculate the shipping cost for the given order.
     *
     * @return int Calculated shipping cost
     */
    public function getRates(ShippingRateDto $shippingRateDto): int
    {
        if ($shippingRateDto->isInternational) {
            // TODO: FINISH THIS LATER
            return 999999999999;
        }

        // Get pricing configuration from factory
        $factory = app(ShippingServiceFactory::class);
        $pricingConfig = $factory->getPricingConfig(
            CourierIdentifierEnum::TRANSCORP->value,
            $shippingRateDto
        );

        return $this->calculateRateFromConfig($pricingConfig, $shippingRateDto);
    }

    /**
     * Calculate shipping cost from configuration data for rates
     */
    private function calculateRateFromConfig(PricingConfigDto $pricingConfig, ShippingRateDto $shippingRateDto): int
    {
        // Calculate total weight from order boxes
        $totalWeight = ($shippingRateDto->calculateWeightFromBoxes($pricingConfig->volumetricDivisor) * 100);

        $basePrice = $pricingConfig->basePrice;
        $additionalWeightCost = 0;
        $distanceCost = 0;

        // Calculate additional weight cost
        if ($totalWeight > $pricingConfig->extraWeightFrom) {
            $additionalWeight = $totalWeight - $pricingConfig->extraWeightFrom;
            $additionalWeightCost = ($additionalWeight / 100) * $pricingConfig->additionalWeightCost;
        }

        // Add cash on delivery cost if applicable
        if ($shippingRateDto->isCod && ! $shippingRateDto->ignoreCod) {
            $basePrice = $basePrice + $pricingConfig->cashOnDeliveryCost;
        }

        // Calculate distance cost if applicable
        if ($pricingConfig->distanceCost > 0) {
            // TODO: Implement distance calculation logic
            // $distanceCost = $this->calculateDistanceCost($shippingRateDto, $pricingConfig->distanceCost);
        }

        // Calculate the total cost
        return $basePrice + $additionalWeightCost + $distanceCost;
    }

    //    public function refreshToken(): string
    //    {
    //        // TODO: Implement refreshToken() method.
    //    }

    /**
     * @return string[]
     *
     * @throws ConnectionException
     */
    public function trackShipments(array $trackingNumbers): array
    {
        $this->ensureAuthenticated();

        $url = "{$this->baseUrl}/tasks";
        $params = [
            'page' => 0,
            'size' => 50,
        ];

        try {
            // Log the request for debugging
            Log::info('Fetching tasks from API', ['url' => $url, 'params' => $params]);

            $response = $this->httpClient()->get($url, $params);

            if ($response->failed()) {
                Log::error('API Error Response', ['response' => $response->body()]);
                $jsonMessage = json_encode([
                    'error' => 'Failed to fetch tasks from API',
                    'status_code' => $response->status(),
                    'response_body' => $response->body(),
                ], JSON_UNESCAPED_UNICODE);
                if ($jsonMessage === false) {
                    throw new Exception('JSON encode failed: '.json_last_error_msg());
                }
                throw new Exception($jsonMessage);
            }

            $tasks = $response->json()['content'] ?? [];

            if (empty($tasks)) {
                $jsonMessage = json_encode([
                    'error' => 'No tasks found in the API response',
                    'response_content' => $response->json(),
                ], JSON_UNESCAPED_UNICODE);
                if ($jsonMessage === false) {
                    throw new Exception('JSON encode failed: '.json_last_error_msg());
                }
                throw new Exception($jsonMessage);
            }

            // Process tasks
            foreach ($tasks as $task) {
                $externalId = $task['id'] ?? null;
                $status = $task['status'] ?? null;

                if ($externalId && $status) {
                    /** @var Order|null $order */
                    $order = Order::where('external_id', $externalId)->first();

                    if ($order) {
                        $order->updateStatus($status);
                    }
                }
            }
        } catch (\Exception $e) {
            Log::error('Error updating statuses', ['error' => $e->getMessage()]);
            $jsonMessage = json_encode([
                'error' => 'Failed to update statuses',
                'original_error' => $e->getMessage(),
            ], JSON_UNESCAPED_UNICODE);
            if ($jsonMessage === false) {
                throw new Exception('JSON encode failed: '.json_last_error_msg());
            }
            throw new Exception($jsonMessage);
        }

        return [];
    }

    /**
     * @return string[]
     *
     * @throws ConnectionException
     */
    public function trackShipment(string $trackingNumber): array
    {
        $this->ensureAuthenticated();

        $url = "{$this->baseUrl}/tasks/awb/{$trackingNumber}/task-activities";

        return $this->httpClient()
            ->get($url)
            ->json();
    }

    /**
     * @return string[]
     *
     * @throws ConnectionException
     */
    public function cancelShipment(string $orderNumber): array
    {
        $this->ensureAuthenticated();

        $url = "{$this->baseUrl}/tasks/awb/$orderNumber";
        $data = [
            'status' => 'CANCELED',
        ];

        return $this->httpClientForPatch()
            ->patch($url, $data)
            ->json();
    }

    /**
     * Calculate the shipping cost for the given order.
     *
     * @return int Calculated shipping cost
     */
    public function getCosts(ShippingRateDto $shippingRateDto): int
    {
        if ($shippingRateDto->isInternational) {
            // TODO: FINISH THIS LATER
            return 999999999999;
        }

        // Get pricing configuration from factory
        $factory = app(ShippingServiceFactory::class);
        $pricingConfig = $factory->getCostPricingConfig(
            CourierIdentifierEnum::TRANSCORP->value,
            $shippingRateDto
        );

        return $this->calculateCostFromConfig($pricingConfig, $shippingRateDto);
    }

    /**
     * Calculate shipping cost from configuration data for costs
     */
    private function calculateCostFromConfig(PricingConfigDto $pricingConfig, ShippingRateDto $shippingRateDto): int
    {
        // Calculate total weight from order boxes
        $totalWeight = ($shippingRateDto->calculateWeightFromBoxes($pricingConfig->volumetricDivisor) * 100);

        $basePrice = $pricingConfig->basePrice;
        $additionalWeightCost = 0;

        // Calculate additional weight cost
        if ($totalWeight > $pricingConfig->extraWeightFrom) {
            $additionalWeight = $totalWeight - $pricingConfig->extraWeightFrom;
            $additionalWeightCost = ($additionalWeight / 100) * $pricingConfig->additionalWeightCost;
        }

        // Add cash on delivery cost if applicable (VALUE type)
        if ($shippingRateDto->isCod && ! $shippingRateDto->ignoreCod && $pricingConfig->codType === CostCodTypeEnum::VALUE->value) {
            $basePrice = $basePrice + $pricingConfig->cashOnDeliveryCost;
        }

        // Add cash on delivery cost if applicable (PERCENTAGE type)
        if ($shippingRateDto->isCod && ! $shippingRateDto->ignoreCod && $pricingConfig->codType === CostCodTypeEnum::PERCENTAGE->value) {
            $codValue = (int) (($basePrice * $pricingConfig->cashOnDeliveryCost) / 100);
            $basePrice = $basePrice + $codValue;
        }

        // Add additional weight cost to base price
        $basePrice = $basePrice + $additionalWeightCost;

        // Calculate fuel cost
        $fuelCost = ($basePrice * $pricingConfig->fuel) / 100;

        // Calculate the total cost
        return $basePrice + $fuelCost;
    }
}
