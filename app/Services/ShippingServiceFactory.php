<?php

namespace App\Services;

use App\Dto\PricingConfigDto;
use App\Dto\ShippingRateDto;
use App\Interfaces\ShipmentCourierInterface;
use App\Models\Merchant;
use App\Models\Order;
use App\Models\ShipmentCourierService;
use App\Models\User;
use App\Models\Warehouse;

class ShippingServiceFactory
{
    public function __construct(private SettingsService $settingsService) {}

    /**
     * @var array<string, class-string>
     */
    protected array $serviceMap = [
        'aramex' => AramexService::class,
        'barq' => BarqFleetService::class,
        'transcorp' => TranscorpSBService::class,
        'thabit' => AlthabitService::class,
        'jt' => JtExpressService::class,
        'spl' => SPLApiService::class,
    ];

    public function create(string $serviceName, bool $useGlobalConfig, ?Order $order): ShipmentCourierInterface
    {
        if (! isset($this->serviceMap[$serviceName])) {
            throw new \InvalidArgumentException("Service $serviceName not supported.");
        }

        // Resolve the service
        $service = app($this->serviceMap[$serviceName]);

        // Determine the configuration based on the preference
        $config = $this->resolveConfig(serviceName: $serviceName, useGlobalConfig: $useGlobalConfig, order: $order);
        // Inject the configuration into the service
        $service->setConfig($config);

        return $service;
    }

    /**
     * @return string[]
     */
    public function resolveConfig(string $serviceName, bool $useGlobalConfig, ?Order $order): array
    {
        // Get the platform-wide configuration
        $platformConfig = $this->settingsService->getPlatformConfig($serviceName);

        // If the global config is explicitly requested, return it
        if ($useGlobalConfig) {
            return $platformConfig;
        }
        $merchant = Merchant::withoutGlobalScopes()->find($order->merchant_id);
        if (! $merchant) {
            $merchant = Warehouse::withoutGlobalScopes()->findOrFail($order->warehouse_id);
        }
        $user = User::findOrFail($merchant->user_id);

        return $user->getShippingConfig($serviceName);
    }

    /**
     * Get pricing configuration for a service and user
     */
    public function getPricingConfig(string $serviceName, ShippingRateDto $shippingRateDto): PricingConfigDto
    {
        $pricingService = app(UserShipmentPricingService::class);

        return $pricingService->getPricingConfig($shippingRateDto->user, $serviceName);
    }

    /**
     * Get cost pricing configuration for a service and user
     */
    public function getCostPricingConfig(string $serviceName, ShippingRateDto $shippingRateDto): PricingConfigDto
    {
        $pricingService = app(UserShipmentPricingService::class);

        if ($shippingRateDto->user) {
            $pricingConfig = $pricingService->getPricingConfig($shippingRateDto->user, $serviceName);

            // For costs, we need to use cost-specific fields
            $service = ShipmentCourierService::where('identifier', $serviceName)->firstOrFail();

            return new PricingConfigDto(
                basePrice: $service->cost_base_price,
                extraWeightFrom: $service->cost_extra_weight_from,
                additionalWeightCost: $service->cost_additional_weight_cost,
                cashOnDeliveryCost: $service->cost_cash_on_delivery_cost,
                distanceCost: $pricingConfig->distanceCost,
                volumetricDivisor: $pricingConfig->volumetricDivisor,
                fuel: $service->cost_fuel,
                codType: $service->cost_cod_type,
            );
        }

        // Fall back to global pricing
        $service = ShipmentCourierService::where('identifier', $serviceName)->firstOrFail();

        return new PricingConfigDto(
            basePrice: $service->cost_base_price,
            extraWeightFrom: $service->cost_extra_weight_from,
            additionalWeightCost: $service->cost_additional_weight_cost,
            cashOnDeliveryCost: $service->cost_cash_on_delivery_cost,
            distanceCost: $service->distance_cost,
            volumetricDivisor: $service->getVolumetricDivisor(),
            fuel: $service->cost_fuel,
            codType: $service->cost_cod_type,
        );
    }
}
