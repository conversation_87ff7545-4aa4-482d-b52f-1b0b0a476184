<?php

use App\Models\Country;

if (! function_exists('getWeekDays')) {
    /**
     * @return string[]
     */
    function getWeekDays(): array
    {
        return [
            1 => __('translation.sunday_label'),
            2 => __('translation.monday_label'),
            3 => __('translation.tuesday_label'),
            4 => __('translation.wednesday_label'),
            5 => __('translation.thursday_label'),
            6 => __('translation.friday_label'),
            7 => __('translation.saturday_label'),
        ];
    }
}

if (! function_exists('phoneCorrector')) {
    function phoneCorrector(?string $receivedPhone, ?int $correctWithCountry): string
    {
        $country = Country::find($correctWithCountry);
        if (! $country) {
            $country = Country::find(1);
        }
        $phoneCode = ltrim($country->phone_code, '+');
        $expectedLength = (int) $country->phone_number_length;

        $digits = preg_replace('/[^0-9]/', '', $receivedPhone);

        if (str_starts_with($digits, $phoneCode)) {
            $digits = substr($digits, strlen($phoneCode));
        }

        $digits = ltrim($digits, '0');

        $digits = substr($digits, -1 * $expectedLength);

        return '+'.$phoneCode.$digits;
    }
}

if (! function_exists('isPhoneInvalid')) {
    function isPhoneInvalid(?string $phone, ?int $countryId): bool
    {

        if (empty($phone) || empty($countryId)) {
            return true;
        }

        $country = Country::find($countryId);
        $phoneCode = $country->phone_code;
        $expectedLength = $country->phone_number_length;

        if (str_starts_with($phone, $phoneCode)) {
            $afterCode = substr($phone, strlen($phoneCode));

            return strlen($afterCode) !== (int) $expectedLength;
        }

        return true;

    }
}
