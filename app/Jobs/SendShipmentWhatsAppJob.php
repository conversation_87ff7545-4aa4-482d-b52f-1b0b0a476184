<?php

namespace App\Jobs;

use App\Models\Order;
use App\Services\MessagingService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class SendShipmentWhatsAppJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public int $timeout = 30; // 30 seconds timeout

    public int $tries = 3; // Number of attempts

    public int $maxExceptions = 3; // Maximum number of exceptions

    /**
     * Create a new job instance.
     */
    public function __construct(
        public Order $order
    ) {
        //
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            $messagingService = new MessagingService;

            // Get customer phone number
            $customerPhone = $this->order->receiver_phone;

            if (empty($customerPhone)) {
                Log::info("No phone number available for order {$this->order->order_number}, skipping WhatsApp message");

                return;
            }

            // Clean and format phone number (remove any non-numeric characters except +)
            $customerPhone = preg_replace('/[^\d+]/', '', $customerPhone);

            // Create message content
            $message = $this->createShipmentMessage();

            // Send WhatsApp message
            $response = $messagingService->sendMessage('966533112592', $message);

            Log::info("WhatsApp message sent via job for order {$this->order->order_number}", [
                'phone' => $customerPhone,
                'shipment_reference' => $this->order->shipment_reference,
                'response' => $response,
            ]);

        } catch (\Exception $exception) {
            Log::error("WhatsApp job failed for order {$this->order->order_number}: ".$exception->getMessage());
            // Do not re-throw - just log the error and continue
        }
    }

    /**
     * Create message content for regular shipment
     */
    private function createShipmentMessage(): string
    {
        $customerName = $this->order->receiver_first_name.' '.$this->order->receiver_last_name;
        $trackingLink = config('app.url').'/shipment_tracking?trackingNumber='.$this->order->shipment_reference;
        $storeOrWareHouseName = $this->order->warehouse?->name;
        if ($this->order->merchant?->name) {
            $storeOrWareHouseName = $this->order->merchant->name;
        }
        $message = "مرحباً {$customerName}! 📦\n\n";
        $message .= "تم إنشاء شحنتك بنجاح ✅\n";
        $message .= "من متجر : {$storeOrWareHouseName}\n";
        $message .= "رقم الشحنة: {$this->order->shipment_reference}\n";
        $message .= "شركة الشحن: {$this->order->shipment_company}\n\n";
        $message .= "يمكنك تتبع شحنتك من خلال الرابط التالي:\n";
        $message .= "{$trackingLink}\n\n";
        $message .= 'شكراً لثقتكم بنا! 🙏';

        return $message;
    }

    /**
     * Handle a job failure.
     */
    public function failed(\Throwable $exception): void
    {
        Log::error("WhatsApp job failed for order {$this->order->order_number}", [
            'order_id' => $this->order->id,
            'shipment_reference' => $this->order->shipment_reference,
            'customer_phone' => $this->order->receiver_phone,
            'attempts' => $this->attempts(),
            'error' => $exception->getMessage(),
            'error_trace' => $exception->getTraceAsString(),
        ]);
    }

    /**
     * Determine the time at which the job should timeout.
     */
    public function retryUntil(): \DateTime
    {
        return now()->addMinutes(5);
    }
}
