<?php

namespace App\Jobs;

use App\Models\Merchant;
use App\Models\Order;
use App\Services\ShopifyAuthService;
use Filament\Notifications\Notification;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class UpdateShopifyFulfillmentJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public int $timeout = 120; // 2 minutes timeout for Shopify API calls

    public int $tries = 3; // Number of retry attempts

    public int $maxExceptions = 3; // Maximum number of exceptions

    public int $backoff = 30; // Wait 30 seconds between retries

    /**
     * Create a new job instance.
     */
    public function __construct(
        public int $orderId,
        public string $trackingNumber,
        public string $trackingCompany,
        public ?string $trackingUrl = null
    ) {
        // Set queue name based on operation type
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            // Load the order with necessary relationships
            $order = Order::withoutGlobalScopes()->with(['merchant'])->find($this->orderId);

            if (! $order) {
                Log::error('Order not found for Shopify fulfillment job', [
                    'order_id' => $this->orderId,
                    'job_id' => $this->job->getJobId(),
                ]);

                return;
            }

            // Verify this is a Shopify order
            if ($order->source !== 'shopify') {
                Log::warning('Attempted to process non-Shopify order in Shopify fulfillment job', [
                    'order_id' => $this->orderId,
                    'order_source' => $order->source,
                    'job_id' => $this->job->getJobId(),
                ]);

                return;
            }

            // Verify order has external_id
            if (! $order->external_id) {
                Log::warning('Shopify order missing external_id', [
                    'order_id' => $this->orderId,
                    'order_number' => $order->order_number,
                    'job_id' => $this->job->getJobId(),
                ]);

                return;
            }

            // Load merchant
            $merchant = Merchant::withoutGlobalScopes()->find($order->merchant_id);
            if (! $merchant) {
                Log::error('Merchant not found for Shopify fulfillment job', [
                    'order_id' => $this->orderId,
                    'merchant_id' => $order->merchant_id,
                    'job_id' => $this->job->getJobId(),
                ]);

                return;
            }

            // Verify merchant has access token
            if (! $merchant->access_token) {
                Log::error('Merchant missing Shopify access token', [
                    'order_id' => $this->orderId,
                    'merchant_id' => $merchant->id,
                    'job_id' => $this->job->getJobId(),
                ]);

                return;
            }

            Log::info('Processing Shopify fulfillment job', [
                'order_id' => $this->orderId,
                'order_number' => $order->order_number,
                'external_id' => $order->external_id,
                'tracking_number' => $this->trackingNumber,
                'tracking_company' => $this->trackingCompany,
                'attempt' => $this->attempts(),
                'job_id' => $this->job->getJobId(),
            ]);

            // Create Shopify service instance
            $shopifyService = new ShopifyAuthService($merchant);

            // Prepare tracking information
            $trackingInfo = [
                'tracking_number' => $this->trackingNumber,
                'tracking_company' => $this->trackingCompany,
                'tracking_url' => $this->trackingUrl ?? '',
            ];

            // Update Shopify fulfillment
            $shopifyService->updateOrderStatus(
                orderId: $order->external_id,
                trackingInfo: $trackingInfo
            );

            Log::info('Shopify fulfillment job completed successfully', [
                'order_id' => $this->orderId,
                'order_number' => $order->order_number,
                'external_id' => $order->external_id,
                'tracking_number' => $this->trackingNumber,
                'job_id' => $this->job->getJobId(),
            ]);

        } catch (\Exception $exception) {
            Log::error('Shopify fulfillment job failed', [
                'order_id' => $this->orderId,
                'tracking_number' => $this->trackingNumber,
                'tracking_company' => $this->trackingCompany,
                'attempt' => $this->attempts(),
                'max_tries' => $this->tries,
                'error' => $exception->getMessage(),
                'error_trace' => $exception->getTraceAsString(),
                'job_id' => $this->job->getJobId(),
            ]);

            // Re-throw the exception to trigger retry mechanism
            throw $exception;
        }
    }

    /**
     * Handle a job failure.
     */
    public function failed(\Throwable $exception): void
    {
        Log::error('Shopify fulfillment job failed permanently', [
            'order_id' => $this->orderId,
            'tracking_number' => $this->trackingNumber,
            'tracking_company' => $this->trackingCompany,
            'attempts' => $this->attempts(),
            'error' => $exception->getMessage(),
            'error_trace' => $exception->getTraceAsString(),
            'job_id' => $this->job->getJobId(),
        ]);

        // Send failure notification only for non-bulk operations
        try {
            $order = Order::find($this->orderId);
            $orderNumber = $order ? $order->order_number : "ID: {$this->orderId}";

            Notification::make()
                ->title('Shopify Fulfillment Failed')
                ->body("Failed to update fulfillment for order {$orderNumber} after {$this->tries} attempts")
                ->danger()
                ->send();
        } catch (\Exception $e) {
            Log::error('Failed to send failure notification', [
                'order_id' => $this->orderId,
                'notification_error' => $e->getMessage(),
            ]);
        }
    }

    /**
     * Determine the time at which the job should timeout.
     */
    public function retryUntil(): \DateTime
    {
        return now()->addMinutes(10); // Give up after 10 minutes total
    }

    /**
     * Calculate the number of seconds to wait before retrying the job.
     *
     * @return array<int>
     */
    public function backoff(): array
    {
        return [30, 60, 120]; // Wait 30s, then 60s, then 120s between retries
    }

    /**
     * Get the tags that should be assigned to the job.
     *
     * @return array<string>
     */
    public function tags(): array
    {
        return [
            'shopify',
            'fulfillment',
            "order:{$this->orderId}",
            "tracking:{$this->trackingNumber}",
        ];
    }
}
