<?php

namespace App\Livewire;

use App\Enums\OrderStatusEnum;
use App\Enums\SalesChannelEnum;
use App\Models\Merchant;
use App\Models\Order;
use App\Models\User;
use App\Services\InvoiceService;
use App\Services\SalesChannelService;
use App\Services\ShipmentService;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Notifications\Notification;
use Filament\Tables;
use Filament\Tables\Columns\Column;
use Filament\Tables\Concerns\InteractsWithTable;
use Filament\Tables\Contracts\HasTable;
use Illuminate\Contracts\View\View;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Livewire\Component;

class ShipmentCustomRates extends Component implements HasForms, HasTable
{
    use InteractsWithForms;
    use InteractsWithTable;

    public int $step = 1;

    public ?string $pdfUrl = null;

    public ?string $internationalPdfUrl = null;

    /**
     * @var Collection<int,Order>
     */
    public Collection $orders;

    public bool $reverse = false;

    /**
     * @var array<int,Order>
     */
    public array $internationalOrders = [];

    /**
     * @param  Collection<int,Order>  $orders
     */
    public function mount(Collection $orders, bool $reverse = false): void
    {
        $this->orders = $orders;
        $this->reverse = $reverse;
    }

    /**
     * @return Order|Builder<Order>
     */
    protected function getTableQuery(): Order|Builder
    {
        return Order::query()->whereIn('id', $this->orders->pluck('id'));
    }

    /**
     * @return Column[]
     */
    protected function getTableColumns(): array
    {
        return [
            Tables\Columns\TextColumn::make('order_number')
                ->label(__('translation.order_number')),

            Tables\Columns\TextColumn::make('receiver_city')
                ->label('translation.receiver_city')
                ->translateLabel(),
            Tables\Columns\TextColumn::make('shipment_cost')
                ->visible($this->reverse)
                ->default(1)
                ->formatStateUsing(fn ($state) => 1)
                ->label('translation.order_grand_total')
                ->translateLabel(),
            Tables\Columns\TextColumn::make('order_grand_total')
                ->visible(! $this->reverse)
                ->default(1)
                ->formatStateUsing(fn ($state) => 1)
                ->label('translation.order_grand_total')
                ->translateLabel(),
            Tables\Columns\SelectColumn::make('selected_shipment_company')
                ->label(__('translation.shipment_company'))
                ->options(fn () => Auth::user()->shipments()
                    ->with('service')
                    ->get()->pluck('service.courier_name', 'service.identifier')->toArray()
                )
                ->selectablePlaceholder(false)
                ->default(fn ($record) => $record->selected_shipment_company ?? $record->shipment_company),
        ];
    }

    /**
     * @return string[]
     */
    protected function getTableActions(): array
    {
        return [];
    }

    public function render(): View
    {
        return view('livewire.shipment-custom-rates-modal', [
            'step' => $this->step,
            'ordersCount' => $this->orders->count(),
        ]);
    }

    protected function isTablePaginationEnabled(): bool
    {
        return false;
    }

    public function createShipments(): ?RedirectResponse
    {
        if ($this->reverse) {
            $this->createReverseShipments();

            return null;
        }

        $selectedRecords = $this->orders
            ->filter(fn ($order) => $order->status === OrderStatusEnum::PENDING->value);

        if (Auth::user()->shipments()->count() < 1) {
            Notification::make()
                ->title(__('translation.must_relate_shipping_cs_first'))
                ->danger()
                ->send();

            return null;
        }

        if ($selectedRecords->count() > 50) {
            Notification::make()
                ->title(__('translation.select_max_50_orders'))
                ->danger()
                ->send();

            return null;
        }

        $this->internationalOrders = [];
        $shipmentService = new ShipmentService;
        /** @var User $user */
        $user = Auth()->user();
        $shippedOrders = collect();
        $shippedInternationalOrders = collect();
        $failedOrders = [];

        // Use async bulk shipment creation
        try {

            $result = $shipmentService->createBulkShipments(
                orders: $selectedRecords,
                createdWithGlobalConfig: false
            );

            // Batch process successful shipments for wallet operations
            $totalWalletDeduction = 0;
            $walletTransactions = [];

            foreach ($result['successful'] as $success) {
                $order = Order::find($success['order_id']);
                if ($order && $order->status === OrderStatusEnum::AWAITING_PICKUP->value) {
                    // Use user's configurable deduction amount instead of fixed cost
                    $shipmentCost = $user->rate_cost; // User-defined cost amount in cents

                    // Save the applied rate deduction to the order for historical tracking
                    $order->update(['applied_rate_cost' => $user->rate_cost]);

                    // Accumulate wallet deduction instead of individual saves
                    $totalWalletDeduction += $shipmentCost;

                    // Prepare wallet transaction for batch insert
                    $walletTransactions[] = [
                        'user_id' => $user->id,
                        'type' => 'debit',
                        'amount' => $shipmentCost,
                        'description' => 'رسوم شحنة: '.$order->selected_shipment_company,
                        'order_id' => $order->id,
                        'created_at' => now(),
                        'updated_at' => now(),
                    ];

                    $shippedOrders->push($order);

                    if ($order->isInternational()) {
                        $this->internationalOrders[] = $order;
                        $shippedInternationalOrders->push($order);
                    }

                    if ($order->source === SalesChannelEnum::ZID->value) {
                        $merchantId = $order->merchant_id;
                        $merchant = Merchant::withoutGlobalScopes()->findorFail($merchantId);
                        $orderMergedPdfPath = $shipmentService->printLabels(collect([$order]));
                        $salesChannelService = new SalesChannelService($merchant);
                        $salesChannelService->sendLabel($order, Storage::disk('public')->url($orderMergedPdfPath));
                    }
                }
            }

            // Single wallet balance update and batch transaction insert
            if ($totalWalletDeduction > 0) {
                $user->wallet_balance -= $totalWalletDeduction;
                $user->save();

                // Batch insert wallet transactions
                DB::table('wallet_transactions')->insert($walletTransactions);
            }

            // Process failed shipments
            foreach ($result['failed'] as $failure) {
                $failedOrders[] = $failure['order_id'];
            }

        } catch (\Throwable $e) {
            Log::error('Bulk custom rates shipment creation failed: '.$e->getMessage());
            Notification::make()
                ->title(__('translation.shipment_creation_failed'))
                ->danger()
                ->body($e->getMessage())
                ->send();

            return null;
        }

        if ($shippedOrders->isNotEmpty()) {
            $mergedPdfPath = $shipmentService->printLabels($shippedOrders);
            $this->pdfUrl = Storage::disk('public')->url($mergedPdfPath);
        }

        if ($shippedInternationalOrders->isNotEmpty()) {
            $invoiceService = new InvoiceService;
            $proformaPath = $invoiceService->generateProformaInvoice($shippedInternationalOrders);
            $this->internationalPdfUrl = Storage::disk('public')->url($proformaPath);
        }

        $this->step = 2;

        if (! empty($failedOrders)) {
            Notification::make()
                ->title(__('translation.partial_or_failed_shipments'))
                ->danger()
                ->body(__('translation.some_shipments_failed_with_ids').': '.implode(', ', $failedOrders))
                ->send();
        }

        return null;
    }

    public function createReverseShipments(): ?RedirectResponse
    {
        $selectedRecords = $this->orders;
        if ($selectedRecords->count() > 50) {
            Notification::make()
                ->title(__('translation.select_max_50_orders'))
                ->danger()
                ->send();

            return null;
        }

        $shipmentService = new ShipmentService;
        /** @var User $user */
        $user = Auth()->user();
        $shippedOrders = collect();

        try {
            foreach ($selectedRecords as $record) {
                /** @var Order $order */
                $order = Order::findOrFail($record->id);
                $shipmentService->createReverseShipment(order: $order, createdWithGlobalConfig: false, courier: $order->shipment_company);

                // Save the applied rate deduction to the order for historical tracking
                $order->update(['applied_rate_cost' => $user->rate_cost]);

                // Only process wallet transactions if shipment was successful
                $user->decreaseWalletBalance($user->rate_cost);
                $user->save();

                // Record wallet transaction
                $user->walletTransactions()->create([
                    'type' => 'debit',
                    'amount' => $user->rate_cost,
                    'description' => 'رسوم شحنة'.$order->shipment_company,
                    'order_id' => $order->id,
                ]);

                $shippedOrders->push($order);
            }

            // Generate labels after successful reverse shipment creation
            if ($shippedOrders->isNotEmpty()) {
                $mergedPdfPath = $shipmentService->printLabels($shippedOrders);
                $this->pdfUrl = Storage::disk('public')->url($mergedPdfPath);
            }

            $this->step = 2;

            Notification::make()
                ->title(__('translation.reverse_shipment_created_successfully'))
                ->success()
                ->send();

        } catch (\Exception $e) {
            Notification::make()
                ->title(__('translation.failed_reverse_shipment_creation', ['message' => $e->getMessage()]))
                ->danger()
                ->send();

            return null;
        }

        return null;
    }
}
