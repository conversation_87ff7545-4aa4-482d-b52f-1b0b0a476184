<?php

namespace App\Livewire;

use App\Models\Order;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Tables;
use Filament\Tables\Columns\Column;
use Filament\Tables\Concerns\InteractsWithTable;
use Filament\Tables\Contracts\HasTable;
use Illuminate\Contracts\View\View;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;
use Livewire\Component;

class ReverseShipmentCustomRates extends Component implements HasForms, HasTable
{
    use InteractsWithForms;
    use InteractsWithTable;

    /**
     * @var Collection<int,Order>
     */
    public Collection $orders;

    /**
     * @param  Collection<int,Order>  $orders
     */
    public function mount(Collection $orders): void
    {
        $this->orders = $orders;
    }

    /**
     * @return Order|Builder<Order>
     */
    protected function getTableQuery(): Order|Builder
    {
        return Order::query()->whereIn('id', $this->orders->pluck('id'));
    }

    /**
     * @return Column[]
     */
    protected function getTableColumns(): array
    {
        return [
            Tables\Columns\TextColumn::make('order_number')
                ->label(__('translation.order_number')),

            Tables\Columns\TextColumn::make('receiver_city')
                ->label('translation.receiver_city')
                ->translateLabel(),
            Tables\Columns\SelectColumn::make('shipment_company')
                ->label(__('translation.shipment_company'))
                ->options([
                    'aramex' => 'Aramex',
                    'transcorp' => 'Transcorp',
                ]
                )
                ->selectablePlaceholder(false)
                ->default(fn ($record) => $record->shipment_company),

        ];
    }

    /**
     * @return string[]
     */
    protected function getTableActions(): array
    {
        return [];
    }

    public function render(): View
    {
        return view('livewire.table-modal');
    }

    protected function isTablePaginationEnabled(): bool
    {
        return false;
    }
}
