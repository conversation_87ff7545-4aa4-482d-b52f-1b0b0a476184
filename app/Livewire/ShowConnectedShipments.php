<?php

namespace App\Livewire;

use App\Models\User;
use App\Models\UserShipmentCourierService;
use Illuminate\Support\Collection;
use Livewire\Component;

class ShowConnectedShipments extends Component
{
    /**
     * @var Collection<int,UserShipmentCourierService>
     */
    public Collection $shipments;

    public function mount(): void
    {
        /** @var User $user */
        $user = auth()->user();
        $this->shipments = $user->shipments;
    }

    public function render(): \Illuminate\Contracts\View\View|\Illuminate\Contracts\View\Factory|\Illuminate\Foundation\Application
    {
        return view('livewire.show-connected-shipments');
    }
}
