<?php

namespace App\Livewire;

use Livewire\Component;

class ShopifyChannelModal extends Component
{
    public bool $showModal = false;

    /**
     * @var string[]
     */
    protected $listeners = ['open-modal' => 'handleModalEvent'];

    /**
     * @param  array<string, mixed>  $data
     */
    public function handleModalEvent(array $data = []): void
    {
        if (isset($data['id']) && $data['id'] === 'shopify-channel-modal') {
            $this->showModal = true;
        }
    }

    public function render(): \Illuminate\Foundation\Application|\Illuminate\Contracts\View\Factory|\Illuminate\Contracts\View\View
    {
        return view('livewire.shopify-channel-modal');
    }
}
