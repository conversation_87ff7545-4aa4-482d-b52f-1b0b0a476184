<?php

namespace App\Livewire;

use App\Dto\ShippingRateDto;
use App\Enums\CourierIdentifierEnum;
use App\Enums\OrderStatusEnum;
use App\Models\Order;
use App\Models\User;
use App\Services\InvoiceService;
use App\Services\ShipmentService;
use App\Services\ShippingServiceFactory;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Notifications\Notification;
use Filament\Tables;
use Filament\Tables\Columns\Column;
use Filament\Tables\Concerns\InteractsWithTable;
use Filament\Tables\Contracts\HasTable;
use Illuminate\Contracts\View\View;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Livewire\Component;

class ShipmentTreekRates extends Component implements HasForms, HasTable
{
    use InteractsWithForms;
    use InteractsWithTable;

    public int $step = 1;

    /**
     * @var string[]
     */
    protected $listeners = ['company-updated' => 'calculateTotal'];

    public ?string $pdfUrl = null;

    public ?string $internationalPdfUrl = null;

    /**
     * @var Collection<int,Order>
     */
    public Collection $orders;

    public bool $reverse = false;

    public float $totalPrice = 0;

    /**
     * @var array<int,Order>
     */
    public array $internationalOrders = [];

    /**
     * @param  Collection<int,Order>  $orders
     */
    public function mount(Collection $orders, bool $reverse = false): void
    {
        $this->orders = $orders;
        $this->reverse = $reverse;
        $this->calculateTotal();
    }

    public function calculateTotal(): void
    {
        $this->totalPrice = $this->orders->sum(function (Order $order) {
            $order->refresh();
            $factory = app(ShippingServiceFactory::class);
            $serviceInstance = $factory->create($order->selected_shipment_company ?? CourierIdentifierEnum::ARAMEX->value, true, $order);

            return $serviceInstance->getRates(ShippingRateDto::fromOrder($order));
        });
        $this->dispatch('$refresh');

    }

    /**
     * @return Order|Builder<Order>
     */
    protected function getTableQuery(): Order|Builder
    {
        return Order::query()->whereIn('id', $this->orders->pluck('id'));
    }

    /**
     * @return Column[]
     */
    protected function getTableColumns(): array
    {
        return [
            Tables\Columns\TextColumn::make('order_number')
                ->label(__('translation.order_number')),

            Tables\Columns\TextColumn::make('receiverCity.name_ar')
                ->label('translation.city')
                ->translateLabel(),

            Tables\Columns\SelectColumn::make('selected_shipment_company')
                ->label(__('translation.shipment_company'))
                ->default('aramex')
                ->options(fn (Order $record) => $record->getAvailableShipmentCompanies())
                ->placeholder('aramex')
                ->selectablePlaceholder(false)
                ->afterStateUpdated(function ($state, Order $record) {
                    // Check for restrictions before updating
                    $restrictions = $record->getShipmentRestriction($state);
                    if (! empty($restrictions)) {
                        foreach ($restrictions as $restriction) {
                            Notification::make()
                                ->title(__('translation.shipment_company_restriction.'.$restriction.'.title'))
                                ->body(__('translation.shipment_company_restriction.'.$restriction.'.body'))
                                ->danger()
                                ->send();
                        }

                        return;
                    }

                    $record->update(['selected_shipment_company' => $state]);
                    $this->calculateTotal();
                    $this->dispatch('$refresh');
                }),
            Tables\Columns\TextInputColumn::make('add_box')
                ->label(__('translation.add_box'))
                ->type('number')
                ->default(1)
                ->afterStateUpdated(function () {
                    $this->calculateTotal();
                }),
            Tables\Columns\TextColumn::make('price2')
                ->label(__('translation.price'))
                ->visible(! $this->reverse)
                ->getStateUsing(function (Order $record) {
                    $factory = app(ShippingServiceFactory::class);
                    $order = Order::find($record->id);
                    $order->refresh();
                    $serviceInstance = $factory->create($order->selected_shipment_company ?? CourierIdentifierEnum::ARAMEX->value, true, $record);

                    return number_format($serviceInstance->getRates(ShippingRateDto::fromOrder($record)) / 100, 2).' '.__('translation.sar');
                })
                ->translateLabel(),
        ];
    }

    public function render(): View
    {
        return view('livewire.shipment-treek-rates-modal', [
            'total' => number_format($this->totalPrice / 100, 2),
            'step' => $this->step,
        ]);
    }

    protected function isTablePaginationEnabled(): bool
    {
        return false;
    }

    public function createShipments(): ?RedirectResponse
    {
        if ($this->reverse) {
            $this->createReverseShipments();

            return null;
        }

        $selectedRecords = $this->orders
            ->filter(fn ($order) => $order->status === OrderStatusEnum::PENDING->value);

        if ($selectedRecords->count() === 1) {
            $selectedOrderId = $selectedRecords->first()->id;

            return redirect()->route('filament.merchant.pages.price-calculator', [
                'order_id' => $selectedOrderId,
            ]);
        }

        if ($selectedRecords->count() > 50) {
            Notification::make()
                ->title(__('translation.select_max_50_orders'))
                ->danger()
                ->send();

            return null;
        }

        /** @var User $user */
        $user = auth()->user();

        $total = 0;
        foreach ($selectedRecords as $selectedRecord) {
            $factory = app(ShippingServiceFactory::class);
            $serviceInstance = $factory->create(
                $selectedRecord->selected_shipment_company ?? CourierIdentifierEnum::ARAMEX->value,
                true,
                $selectedRecord
            );

            $orderPrice = $serviceInstance->getRates(ShippingRateDto::fromOrder($selectedRecord));
            $total += $orderPrice;
        }

        if ($user->wallet_balance < $total) {
            Notification::make()
                ->title(__('translation.insufficient_wallet_balance'))
                ->danger()
                ->send();

            return null;
        }

        $this->internationalOrders = [];
        $shipmentService = new ShipmentService;
        $shippedOrders = collect();
        $shippedInternationalOrders = collect();
        $failedOrders = [];

        // Use async bulk shipment creation
        try {
            $livewireStart = microtime(true);
            Log::info('LIVEWIRE START: Processing '.$selectedRecords->count().' orders');

            $result = $shipmentService->createBulkShipments(
                orders: $selectedRecords,
                createdWithGlobalConfig: true
            );

            $bulkTime = microtime(true);
            Log::info('LIVEWIRE BULK DONE: '.round(($bulkTime - $livewireStart) * 1000, 2).'ms');

            // Batch process successful shipments for wallet operations
            $totalWalletDeduction = 0;
            $walletTransactions = [];

            foreach ($result['successful'] as $success) {
                $order = Order::find($success['order_id']);
                if ($order && $order->status === OrderStatusEnum::AWAITING_PICKUP->value) {
                    $factory = app(ShippingServiceFactory::class);
                    $serviceInstance = $factory->create(
                        $order->selected_shipment_company,
                        true,
                        $order
                    );

                    $shipmentCost = $serviceInstance->getRates(ShippingRateDto::fromOrder($order));

                    // Accumulate wallet deduction instead of individual saves
                    $totalWalletDeduction += $shipmentCost;

                    // Prepare wallet transaction for batch insert
                    $walletTransactions[] = [
                        'user_id' => $user->id,
                        'type' => 'debit',
                        'amount' => $shipmentCost,
                        'description' => 'رسوم شحنة: '.$order->selected_shipment_company,
                        'order_id' => $order->id,
                        'created_at' => now(),
                        'updated_at' => now(),
                    ];

                    $shippedOrders->push($order);

                    if ($order->isInternational()) {
                        $this->internationalOrders[] = $order;
                        $shippedInternationalOrders->push($order);
                    }
                }
            }

            // Single wallet balance update and batch transaction insert
            if ($totalWalletDeduction > 0) {
                $user->wallet_balance -= $totalWalletDeduction;
                $user->save();

                // Batch insert wallet transactions
                DB::table('wallet_transactions')->insert($walletTransactions);
            }

            // Process failed shipments
            foreach ($result['failed'] as $failure) {
                $failedOrders[] = $failure['order_id'];
                Log::error('Shipment failed for order #'.$failure['order_id'].': '.$failure['error']);
            }

            $postProcessTime = microtime(true);
            Log::info('LIVEWIRE POST-PROCESS: '.round(($postProcessTime - $bulkTime) * 1000, 2).'ms');

        } catch (\Throwable $e) {
            Log::error('Bulk shipment creation failed: '.$e->getMessage());
            Notification::make()
                ->title(__('translation.shipment_creation_failed'))
                ->danger()
                ->body($e->getMessage())
                ->send();

            return null;
        }

        if ($shippedOrders->isNotEmpty()) {
            $mergedPdfPath = $shipmentService->printLabels($shippedOrders);
            $this->pdfUrl = Storage::disk('public')->url($mergedPdfPath);
        }

        if ($shippedInternationalOrders->isNotEmpty()) {
            $invoiceService = new InvoiceService;
            $proformaPath = $invoiceService->generateProformaInvoice($shippedInternationalOrders);
            $this->internationalPdfUrl = Storage::disk('public')->url($proformaPath);
        }

        $this->step = 2;

        if (! empty($failedOrders)) {
            Notification::make()
                ->title(__('translation.partial_or_failed_shipments'))
                ->danger()
                ->body(__('translation.some_shipments_failed_with_ids').': '.implode(', ', $failedOrders))
                ->send();
        }

        return null;
    }

    public function createReverseShipments(): ?RedirectResponse
    {
        $selectedRecords = $this->orders;
        if ($selectedRecords->count() > 1) {
            Notification::make()
                ->title(__('translation.select_max_1_order'))
                ->danger()
                ->send();

            return null;
        }

        $shipmentService = new ShipmentService;
        $record = $selectedRecords->first();
        /** @var Order $order */
        $order = Order::findOrFail($record->id);
        $shipmentService->createReverseShipment(order: $order, createdWithGlobalConfig: true, courier: $order->selected_shipment_company);
        $factory = app(ShippingServiceFactory::class);
        $serviceInstance = $factory->create($order->selected_shipment_company, true, $order);
        $reverseShipmentCost = $serviceInstance->getRates(ShippingRateDto::fromOrder(order: $order, isReverseShipment: true));

        if ($order->status === OrderStatusEnum::RETURNED->value) {

            /** @var User $user */
            $user = Auth()->user();
            $user->decreaseWalletBalance($reverseShipmentCost);
            $user->save();
            // Record wallet transaction
            $user->walletTransactions()->create([
                'type' => 'debit',
                'amount' => $reverseShipmentCost,
                'description' => 'رسوم شحنة عكسية: '.$order->selected_shipment_company,
                'order_id' => $order->id,
            ]);
            /** @var Order $order */
            $order = Order::findOrFail($record->id);
            $shipmentService = new ShipmentService;
            $mergedPdfPath = $shipmentService->printLabels(orders: $selectedRecords);
            $this->pdfUrl = Storage::disk('public')->url($mergedPdfPath);
        }

        $this->step = 2;
        $this->internationalPdfUrl = null;

        return null;
    }

    protected function getTableHeader(): ?View
    {
        return view('livewire.shipment-company-header', [
            'orders' => $this->orders,
        ]);
    }
}
