<?php

namespace App\Livewire;

use App\Enums\OrderHistoryEnum;
use App\Models\OrderHistory;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Tables;
use Filament\Tables\Columns\Column;
use Filament\Tables\Concerns\InteractsWithTable;
use Filament\Tables\Contracts\HasTable;
use Illuminate\Contracts\View\View;
use Illuminate\Database\Eloquent\Builder;
use Livewire\Component;

class TableModal extends Component implements HasForms, HasTable
{
    use InteractsWithForms;
    use InteractsWithTable;

    public ?int $orderId;

    public function mount(int $orderId): void
    {
        $this->orderId = $orderId;
    }

    /**
     * @return Builder<OrderHistory>
     */
    protected function getTableQuery(): Builder
    {
        return OrderHistory::query()->where('order_id', $this->orderId)->orderBy('action_time', 'asc');
    }

    /**
     * @return Column[]
     */
    protected function getTableColumns(): array
    {
        return [
            Tables\Columns\TextColumn::make('action_time')
                ->translateLabel()
                ->label('translation.date')
                ->dateTime('Y-m-d H:m:s'),
            Tables\Columns\ImageColumn::make('shipment_company_logo')
                ->translateLabel()
                ->height(fn ($record) => $record->shipment_company === 'aramex' ? 10 : 60)
                ->label('translation.shipping_company'),
            Tables\Columns\TextColumn::make('shipment_id')
                ->translateLabel()
                ->label('translation.shipping_id'),
            Tables\Columns\TextColumn::make('description')
                ->translateLabel()
                ->label('translation.description'),
            Tables\Columns\TextColumn::make('event_type')
                ->label(__('translation.status'))
                ->badge()
                ->formatStateUsing(fn ($state) => __("translation.order_status.{$state}"))
                ->color(fn ($state) => OrderHistoryEnum::tryFrom($state)?->color() ?? 'gray'),
        ];
    }

    /**
     * @return string[]
     */
    protected function getTableActions(): array
    {
        return [];
    }

    public function render(): View
    {
        return view('livewire.table-modal');
    }

    protected function isTablePaginationEnabled(): bool
    {
        return false;
    }
}
