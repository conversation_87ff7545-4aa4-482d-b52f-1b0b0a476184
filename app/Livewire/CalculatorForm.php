<?php

namespace App\Livewire;

use App\Dto\ShippingRateDto;
use App\Enums\CourierIdentifierEnum;
use App\Models\City;
use App\Models\Country;
use App\Models\Order;
use App\Models\OrderBox;
use App\Services\ShippingServiceFactory;
use Illuminate\Contracts\View\Factory;
use Illuminate\Contracts\View\View;
use Illuminate\Foundation\Application;
use Livewire\Component;

class CalculatorForm extends Component
{
    /**
     * @var array<string, (float|int)>
     */
    public array $prices;

    public int $weight = 0;

    public int $length = 0;

    public int $width = 0;

    public int $height = 0;

    public string $shipper = '';

    public string $receiver = '';

    /**
     * @var array<string, string>
     */
    protected $listeners = [
        'citySelected' => 'updateCity',
    ];

    public function updateCity(string $property, string $value): void
    {
        if ($property === 'shipper') {
            $this->shipper = $value;
        } elseif ($property === 'receiver') {
            $this->receiver = $value;
        }
    }

    public function render(): View|Factory|Application
    {
        return view('livewire.calculator-form');
    }

    public function incrementWeight(): void
    {
        $this->weight++;
    }

    public function decrementWeight(): void
    {
        if ($this->weight > 0) {
            $this->weight--;
        }
    }

    public function incrementLength(): void
    {
        $this->length++;
    }

    public function decrementLength(): void
    {
        if ($this->length > 0) {
            $this->length--;
        }
    }

    public function incrementHeight(): void
    {
        $this->height++;
    }

    public function decrementHeight(): void
    {
        if ($this->height > 0) {
            $this->height--;
        }
    }

    public function incrementWidth(): void
    {
        $this->width++;
    }

    public function decrementWidth(): void
    {
        if ($this->width > 0) {
            $this->width--;
        }
    }

    private function isInternational(): bool
    {
        $receiverCountry_id = City::where('name_ar', $this->receiver)
            ->orWhere('name', $this->receiver)->value('country_id');
        $shipperCity_id = City::where('name_ar', $this->shipper)
            ->orWhere('name', $this->shipper)->value('country_id');

        return $receiverCountry_id !== $shipperCity_id;
    }

    private function getReceiverCountryCode(): string
    {
        $receiverCountry_id = City::where('name_ar', $this->receiver)
            ->orWhere('name', $this->receiver)->value('country_id');

        return Country::find($receiverCountry_id)->code_country ?? 'SA';
    }

    public function calculate(): void
    {
        // Access values from child components
        // Create a new order instance (not saved to the database)
        $order = new Order;
        $order->order_number = '99';
        $order->status = 'calculation';
        $order->date = date('Y-m-d');
        $order->payment_method = 'calculation';
        // Manually initialize the boxes collection
        $order->setRelation('boxes', collect());

        // Create a new order box instance
        $box = new OrderBox;
        $box->weight = $this->weight;
        $box->length = $this->length;
        $box->width = $this->width;
        $box->height = $this->height;

        // Add the box to the collection
        $order->boxes->push($box);
        //        $order->save();
        $factory = app(ShippingServiceFactory::class);
        $aramex = $factory->create(CourierIdentifierEnum::ARAMEX->value, true, $order);
        $barq = $factory->create(CourierIdentifierEnum::BARQ->value, true, $order);
        $transcorp = $factory->create(CourierIdentifierEnum::TRANSCORP->value, true, $order);
        $thabit = $factory->create(CourierIdentifierEnum::THABIT->value, true, $order);
        $jt = $factory->create(CourierIdentifierEnum::JT->value, true, $order);
        $spl = $factory->create(CourierIdentifierEnum::SPL->value, true, $order);
        $boxes = [[
            'width' => $this->width,
            'length' => $this->length,
            'height' => $this->height,
            'weight' => $this->weight,
        ]];
        $shippingRateDto = new ShippingRateDto(
            boxes: $boxes, weight: null,
            fromLocation: $this->shipper,
            toLocation: $this->receiver,
            receiverCountryCode: $this->getReceiverCountryCode(),
            isCod: false,
            ignoreCod: false,
            isInternational: $this->isInternational()
        );
        if ($this->isInternational()) {
            $this->prices = [
                'aramex' => $aramex->getRates($shippingRateDto) / 100,
            ];
        } else {
            $this->prices = [
                'aramex' => $aramex->getRates($shippingRateDto) / 100,
                'barq' => $barq->getRates($shippingRateDto) / 100,
                'transcorp' => $transcorp->getRates($shippingRateDto) / 100,
                'thabit' => $thabit->getRates($shippingRateDto) / 100,
                'jt' => $jt->getRates($shippingRateDto) / 100,
                'spl' => $spl->getRates($shippingRateDto) / 100,
            ];
        }
    }
}
