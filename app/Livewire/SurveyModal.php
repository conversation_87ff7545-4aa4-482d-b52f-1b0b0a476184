<?php

namespace App\Livewire;

use App\Models\SurveyResponse;
use Illuminate\Support\Facades\Auth;
use Livewire\Component;

class SurveyModal extends Component
{
    public int $step = 1;

    public bool $showModal = false;

    public string $ecommerceStore;

    public string $businessIndustry;

    public string $monthlyOrders;

    /**
     * @var array|string[]
     */
    public $industries = [
        'Retail',
        'Health & Beauty',
        'Electronics',
        'Home & Garden',
        'Fashion',
        'Automotive',
        'Food & Beverage',
        'Sports & Outdoors',
    ];

    /**
     * @var array|string[]
     */
    public array $orderOptions = [
        '1-10 orders',
        '11-50 orders',
        '51-100 orders',
        '101-500 orders',
        '500+ orders',
    ];

    /**
     * @var array<string, string>
     */
    protected array $rules = [
        'ecommerceStore' => 'required',
        'businessIndustry' => 'required|string',
        'monthlyOrders' => '',
        'orderPreference' => 'required',
    ];

    public function nextStep(): void
    {
        switch ($this->step) {
            case 1:
                $this->validate([
                    'ecommerceStore' => 'required',
                ]);
                break;
            case 2:
                $this->validate([
                    'businessIndustry' => 'required|string',
                ]);
                break;
            case 3:
                $this->validate([
                    'monthlyOrders' => 'required|string',
                ]);
                $this->submitSurvey(); // Call the submit method in step 3
                break;
        }

        $this->step++;
    }

    public function previousStep(): void
    {
        $this->step--;
    }

    public function mount(): void
    {
        $user = Auth::user();

        // Show the modal if the user does not have a related SurveyResponse
        if ($user && ! $user->survey && $user->role === 'merchant' && $user->email_verified_at !== null) {
            $this->showModal = true;
        }

    }

    public function submitSurvey(): void
    {
        //        $this->validate();

        SurveyResponse::create([
            'user_id' => Auth::id(),
            'has_ecommerce_store' => $this->ecommerceStore ?? true,
            'business_industry' => $this->businessIndustry,
            'monthly_orders' => $this->monthlyOrders,
        ]);

        session()->flash('message', 'Survey submitted successfully!');
        //        $this->reset(); // Clear the form for future use
    }

    public function render(): \Illuminate\Contracts\View\View|\Illuminate\Contracts\View\Factory|\Illuminate\Foundation\Application
    {
        //        $this->dispatch('open-modal', id: 'edit-user');
        return view('livewire.survey-modal');
    }
}
