<?php

namespace App\Livewire;

use App\Models\Country;
use App\Models\User;
use Illuminate\Contracts\View\View;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Auth;
use Livewire\Component;
use Livewire\Features\SupportRedirects\Redirector;

class Register extends Component
{
    public int $currentStep = 1;

    public string $email = '';

    public string $password = '';

    public string $firstName = '';

    public string $lastName = '';

    public string $phoneNumber = '';

    public function mount(): void
    {
        $this->currentStep = $this->currentStep ?? 1;
    }

    public function render(): View
    {
        $currentStep = $this->currentStep;
        $email = $this->email;
        $phoneNumber = $this->phoneNumber;
        $countries = Country::all();

        return view('livewire.register-new', compact('currentStep', 'email', 'phoneNumber', 'countries'));
    }

    public function goToSecondStep(): void
    {
        $this->validate([
            'email' => 'required|email|unique:users,email',
            'password' => 'required|min:6',
        ]);
        $this->currentStep = 2;
    }

    public function goToThirdStep(): void
    {

        $this->validate([
            'firstName' => 'required|string',
            'lastName' => 'required|string',
        ]);
        $this->currentStep = 3;
    }

    public function save(): RedirectResponse|Redirector
    {
        //        $this->validate([
        //            'email' => 'required|email|unique:users,email',
        //            'password' => 'required|min:6',
        //            'firstName' => 'required|string',
        //            'lastName' => 'required|string',
        //        ]);
        User::create([
            'name' => $this->firstName.$this->lastName,
            'email' => $this->email,
            'password' => bcrypt($this->password),
            'dob' => now(),
            'avatar' => '/images/1727995709.png',
        ]);
        $credentials = [
            'email' => $this->email,
            'password' => $this->password,
        ];
        if (Auth::attempt($credentials)) {
            session()->regenerate();

            return redirect()->intended('');
        }

        return redirect()->intended('');
    }
}
