<?php

namespace App\Livewire;

use App\Models\Transaction;
use Filament\Notifications\Notification;
use GuzzleHttp\Client;
use Illuminate\Auth\Access\AuthorizationException;
use Illuminate\Contracts\View\View;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Livewire\Component;
use Livewire\Features\SupportFileUploads\TemporaryUploadedFile;
use Livewire\Features\SupportRedirects\Redirector;
use Livewire\WithFileUploads;

class AccountRechargeWizard extends Component
{
    use WithFileUploads;

    public int $currentStep = 1;

    public ?int $amount = null;

    public ?float $tva = null;

    public ?float $total = null;

    public ?string $baseUrl = null;

    public ?string $checkoutId = null;

    public ?string $selectedTransferMethod = null;

    public ?TemporaryUploadedFile $transferProof = null;

    /**
     * Move to the next step in the wizard.
     */
    public function nextStep(): void
    {

        if ($this->currentStep === 2 && $this->selectedTransferMethod === 'direct') {
            $this->fetchCheckoutId();
        }

        if ($this->currentStep < 3) {
            $this->currentStep++;
        }
    }

    /**
     * Move to the previous step in the wizard.
     */
    public function previousStep(): void
    {
        if ($this->currentStep > 1) {
            $this->currentStep--;
        }
    }

    /**
     * Select the amount and calculate VAT and total.
     */
    public function selectAmount(int $amount): void
    {
        $this->updateAmounts($amount);
    }

    /**
     * Update amounts when the amount field changes.
     */
    public function updatedAmount(null|int|string $value): void
    {
        $this->updateAmounts((int) $value);
    }

    /**
     * Fetch the checkout ID from the Hyperpay API.
     */
    public function fetchCheckoutId(): RedirectResponse|Redirector|null
    {
        $user = Auth::user();
        if (! $user) {
            throw new AuthorizationException;
        }
        if (! $user->companyCity || ! $user->company_name || ! $user->company_commercial_registration_number || ! $user->company_postal_code || ! $user->company_tax_number) {
            Notification::make()
                ->title(__('translation.complete_profile'))
                ->danger()
                ->send();

            return redirect()->route('filament.merchant.auth.profile');
        }
        $settings = $this->getHyperpaySettings();
        if (! $settings) {
            session()->flash('error', 'Payment settings are not configured.');

            return null;
        }
        $this->baseUrl = $settings['baseUrl'];
        $transaction = $this->createTransaction();
        $client = new Client;
        $response = $client->post("{$settings['baseUrl']}/v1/checkouts", [
            'headers' => [
                'Authorization' => "Bearer {$settings['token']}",
                'Content-Type' => 'application/x-www-form-urlencoded',
            ],
            'form_params' => [
                'entityId' => $settings['entityId'],
                'amount' => $this->total,
                'currency' => 'SAR',
                'paymentType' => 'DB',
                'integrity' => 'true',
                'merchantTransactionId' => $transaction->id,
                'customer.email' => $user->email,
                'billing.street1' => $user->company_name,
                'billing.city' => $user->companyCity->name,
                'billing.state' => $user->companyCity->name,
                'billing.country' => 'SA',
                'billing.postcode' => $user->company_postal_code,
                'customer.givenName' => $user->first_name,
                'customer.surname' => $user->last_name,
            ],
        ]);

        $body = json_decode($response->getBody(), true);
        $this->checkoutId = $body['id'] ?? null;

        if (! $this->checkoutId) {
            $jsonMessage = json_encode([
                'error' => 'Failed to initialize payment',
                'user_id' => $user->id,
                'amount' => $this->total,
                'transaction_id' => $transaction->id,
            ], JSON_UNESCAPED_UNICODE);
            if ($jsonMessage === false) {
                throw new \Exception('JSON encode failed: '.json_last_error_msg());
            }
            throw new \Exception($jsonMessage);
        }

        return null;
    }

    /**
     * Render the Livewire component.
     */
    public function render(): View
    {
        return view('livewire.account-recharge-wizard');
    }

    /**
     * Update amounts based on the provided amount.
     */
    private function updateAmounts(int $amount): void
    {
        if ($amount === 0) {
            return;
        }
        $this->amount = $amount;
        $this->tva = round(($amount * 15) / 100, 2);
        $this->total = round($amount + $this->tva, 2);
    }

    /**
     * Get Hyperpay settings from the database.
     *
     * @return array<string,string>|null
     */
    private function getHyperpaySettings(): ?array
    {
        $settings = DB::table('settings')
            ->where('key', 'like', 'hyperpay.%')
            ->pluck('value', 'key')
            ->toArray();

        if (isset($settings['hyperpay.baseUrl'], $settings['hyperpay.entityId'], $settings['hyperpay.token'])) {
            return [
                'baseUrl' => $settings['hyperpay.baseUrl'],
                'entityId' => $settings['hyperpay.entityId'],
                'token' => $settings['hyperpay.token'],
            ];
        }

        return null;
    }

    /**
     * Create a transaction record for the user.
     */
    private function createTransaction(): Transaction
    {
        $transaction = new Transaction;
        $transaction->user_id = (int) Auth::id();
        $transaction->payment_method = 'direct';
        $transaction->save();

        return $transaction;
    }

    public function selectTransferMethod(string $method): void
    {
        $this->selectedTransferMethod = $method;
        // Add any other logic you need here, e.g.,
        // - Storing the selected method in the session.
        // - Showing/hiding other elements based on the selection.
        // - Preparing data for the next step.
        $this->nextStep();

    }

    /**
     * Create a transaction record.
     */
    private function createBankTransferTransaction(string $transferProofPath): Transaction
    {
        // Use your Transaction model.  Make sure it has fillable fields.
        $transaction = Transaction::create([
            'user_id' => Auth::id(),
            'amount' => $this->amount,
            'status' => 'pending',
            'payment_method' => $this->selectedTransferMethod,
            'transfer_proof' => $transferProofPath,
        ]);

        return $transaction;
    }

    public function submitBankTransfer(): void
    {
        // 1. Validate the file upload
        $validatedData = Validator::make(
            ['transferProof' => $this->transferProof],
            [
                'transferProof' => 'required|image|max:2048', // Example: required, image, max 2MB
            ],
            [
                'transferProof.required' => 'Please upload a file.',
                'transferProof.image' => 'The file must be an image.',
                'transferProof.max' => 'The image may not be greater than 2MB.',
            ]
        )->validate();

        // 2. Store the file (using Laravel's storage system)
        $path = $this->transferProof->store('transfer-proofs', 'public');
        if (! $path) {
            $jsonMessage = json_encode([
                'error' => 'Failed to store transfer file',
                'user_id' => Auth::id(),
                'amount' => $this->amount,
            ], JSON_UNESCAPED_UNICODE);
            if ($jsonMessage === false) {
                throw new \Exception('JSON encode failed: '.json_last_error_msg());
            }
            throw new \Exception($jsonMessage);
        }
        // 3.  Do something with the stored file path (e.g., save it to a database)
        //     You'll likely need to create a database table/model to store
        //     the transfer information (user ID, amount, file path, status, etc.)
        //     Example:
        //     Transfer::create([
        //         'user_id' => auth()->id(),
        //         'amount' => $this->amount,
        //         'file_path' => $path,
        //         'status' => 'pending',
        //     ]);

        // 4. Reset for next time and give feedback.
        $this->transferProof = null;
        $this->createBankTransferTransaction($path);
        Notification::make()
            ->title(__('translation.bank_transfer_submitted'))
            ->success()
            ->send();

        $this->dispatch('closeModal');
    }
}
