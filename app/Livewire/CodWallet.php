<?php

namespace App\Livewire;

use App\Models\Order;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Tables;
use Filament\Tables\Columns\Column;
use Filament\Tables\Concerns\InteractsWithTable;
use Filament\Tables\Contracts\HasTable;
use Illuminate\Contracts\View\View;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;
use Livewire\Component;

class CodWallet extends Component implements HasForms, HasTable
{
    use InteractsWithForms;
    use InteractsWithTable;

    /**
     * @var Collection<int,Order>
     */
    public Collection $orders;

    public bool $reverse = false;

    /**
     * @param  Collection<int,Order>  $orders
     */
    public function mount(Collection $orders, bool $reverse = false): void
    {
        $this->orders = $orders;
    }

    /**
     * @return Order|Builder<Order>
     */
    protected function getTableQuery(): Order|Builder
    {
        return Order::query()->whereIn('id', $this->orders->pluck('id'));
    }

    /**
     * @return Column[]
     */
    protected function getTableColumns(): array
    {
        return [
            Tables\Columns\TextColumn::make('order_number')
                ->label(__('translation.order_number'))
                ->copyable(),
            Tables\Columns\TextColumn::make('shipment_reference')
                ->label(__('translation.shipment_reference')),
            Tables\Columns\TextColumn::make('receiverCity.name')
                ->getStateUsing(fn ($record) => app()->getLocale() === 'ar' ? $record->receiverCity?->name_ar : $record->receiverCity?->name)
                ->label('translation.receiver_city')
                ->translateLabel(),
            Tables\Columns\TextColumn::make('order_grand_total')
                ->label(__('translation.order_grand_total'))
                ->formatStateUsing(fn ($state) => number_format($state, 2)),
            Tables\Columns\TextColumn::make('payment_method')
                ->label(__('translation.payment_method'))
                ->formatStateUsing(fn ($state) => trans('translation.'.$state)),
            Tables\Columns\TextColumn::make('created_at')
                ->label(__('translation.created_at')),
        ];
    }

    /**
     * @return string[]
     */
    protected function getTableActions(): array
    {
        return [];
    }

    public function render(): View
    {
        return view('livewire.table-modal');
    }

    protected function isTablePaginationEnabled(): bool
    {
        return false;
    }
}
