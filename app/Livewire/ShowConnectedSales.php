<?php

namespace App\Livewire;

use App\Models\Merchant;
use App\Services\ShopifyAuthService;
use Auth;
use Filament\Notifications\Notification;
use Illuminate\View\View;
use Livewire\Component;

class ShowConnectedSales extends Component
{
    public function render(): View
    {
        $connectedSalesChannels = Merchant::where('user_id', Auth::id())->get();

        return view('livewire.show-connected-sales', compact('connectedSalesChannels'));
    }

    public function toggleStatus(int $id): void
    {
        $salesChannel = Merchant::findOrFail($id);
        $salesChannel->active = ! $salesChannel->active;
        $salesChannel->save();

        Notification::make()
            ->title(__('translation.channel_status_changed'))
            ->success()
            ->send();
    }

    public function setupWebhook(int $id): void
    {
        $record = Merchant::findOrFail($id);

        if (Merchant::where('access_token', $record->access_token)->where('id', '!=', $record->id)->exists()) {
            Notification::make()
                ->title(__('translation.access_token_exist'))
                ->danger()
                ->send();

            return;
        }

        if (Merchant::where('api_key', $record->api_key)->where('id', '!=', $record->id)->exists()) {
            Notification::make()
                ->title(__('translation.api_key_exist'))
                ->danger()
                ->send();

            return;
        }

        if (Merchant::where('api_secret_key', $record->api_secret_key)->where('id', '!=', $record->id)->exists()) {
            Notification::make()
                ->title(__('translation.api_secret_key_exist'))
                ->danger()
                ->send();

            return;
        }

        // Call webhook creation logic
        (new ShopifyAuthService($record))->createWebhooksForMerchant();

        Notification::make()
            ->title(__('translation.linked'))
            ->success()
            ->send();
    }
}
