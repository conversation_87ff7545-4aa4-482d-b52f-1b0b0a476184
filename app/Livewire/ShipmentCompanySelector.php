<?php

namespace App\Livewire;

use App\Models\Order;
use Filament\Notifications\Notification;
use Illuminate\Support\Collection;
use Livewire\Component;

class ShipmentCompanySelector extends Component
{
    public string $selectedCompany;

    /** @var Collection<int,Order> */
    public Collection $orders;

    /**
     * @var string[]
     */
    protected $listeners = ['refresh' => '$refresh'];

    /**
     * @param  Collection<int,Order>  $orders
     * @return void
     */
    public function mount(Collection $orders)
    {
        $this->orders = $orders;
    }

    public function updatedSelectedCompany(string $value): void
    {
        if (! $value) {
            return;
        }

        // Update all orders
        $this->orders->each(function ($order) use ($value) {
            $order->update(['selected_shipment_company' => $value]);
        });

        Notification::make()
            ->title('Carrier updated successfully')
            ->success()
            ->send();

        $this->dispatch('company-updated');
    }

    public function render(): \Illuminate\Contracts\View\View
    {
        return view('livewire.shipment-company-selector', [
            'options' => [
                'aramex' => __('translation.aramex'),
                'jt' => __('translation.jt'),
                'transcorp' => __('translation.transcorp'),
            ],
        ]);
    }
}
