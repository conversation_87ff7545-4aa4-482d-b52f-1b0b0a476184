<?php

namespace App\Console;

use App\Console\Commands\GenerateCodWallet;
use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;

class Kernel extends ConsoleKernel
{
    /**
     * The Artisan commands provided by your application.
     *
     * Do not add a type (`array`) to avoid conflicts with <PERSON><PERSON>.
     */
    // protected $commands = [
    //      GenerateCodWallet::class,  // Ensure this class exists in the correct namespace
    // ];

    /**
     * Define the application's command schedule.
     */
    protected function schedule(Schedule $schedule): void
    {
        // $schedule->command('codwallet:generate')->weekly();
    }

    /**
     * Register the commands for the application.
     */
    protected function commands(): void
    {
        $this->load(__DIR__.'/Commands');

        require base_path('routes/console.php');
    }
}
