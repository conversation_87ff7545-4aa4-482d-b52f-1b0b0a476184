<?php

namespace App\Console\Commands;

use App\Enums\CourierIdentifierEnum;
use App\Enums\OrderStatusEnum;
use App\Models\Merchant;
use App\Models\Order;
use App\Models\OrderHistory;
use App\Models\Scopes\MerchantScope;
use App\Models\Scopes\UserScope;
use App\Services\JtExpressService;
use App\Services\SalesChannelService;
use App\Services\ShippingServiceFactory;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class UpdateJtShipments extends Command
{
    protected $signature = 'shipments:update-jt  {--all} {--year=} {--month=} {--shipment_reference=}';

    protected $description = 'Fetch shipments individually from JtExpress and update order statuses';

    public function __construct()
    {
        parent::__construct();
    }

    public function handle(): void
    {
        $all = $this->option('all');
        $year = $this->option('year');
        $month = $this->option('month');
        $shipmentReference = $this->option('shipment_reference');

        $this->info('Fetching shipments individually from JtExpress...');
        Log::info('Fetching shipments individually from JtExpress...');
        // Fetch all orders with shipment IDs
        $query = Order::withoutGlobalScope(MerchantScope::class)
            ->where('shipment_company', CourierIdentifierEnum::JT->value)
            ->whereNot('status', OrderStatusEnum::RETURNED->value)
            ->whereNotNull('shipment_reference');

        // If specific shipment reference is provided, filter by it
        if ($shipmentReference) {
            $query->where('shipment_reference', $shipmentReference);
        } elseif (! $this->option('all')) {
            if ($month) {
                $year = $year ? (int) $year : now()->year;
                $month = (int) $month;
                $start = Carbon::createFromDate($year, $month, 1)->startOfMonth();
                $end = Carbon::createFromDate($year, $month, 1)->endOfMonth();
                $query->whereBetween('created_at', [$start, $end]);
            } else {
                $query->where('created_at', '>', now()->subDays(30));
            }
        }

        $orders = $query->get();

        $this->info((string) count($orders));

        if ($orders->isEmpty()) {
            $this->warn('No shipments found to update.');

            return;
        }

        try {

            /** @var Order[] $orders */
            foreach ($orders as $order) {
                $shipmentId = $order->shipment_reference;
                if (! $shipmentId) {
                    continue;
                }
                $this->info("Fetching shipment status for Order #{$order->id}, Shipment ID: {$shipmentId}...");

                $factory = app(ShippingServiceFactory::class);
                /** @var JtExpressService $service */
                $service = $factory->create(serviceName: CourierIdentifierEnum::JT->value, useGlobalConfig: $order->createdWithGlobalConfig(), order: $order);
                // Fetch shipment status for the current shipment ID
                $response = $service->trackShipment($shipmentId);

                $responseData = $response['data'] ?? [];

                if (! empty($responseData)) {
                    foreach ($responseData as $trackingResult) {
                        $waybillNumber = $trackingResult['billCode'] ?? null;
                        $trackingEvents = $trackingResult['details'] ? array_reverse($trackingResult['details']) : [];
                        if (empty($trackingEvents)) {
                            $this->warn("No tracking events found for Waybill #{$waybillNumber}.");

                            continue;
                        }

                        foreach ($trackingEvents as $trackingEvent) {
                            $updateCode = $trackingEvent['scanType'] ?? null;
                            $description = $trackingEvent['desc'] ?? null;
                            $location = $trackingEvent['scanNetworkCity'] ?? null;
                            $dateTime = $trackingEvent['scanTime'] ?? null;
                            $comments = $trackingEvent['scanNetworkName'] ?? null;
                            // Check for existing history
                            $orderHistoryAlreadyExist = OrderHistory::where(['shipment_status' => $updateCode, 'shipment_id' => $shipmentId, 'description' => $description, 'action_time' => $dateTime])->first();
                            if ($orderHistoryAlreadyExist) {
                                continue;
                            }

                            // Map JT status to app status
                            try {
                                $appStatus = OrderStatusEnum::fromJtStatus($updateCode)->value;

                                // Special handling for abnormal parcel scan
                                if ($updateCode === 'Abnormal parcel scan') {
                                    $appStatus = OrderStatusEnum::CANCELED->value;
                                }
                            } catch (\Exception $e) {
                                Log::info($trackingEvent);
                                throw $e;
                            }

                            OrderHistory::create([
                                'order_id' => $order->id,
                                'event_type' => $appStatus,
                                'shipment_id' => $shipmentId,
                                'description' => $description,
                                'shipment_status' => $updateCode,
                                'action_time' => $dateTime,
                                'shipment_company' => CourierIdentifierEnum::JT->value,
                                'performed_by' => null,
                                'additional_info' => json_encode([
                                    'location' => $location,
                                    'comments' => $comments,
                                ]),
                            ]);

                            $this->info("Logged history for Order #{$order->id}, Event: {$updateCode} ({$description})");

                        }

                        // Determine final order status
                        $latestEvent = end($trackingEvents) ?? null;
                        $latestKey = array_key_last($trackingEvents);
                        if (! $latestEvent['scanType']) {
                            $this->warn("No status found in tracking events for Order #{$order->id}");

                            continue;
                        }

                        // Only if last event is also first (i.e. key is 0) and abnormal
                        if ($latestKey === 0 && ($latestEvent['scanType'] ?? '') === 'Abnormal parcel scan') {
                            $newStatus = OrderStatusEnum::CANCELED->value;
                        } else {
                            $latestScanType = $latestEvent['scanType'] ?? null;
                            $newStatus = OrderStatusEnum::fromJtStatus($latestScanType)->value;
                        }

                        if ($newStatus === $order->status) {
                            $this->info("Order #{$order->id}: Status unchanged ({$newStatus})");

                            continue;
                        }

                        $order->updateStatus($newStatus);

                        $this->info("Order #{$order->id} updated with status: {$newStatus}");

                        // Update Shopify COD orders to paid if status is delivered
                        if ($newStatus === OrderStatusEnum::DELIVERED->value && $order->isCod() && $order->source === 'shopify' && $order->external_id) {
                            $merchant = Merchant::withoutGlobalScope(UserScope::class)
                                ->where('id', $order->merchant_id)
                                ->first();
                            if ($merchant) {
                                try {
                                    $shopifyService = new \App\Services\ShopifyAuthService($merchant);
                                    $shopifyService->updateOrderToPaid($order->external_id);
                                    $this->info("Order #{$order->id}: Updated Shopify COD order to paid");
                                    Log::info('Updated Shopify COD order to paid via JT shipment update', [
                                        'order_id' => $order->id,
                                        'order_number' => $order->order_number,
                                        'external_id' => $order->external_id,
                                        'merchant_id' => $order->merchant_id,
                                        'shipment_company' => 'jt',
                                    ]);
                                } catch (\Exception $e) {
                                    $this->error("Order #{$order->id}: Failed to update Shopify COD order to paid: {$e->getMessage()}");
                                    Log::error('Failed to update Shopify COD order to paid via JT shipment update', [
                                        'order_id' => $order->id,
                                        'order_number' => $order->order_number,
                                        'external_id' => $order->external_id,
                                        'error' => $e->getMessage(),
                                    ]);
                                }
                            }
                        }

                        $merchant = Merchant::withoutGlobalScope(UserScope::class)
                            ->where('id', $order->merchant_id)
                            ->first();
                        if (! $all && ! $year && ! $month && $merchant) {
                            $salesChannelService = new SalesChannelService($merchant);
                            $salesChannelService->updateStatus($order);
                        }
                    }
                } else {
                    $this->warn("No tracking results returned for Order #{$order->id}, Shipment ID: {$shipmentId}.");
                }
            }
        } catch (\Exception $e) {
            $this->error('Error fetching shipments: '.$e->getMessage());
        }

        $this->info('Shipment statuses updated successfully.');
    }
}
