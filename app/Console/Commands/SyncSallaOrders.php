<?php

namespace App\Console\Commands;

use App\Enums\SalesChannelEnum;
use App\Models\Merchant;
use App\Models\Order;
use App\Services\OrderStatusMapper;
use App\Services\SallaAuthService;
use Illuminate\Console\Command;

class SyncSallaOrders extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:sync-salla-orders';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Sync Salla Orders';

    /**
     * Execute the console command.
     */
    public function handle(): void
    {
        $sallaAuthProvider = new SallaAuthService;
        $merchant = Merchant::latest()->first();
        if (! $merchant) {
            $jsonMessage = json_encode([
                'error' => 'Merchant not found',
                'command' => $this->getName(),
            ], JSON_UNESCAPED_UNICODE);
            if ($jsonMessage === false) {
                throw new \Exception('JSON encode failed: '.json_last_error_msg());
            }
            throw new \Exception($jsonMessage);
        }
        $sallaAuthProvider->forUser($merchant);
        $orders = $sallaAuthProvider->getOrders();
        foreach ($orders as $order) {
            // TODO: CHECK IF THIS ID IS STATIC OR DIFFERENT IN EACH STORE
            if ($order['status']['id'] !== 566146469) {
                continue;
            }
            $existantOrder = Order::where(['external_id' => $order['id']])->first();
            if (! $existantOrder) {
                $this->info('added');
                $createdOrder = Order::create([
                    'external_id' => $order['id'],
                    'order_number' => $order['reference_id'],
                    'order_grand_total' => $order['total']['amount'],
                    'date' => $order['date']['date'],
                    'payment_method_id' => 1,
                    'receiver_first_name' => $order['customer']['first_name'],
                    'receiver_last_name' => $order['customer']['last_name'],
                    'receiver_phone' => $order['customer']['mobile'],
                    'status' => OrderStatusMapper::mapStatus(SalesChannelEnum::SALLA->value, $order['status']['slug']),
                    'merchant_id' => $merchant->id,
                ]);
                $createdOrder->logOrderCreation();
            } else {
                $this->info('updated');
                $this->info($order['status']['name']);
                $existantOrder->updateStatus(OrderStatusMapper::mapStatus(SalesChannelEnum::SALLA->value, $order['status']['slug'])->value);
            }
        }
    }
}
