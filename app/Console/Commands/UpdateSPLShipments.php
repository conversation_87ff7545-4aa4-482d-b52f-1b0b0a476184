<?php

namespace App\Console\Commands;

use App\Models\Order;
use App\Services\SPLApiService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class UpdateSPLShipments extends Command
{
    protected $signature = 'shipments:update-spl {--shipment_reference=}';

    protected $description = 'Fetch shipment from SPL and update the first order status';

    protected SPLApiService $splApiService;

    public function __construct(SPLApiService $splApiService)
    {
        parent::__construct();
        $this->splApiService = $splApiService;
    }

    public function handle(): void
    {
        $shipmentReference = $this->option('shipment_reference');

        Log::info('Fetching a shipment from SPL...');
        $this->info('Fetching a shipment from SPL...');

        // Fetch order by shipment reference if provided, otherwise get first order
        if ($shipmentReference) {
            $order = Order::where('shipment_reference', $shipmentReference)->first();
        } else {
            $order = Order::whereNotNull('shipment_reference')->first();
        }

        if (! $order) {
            $this->warn('No orders found to update.', 'warn');

            return;
        }

        try {
            // Fetch tracking information using a static barcode
            $response = $this->splApiService->getItemEvents();

            if (isset($response['IsSuccess']) && $response['IsSuccess'] === true) {
                $trackingResults = $response['ItemsWithStatus'] ?? [];

                foreach ($trackingResults as $result) {
                    $status = $result['EventCategoryCode'] ?? null;

                    if ($status !== null) {
                        // Map SPL code to the OrderStatusEnum
                        $mappedStatus = \App\Enums\OrderStatusEnum::fromSPLCode($status);

                        // Update the order status with the mapped status
                        $order->updateStatus($mappedStatus->value);

                        $this->error("Order #{$order->id} updated with status: {$mappedStatus->value}");

                        // Update Shopify COD orders to paid if status is delivered
                        if ($mappedStatus->value === \App\Enums\OrderStatusEnum::DELIVERED->value && $order->isCod() && $order->source === 'shopify' && $order->external_id) {
                            $merchant = \App\Models\Merchant::withoutGlobalScope(\App\Models\Scopes\UserScope::class)
                                ->where('id', $order->merchant_id)
                                ->first();
                            if ($merchant) {
                                try {
                                    $shopifyService = new \App\Services\ShopifyAuthService($merchant);
                                    $shopifyService->updateOrderToPaid($order->external_id);
                                    $this->info("Order #{$order->id}: Updated Shopify COD order to paid");
                                    Log::info('Updated Shopify COD order to paid via SPL shipment update', [
                                        'order_id' => $order->id,
                                        'order_number' => $order->order_number,
                                        'external_id' => $order->external_id,
                                        'merchant_id' => $order->merchant_id,
                                        'shipment_company' => 'spl',
                                    ]);
                                } catch (\Exception $e) {
                                    $this->error("Order #{$order->id}: Failed to update Shopify COD order to paid: {$e->getMessage()}");
                                    Log::error('Failed to update Shopify COD order to paid via SPL shipment update', [
                                        'order_id' => $order->id,
                                        'order_number' => $order->order_number,
                                        'external_id' => $order->external_id,
                                        'error' => $e->getMessage(),
                                    ]);
                                }
                            }
                        }
                    }
                }
            } else {
                $this->error('Failed to fetch tracking details from SPL.');
            }
        } catch (\Exception $e) {
            $this->error('Error fetching shipment: '.$e->getMessage());
            Log::error('Error fetching SPL shipment', ['error' => $e->getMessage()]);
        }

        $this->info('Shipment status updated successfully.');
    }
}
