<?php

namespace App\Console\Commands;

use App\Models\Order;
use Illuminate\Console\Command;

class UpdateReceiverCountryToEnglish extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:update-receiver-country-to-english';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle(): void
    {
        // Create a map of incorrect => correct values
        $countryCorrectionMap = [
            '1' => 'SA',
            '5' => 'KW',
            'السعودية' => 'SA',
            'المملكة العربية السعودية' => 'SA',
            'الامارات' => 'AE',
            'الإمارات العربية المتحدة' => 'AE',
            'البحرين' => 'BH',
            'الإمارات' => 'AE',
        ];

        $incorrectValues = array_keys($countryCorrectionMap);

        $orders = Order::withoutGlobalScopes()
            ->whereIn('receiver_country', $incorrectValues)
            ->get();

        foreach ($orders as $order) {
            $currentValue = $order->receiver_country;

            if (isset($countryCorrectionMap[$currentValue])) {
                $newValue = $countryCorrectionMap[$currentValue];

                $order->receiver_country = $newValue;
                $order->timestamps = false;
                $order->save();

                $this->info("Updated order #{$order->order_number}: '{$currentValue}' => '{$newValue}'");
            }
        }
    }
}
