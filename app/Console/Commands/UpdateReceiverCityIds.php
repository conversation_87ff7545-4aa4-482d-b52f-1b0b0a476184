<?php

namespace App\Console\Commands;

use App\Models\City;
use App\Models\Order;
use Illuminate\Console\Command;

class UpdateReceiverCityIds extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:orders:update-receiver-city-id {--only-null}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Update receiver_city_id field for all orders based on the receiver city name';

    /**
     * Execute the console command.
     */
    public function handle(): void
    {
        $this->info('Starting to update orders...');

        // Fetch all orders
        $query = Order::withoutGlobalScopes();

        if ($this->option('only-null')) {
            $query->whereNull('receiver_city_id');
            //            $query->where('id', 11494);
            $this->info('Filtering only orders with null receiver_city_id...');
        }

        $orders = $query->get();

        $this->info("Found {$orders->count()} orders to update.");

        foreach ($orders as $order) {
            $cityId = City::findByString($order->receiver_city);
            if (! $cityId) {
                $this->warn("No matching city found for order #{$order->id} (City: {$order->receiver_city})");

                continue;
            }
            $order->update(['receiver_city_id' => $cityId]);
            $this->info("Updated order #{$order->id} with city ID: {$cityId}");
        }

        $this->info('Order updates completed.');
    }
}
