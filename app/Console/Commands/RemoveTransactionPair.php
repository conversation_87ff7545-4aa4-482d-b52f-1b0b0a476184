<?php

namespace App\Console\Commands;

use App\Models\User;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class RemoveTransactionPair extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'wallet:remove-pair {--dry-run : Show what would be removed without actually removing} {--user-id= : Remove pairs for specific user ID only} {--order-id= : Remove pair for specific order ID only}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Remove cancel and debit transaction pairs for all orders';

    /**
     * Execute the console command.
     */
    public function handle(): void
    {
        $specificUserId = $this->option('user-id');
        $specificOrderId = $this->option('order-id');
        $isDryRun = $this->option('dry-run');

        if ($isDryRun) {
            $this->warn('⚠️  DRY RUN MODE - No changes will be made');
        }

        $this->info('🔍 Finding transaction pairs for all users');
        if ($specificUserId) {
            $this->info("   Filtering for User ID: {$specificUserId}");
        }
        $this->newLine();

        // Find all orders with transactions
        $query = DB::table('wallet_transactions')
            ->whereNotNull('order_id')
            ->select('user_id', 'order_id')
            ->groupBy('user_id', 'order_id');

        if ($specificUserId) {
            $query->where('user_id', $specificUserId);
        }

        $ordersWithPairs = $query->get();

        if ($specificOrderId) {
            $ordersWithPairs = $ordersWithPairs->filter(function ($order) use ($specificOrderId) {
                return $order->order_id == $specificOrderId;
            });
        }

        if ($ordersWithPairs->isEmpty()) {
            $this->warn('⚠️  No orders found with transactions');

            return;
        }

        $this->line("📊 FOUND {$ordersWithPairs->count()} ORDERS WITH TRANSACTIONS:");
        $this->newLine();

        $totalNetEffect = 0;
        $pairsToRemove = [];
        $usersToUpdate = [];

        foreach ($ordersWithPairs as $orderData) {
            $userId = $orderData->user_id;
            $orderId = $orderData->order_id;

            // Get transactions for this order
            $transactions = DB::table('wallet_transactions')
                ->where('user_id', $userId)
                ->where('order_id', $orderId)
                ->select('id', 'type', 'amount', 'description', 'order_id', 'created_at')
                ->orderBy('created_at')
                ->get();

            $cancelTransaction = $transactions->where('type', 'cancel')->first();
            $debitTransaction = $transactions->where('type', 'debit')->first();

            if ($cancelTransaction && $debitTransaction) {
                $netEffect = $cancelTransaction->amount - $debitTransaction->amount;

                $this->line("🎯 USER {$userId}, ORDER {$orderId}:");
                $this->line("   Cancel: ID {$cancelTransaction->id}, Amount: ".number_format($cancelTransaction->amount / 100, 2).' SAR');
                $this->line("   Debit: ID {$debitTransaction->id}, Amount: ".number_format($debitTransaction->amount / 100, 2).' SAR');
                $this->line('   Net effect: '.number_format($netEffect / 100, 2).' SAR');
                $this->newLine();

                $pairsToRemove[] = [
                    'user_id' => $userId,
                    'order_id' => $orderId,
                    'cancel_id' => $cancelTransaction->id,
                    'debit_id' => $debitTransaction->id,
                    'cancel_amount' => $cancelTransaction->amount,
                    'debit_amount' => $debitTransaction->amount,
                    'net_effect' => $netEffect,
                ];

                // Track users that need balance updates
                if (! isset($usersToUpdate[$userId])) {
                    $usersToUpdate[$userId] = 0;
                }
                $usersToUpdate[$userId] += $netEffect;
            }
        }

        if (empty($pairsToRemove)) {
            $this->warn('⚠️  No transaction pairs found to remove');

            return;
        }

        $this->line('📈 SUMMARY:');
        $this->line('   Total pairs to remove: '.count($pairsToRemove));
        $this->line('   Total users affected: '.count($usersToUpdate));

        foreach ($usersToUpdate as $userId => $netEffect) {
            $user = User::find($userId);
            $currentBalance = $user ? $user->wallet_balance : 0;
            $newBalance = $currentBalance - $netEffect;
            $this->line("   User {$userId}: ".number_format($currentBalance / 100, 2).' SAR → '.number_format($newBalance / 100, 2).' SAR (change: '.number_format(-$netEffect / 100, 2).' SAR)');
        }
        $this->newLine();

        if (! $isDryRun) {
            // Confirm the action
            if (! $this->confirm('Are you sure you want to remove '.count($pairsToRemove).' transaction pairs for '.count($usersToUpdate).' users?')) {
                $this->info('❌ Operation cancelled');

                return;
            }

            // Remove the transactions
            $this->line('🗑️  REMOVING TRANSACTION PAIRS...');

            try {
                DB::beginTransaction();

                $removedCount = 0;
                foreach ($pairsToRemove as $pair) {
                    // Remove cancel transaction
                    DB::table('wallet_transactions')->where('id', $pair['cancel_id'])->delete();
                    $this->line("   ✅ Removed cancel transaction ID: {$pair['cancel_id']} (User {$pair['user_id']}, Order {$pair['order_id']})");

                    // Remove debit transaction
                    DB::table('wallet_transactions')->where('id', $pair['debit_id'])->delete();
                    $this->line("   ✅ Removed debit transaction ID: {$pair['debit_id']} (User {$pair['user_id']}, Order {$pair['order_id']})");

                    $removedCount++;
                }

                // Update all affected users' wallet balances
                foreach ($usersToUpdate as $userId => $netEffect) {
                    $user = User::find($userId);
                    if ($user) {
                        $user->wallet_balance -= $netEffect;
                        $user->save();
                        $this->line("   ✅ Updated user {$userId} balance: ".number_format($user->wallet_balance / 100, 2).' SAR');
                    }
                }

                DB::commit();
                $this->info("✅ Successfully removed {$removedCount} transaction pairs for ".count($usersToUpdate).' users!');

            } catch (\Exception $e) {
                DB::rollBack();
                $this->error('❌ Error removing transactions: '.$e->getMessage());
            }
        } else {
            $this->warn('🔧 DRY RUN - Would remove '.count($pairsToRemove).' transaction pairs:');
            foreach ($pairsToRemove as $pair) {
                $this->line("   User {$pair['user_id']}, Order {$pair['order_id']}: Cancel ID {$pair['cancel_id']}, Debit ID {$pair['debit_id']}");
            }
            $this->line('   Would update '.count($usersToUpdate)." users' wallet balances");
        }

        // Show all users with current and projected balances
        $this->showUserBalances($usersToUpdate, $isDryRun);
    }

    /**
     * @param  array<mixed>  $usersToUpdate
     */
    private function showUserBalances(array $usersToUpdate, bool $isDryRun): void
    {
        $this->newLine();
        $this->line('💰 USER WALLET BALANCES:');
        $this->newLine();

        if (empty($usersToUpdate)) {
            $this->line('   No users will be affected by this operation.');

            return;
        }

        // Get all users that will be affected
        $affectedUserIds = array_keys($usersToUpdate);
        $users = User::whereIn('id', $affectedUserIds)->get();

        foreach ($users as $user) {
            $currentBalance = $user->wallet_balance;
            $balanceChange = $usersToUpdate[$user->id];
            $newBalance = $currentBalance - $balanceChange;

            $this->line("   User {$user->id} ({$user->email}):");
            $this->line('      Current balance: '.number_format($currentBalance / 100, 2).' SAR');
            $this->line('      Balance change: '.number_format(-$balanceChange / 100, 2).' SAR');
            $this->line('      '.($isDryRun ? 'Projected' : 'New').' balance: '.number_format($newBalance / 100, 2).' SAR');
            $this->newLine();
        }

        // Also show users with wallet transactions but no changes
        $allUsersWithTransactions = DB::table('wallet_transactions')
            ->select('user_id')
            ->distinct()
            ->pluck('user_id');

        $unaffectedUsers = $allUsersWithTransactions->diff($affectedUserIds);

        if ($unaffectedUsers->count() > 0) {
            $this->line('📊 USERS WITH WALLET TRANSACTIONS (NO CHANGES):');
            $this->newLine();

            $unaffectedUsersList = User::whereIn('id', $unaffectedUsers)->get();
            foreach ($unaffectedUsersList as $user) {
                $this->line("   User {$user->id} ({$user->email}): ".number_format($user->wallet_balance / 100, 2).' SAR');
            }
            $this->newLine();
        }

        // Summary
        $totalCurrentBalance = $users->sum('wallet_balance');
        $totalBalanceChange = array_sum($usersToUpdate);
        $totalNewBalance = $totalCurrentBalance - $totalBalanceChange;

        $this->line('📈 SUMMARY:');
        $this->line('   Total users affected: '.count($usersToUpdate));
        $this->line('   Total current balance: '.number_format($totalCurrentBalance / 100, 2).' SAR');
        $this->line('   Total balance change: '.number_format(-$totalBalanceChange / 100, 2).' SAR');
        $this->line('   Total '.($isDryRun ? 'projected' : 'new').' balance: '.number_format($totalNewBalance / 100, 2).' SAR');
    }
}
