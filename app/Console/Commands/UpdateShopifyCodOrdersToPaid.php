<?php

namespace App\Console\Commands;

use App\Enums\OrderStatusEnum;
use App\Enums\SalesChannelEnum;
use App\Models\Merchant;
use App\Models\Order;
use App\Services\ShopifyAuthService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class UpdateShopifyCodOrdersToPaid extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'shopify:update-cod-orders-to-paid {--dry-run : Run without making actual changes}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Update Shopify orders to paid status for delivered COD orders';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $isDryRun = $this->option('dry-run');

        if ($isDryRun) {
            $this->info('Running in dry-run mode - no changes will be made');
        }

        // Find delivered COD orders from Shopify
        $orders = Order::withoutGlobalScopes()
            ->where('payment_method', 'cod')
            ->where('status', OrderStatusEnum::DELIVERED->value)
            ->where('source', SalesChannelEnum::SHOPIFY->value)
            ->whereNotNull('external_id')
            ->get();

        if ($orders->isEmpty()) {
            $this->info('No delivered COD Shopify orders found to update.');

            return self::SUCCESS;
        }

        $this->info("Found {$orders->count()} delivered COD Shopify orders to process.");

        $successCount = 0;
        $errorCount = 0;
        $skippedCount = 0;

        foreach ($orders as $order) {
            try {
                $merchant = Merchant::withoutGlobalScopes()->find($order->merchant_id);
                // Skip if merchant doesn't have access token
                if (! $merchant || ! $merchant->access_token) {
                    $this->warn("Skipping order #{$order->id} - merchant missing access token");
                    $skippedCount++;

                    continue;
                }

                $this->info("Processing order #{$order->id} ({$order->order_number})");

                if (! $isDryRun) {
                    // Create Shopify service instance
                    $shopifyService = new ShopifyAuthService($merchant);

                    // Update order payment status to paid
                    $shopifyService->updateOrderToPaid($order->external_id);

                    Log::info('Updated Shopify COD order to paid', [
                        'order_id' => $order->id,
                        'order_number' => $order->order_number,
                        'external_id' => $order->external_id,
                        'merchant_id' => $order->merchant_id,
                    ]);
                }

                $successCount++;
                $this->info("✓ Successfully processed order #{$order->id}");

            } catch (\Exception $e) {
                $errorCount++;
                $this->error("✗ Error processing order #{$order->id}: {$e->getMessage()}");

                Log::error('Failed to update Shopify COD order to paid', [
                    'order_id' => $order->id,
                    'order_number' => $order->order_number,
                    'external_id' => $order->external_id,
                    'error' => $e->getMessage(),
                ]);
            }
        }

        // Summary
        $this->newLine();
        $this->info('=== Summary ===');
        $this->info("Total orders processed: {$orders->count()}");
        $this->info("Successful: {$successCount}");
        $this->info("Errors: {$errorCount}");
        $this->info("Skipped: {$skippedCount}");

        if ($isDryRun) {
            $this->info('This was a dry run - no actual changes were made');
        }

        return $errorCount > 0 ? self::FAILURE : self::SUCCESS;
    }
}
