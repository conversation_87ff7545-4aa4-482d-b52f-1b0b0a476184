<?php

namespace App\Console\Commands;

use App\Enums\CourierIdentifierEnum;
use App\Models\Order;
use App\Models\Scopes\MerchantScope;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class DeleteOldOrdersHistories extends Command
{
    protected $signature = 'shipments:delete-old-orders-histories
                            {--company= : Shipment company (e.g., thabit, aramex)}
                            {--all : Include all orders}';

    protected $description = 'Delete all order histories for each order, except the first one';

    public function handle(): void
    {
        $company = $this->option('company');
        $all = $this->option('all');

        if ($company && ! CourierIdentifierEnum::tryFrom($company)) {
            $this->error("Invalid company: {$company}");

            return;
        }

        $orderQuery = Order::withoutGlobalScope(MerchantScope::class)
            ->whereNotNull('shipment_reference');

        if ($company) {
            $orderQuery->where('shipment_company', $company);
        } else {
            $orderQuery->whereIn('shipment_company', array_column(CourierIdentifierEnum::cases(), 'value'));
        }

        if (! $all) {
            $orderQuery->where('created_at', '>', now()->subDays(30));
        }

        $orders = $orderQuery->get();

        if ($orders->isEmpty()) {
            $this->warn('No matching orders found.');

            return;
        }

        $this->info('Found '.$orders->count().' orders. Cleaning up their histories...');

        $totalDeleted = 0;

        foreach ($orders as $order) {
            $deleted = $order->orderHistories()->where('event_type', '<>', 'new')->delete();

            $totalDeleted += $deleted;

            $this->line("Order #{$order->id} deleted {$deleted} others.");
        }

        $this->info("Done. Total deleted history records: {$totalDeleted}");
        Log::info("Deleted {$totalDeleted} order_histories via command.");
    }
}
