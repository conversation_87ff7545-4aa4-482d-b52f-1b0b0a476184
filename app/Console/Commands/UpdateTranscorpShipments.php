<?php

namespace App\Console\Commands;

use App\Enums\CourierIdentifierEnum;
use App\Enums\OrderStatusEnum;
use App\Models\Merchant;
use App\Models\Order;
use App\Models\OrderHistory;
use App\Models\Scopes\MerchantScope;
use App\Models\Scopes\UserScope;
use App\Services\SalesChannelService;
use App\Services\ShippingServiceFactory;
use App\Services\TranscorpSBService;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class UpdateTranscorpShipments extends Command
{
    protected $signature = 'shipments:update-transcorp {--all} {--year=} {--month=} {--shipment_reference=}';

    protected $description = 'Fetch shipments individually from TRANSCORP and update order statuses';

    public function __construct()
    {
        parent::__construct();
    }

    public function handle(): void
    {
        $all = $this->option('all');
        $year = $this->option('year');
        $month = $this->option('month');
        $shipmentReference = $this->option('shipment_reference');

        $this->info('Fetching shipments individually from TRANSCORP...');
        Log::info('Fetching shipments individually from TRANSCORP...');
        // Fetch all orders with shipment IDs
        $query = Order::withoutGlobalScope(MerchantScope::class)
            ->where('shipment_company', CourierIdentifierEnum::TRANSCORP->value)
            ->whereNot('status', OrderStatusEnum::RETURNED->value)
            ->whereNotNull('shipment_reference');

        // If specific shipment reference is provided, filter by it
        if ($shipmentReference) {
            $query->where('shipment_reference', $shipmentReference);
        } elseif (! $this->option('all')) {
            if ($month) {
                $year = $year ? (int) $year : now()->year;
                $month = (int) $month;
                $start = Carbon::createFromDate($year, $month, 1)->startOfMonth();
                $end = Carbon::createFromDate($year, $month, 1)->endOfMonth();
                $query->whereBetween('created_at', [$start, $end]);
            } else {
                $query->where('created_at', '>', now()->subDays(30));
            }
        }

        $orders = $query->get();
        $this->info((string) count($orders));
        if ($orders->isEmpty()) {
            $this->warn('No shipments found to update.');

            return;
        }
        //        dd((string) count($orders));
        try {
            /** @var Order[] $orders */
            foreach ($orders as $order) {
                $shipmentId = $order->shipment_reference;
                if (! $shipmentId) {
                    continue;
                }
                $this->info("Fetching shipment status for Order #{$order->id}, Shipment ID: {$shipmentId}...");

                $factory = app(ShippingServiceFactory::class);
                /** @var TranscorpSBService $service */
                $service = $factory->create(serviceName: CourierIdentifierEnum::TRANSCORP->value, useGlobalConfig: $order->createdWithGlobalConfig(), order: $order);
                // Fetch shipment status for the current shipment ID
                /** @var array<array{activityType: string, taskStatus?: string, createdDate: string, id?: string}> $trackingEvents */
                $trackingEvents = $service->trackShipment($shipmentId);

                // Process only events with activityType CHANGE_STATUS
                foreach ($trackingEvents as $trackingEvent) {
                    if ($trackingEvent['activityType'] !== 'CHANGE_STATUS') {
                        continue;
                    }

                    $updateCode = $trackingEvent['taskStatus'] ?? null;
                    $description = $trackingEvent['taskStatus'] ?? null;
                    $location = $trackingEvent['taskStatus'] ?? null;
                    $dateTime = $trackingEvent['createdDate'];
                    $id = $trackingEvent['id'] ?? null;

                    if (! $updateCode) {
                        continue;
                    }

                    $orderHistoryAlreadyExist = OrderHistory::where([
                        'shipment_status' => $updateCode,
                        'shipment_id' => $shipmentId,
                        'reference' => $id,
                    ])->first();

                    if ($orderHistoryAlreadyExist) {
                        continue;
                    }

                    if ($description && $dateTime) {
                        try {
                            $appStatus = OrderStatusEnum::fromTranscorpStatus($updateCode);
                        } catch (\Exception $e) {
                            Log::info($trackingEvent);
                            throw $e;
                        }
                        OrderHistory::create([
                            'order_id' => $order->id,
                            'event_type' => $appStatus,
                            'shipment_id' => $shipmentId,
                            'description' => $description,
                            'shipment_status' => $updateCode,
                            'action_time' => $dateTime,
                            'shipment_company' => CourierIdentifierEnum::TRANSCORP->value,
                            'performed_by' => null, // System-generated
                            'additional_info' => json_encode([
                                'location' => $location,
                            ]),
                            'reference' => $id,
                        ]);

                        $this->info("Logged history for Order #{$order->id}, Event: {$updateCode} ({$description})");
                    }

                    $status = $trackingEvent['taskStatus'] ?? null;

                    if ($status === null) {
                        Log::info("{$order->id} is missing a status from transcorp?");

                        continue;
                    }

                    $newStatus = OrderStatusEnum::fromTranscorpStatus($status)->value;

                    if ($newStatus === $order->status) {
                        $this->info("Order #{$order->id}: Status unchanged ({$newStatus})");

                        continue;
                    }

                    // Update the order status
                    // $order->updateStatus($newStatus);
                    $order->update(['status' => $newStatus]);
                    $this->info("Order #{$order->id} updated with status: {$newStatus}");

                    // Update Shopify COD orders to paid if status is delivered
                    if ($newStatus === OrderStatusEnum::DELIVERED->value && $order->isCod() && $order->source === 'shopify' && $order->external_id) {
                        $merchant = Merchant::withoutGlobalScope(UserScope::class)
                            ->where('id', $order->merchant_id)
                            ->first();
                        if ($merchant) {
                            try {
                                $shopifyService = new \App\Services\ShopifyAuthService($merchant);
                                $shopifyService->updateOrderToPaid($order->external_id);
                                $this->info("Order #{$order->id}: Updated Shopify COD order to paid");
                                Log::info('Updated Shopify COD order to paid via Transcorp shipment update', [
                                    'order_id' => $order->id,
                                    'order_number' => $order->order_number,
                                    'external_id' => $order->external_id,
                                    'merchant_id' => $order->merchant_id,
                                    'shipment_company' => 'transcorp',
                                ]);
                            } catch (\Exception $e) {
                                $this->error("Order #{$order->id}: Failed to update Shopify COD order to paid: {$e->getMessage()}");
                                Log::error('Failed to update Shopify COD order to paid via Transcorp shipment update', [
                                    'order_id' => $order->id,
                                    'order_number' => $order->order_number,
                                    'external_id' => $order->external_id,
                                    'error' => $e->getMessage(),
                                ]);
                            }
                        }
                    }

                    $merchant = Merchant::withoutGlobalScope(UserScope::class)
                        ->where('id', $order->merchant_id)
                        ->first();
                    if (! $all && ! $year && ! $month && $merchant) {
                        $salesChannelService = new SalesChannelService($merchant);
                        $salesChannelService->updateStatus($order);
                    }
                }
            }
        } catch (\Exception $e) {
            $this->error('Error fetching shipments: '.$e->getMessage());
        }

        $this->info('Shipment statuses updated successfully.');
    }
}
