<?php

namespace App\Console\Commands;

use App\Dto\ShippingRateDto;
use App\Models\Order;
use App\Services\ShippingServiceFactory;
use Illuminate\Console\Command;

class UpdateShipmentCost extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:update-shipment-cost';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle(): void
    {
        $orders = Order::withoutGlobalScopes()
            ->whereNotNull('shipment_company')
            ->where('shipment_company', '!=', '')
            ->where('shipment_credentials_type', 'application')
            ->get();

        foreach ($orders as $order) {
            $this->updateShipmentCost($order);
        }
        $this->info("✅ Done. Total updated orders: {$this->modifiedCount}");
    }

    public int $modifiedCount = 0;

    public function updateShipmentCost(Order $order): void
    {
        $this->info("Start order {$order->order_number}:");
        $courier = $order->shipment_company;
        $createdWithGlobalConfig = $order->createdWithGlobalConfig();
        $factory = app(ShippingServiceFactory::class);
        $service = $factory->create(serviceName: $courier, useGlobalConfig: $createdWithGlobalConfig, order: $order);
        $shipment_cost = $service->getRates(ShippingRateDto::fromOrder(order: $order));
        if ($order->shipment_cost === $shipment_cost) {
            return;
        }
        $this->modifiedCount++;
        $this->info("Order #{$order->order_number} | Company: {$courier} | Old: {$order->shipment_cost} | New: {$shipment_cost}");
        $order->timestamps = false;
        $order->update([
            'shipment_cost' => $shipment_cost,
        ]);

    }
}
