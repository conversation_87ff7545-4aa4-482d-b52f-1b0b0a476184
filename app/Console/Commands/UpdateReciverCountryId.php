<?php

namespace App\Console\Commands;

use App\Models\Country;
use App\Models\Order;
use Illuminate\Console\Command;

class UpdateReciverCountryId extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:update-receiver-country-id';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Update receiver country id column';

    /**
     * Execute the console command.
     */
    public int $modifiedCount = 0;

    public function handle(): void
    {
        $orders = Order::withoutGlobalScopes()->whereNull('receiver_country_id')->get();
        $this->info("Starting to update receiver_country_id of {$orders->count()} orders...");
        foreach ($orders as $order) {
            $this->updateReceiverCountryId($order);
        }
        $this->info("✅ Done. Total updated orders: {$this->modifiedCount}");
    }

    public function updateReceiverCountryId(Order $order): void
    {
        $countryId = Country::findByString($order->receiver_country, $order->receiver_country_code);
        $oldReceiverCountryId = $order->receiver_country_id;
        if (! $countryId) {
            $this->warn("No matching country found for order #{$order->id} (Country: {$order->receiver_country})");

            return;
        }
        $order->timestamps = false;
        $order->update([
            'receiver_country_id' => $countryId,
        ]);
        $this->modifiedCount++;
        $this->info("Order #{$order->order_number} | Old: {$oldReceiverCountryId} | New: {$countryId}");
    }
}
