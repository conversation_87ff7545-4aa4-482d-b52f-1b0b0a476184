<?php

namespace App\Console\Commands;

use App\Models\City;
use Illuminate\Console\Command;

class ImportOmCities extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:om-cities';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle(): void
    {

        $locations = [
            'Adam' => 'آدم',
            'Al Amrat' => 'العامرات',
            'Al Aqir' => 'العقير',
            'Al Ashkarah' => 'العشيرة',
            'Al Awabi' => 'العوابي',
            'Al Awabi - Barka' => 'العوابي - بركاء',
            'Al Awabi - Muscat' => 'العوابي - مسقط',
            'Al Bidayah' => 'البدية',
            'Al Buraymi' => 'البريمي',
            'Al <PERSON>' => 'الدقم',
            '<PERSON>' => 'الغزير',
            'Al <PERSON>' => 'الحمراء',
            'Al Jadida' => 'الجديدة',
            'Al <PERSON>bil' => 'الكعيل',
            'Al Kamil Wa Al Wafi' => 'الكامل والوافي',
            'Al Kamil Wal Wafi' => 'الكامل والوافي',
            'Al Khaburah' => 'الخابورة',
            'Al Khodh' => 'الخوض',
            'Al Khuwair' => 'الخوير',
            'Al Mazyunah' => 'المزيونة',
            'Al Mudaybi' => 'المديبي',
            'Al Mudhaibi' => 'المضيبي',
            'Al Musanaah' => 'المصنعة',
            'Al Saada' => 'السعادة',
            'Alansab' => 'العنصب',
            'Alhail' => 'الحيل',
            'Aljafnayn' => 'الجفنين',
            'Almaabilah' => 'المعبيلة',
            'Alwajajah' => 'الوجاجة',
            'Amerat' => 'عامرات',
            'As Sib' => 'السيب',
            'As Sulaif' => 'السليف',
            'Awqad' => 'أوقاد',
            'Azaiba' => 'العذيبة',
            'Bahla' => 'بهلا',
            'Barka' => 'بركاء',
            'Bidbid' => 'بدبد',
            'Bidiya' => 'بدية',
            'Bidiyyah' => 'بدية',
            'Bisiyah' => 'بسياء',
            'Bowsher' => 'بوشر',
            'Bukha' => 'بخا',
            'Buraimi' => 'البريمي',
            'Dahariz' => 'دحريز',
            'Dank' => 'دنك',
            'Darsait' => 'درسايت',
            'Dhalkut' => 'ذلقط',
            'Dhank' => 'ذنك',
            'Diba' => 'دبا',
            'Dima Wattayeen' => 'دماء والطائيين',
            'Fahud' => 'فهد',
            'Falaj Al Qabail' => 'فلج القبائل',
            'Fanja' => 'فنجاء',
            'Firq' => 'فرق',
            'Ghala' => 'غلا',
            'Ghubra' => 'غبرة',
            'Haima' => 'هيما',
            'Halban' => 'حلبان',
            'Hamriya' => 'حمريا',
            'Ibra' => 'إبراء',
            'Ibri' => 'عبري',
            'Izki' => 'إزكي',
            'Jaalan Bani Bu Ali' => 'جعلان بني بو علي',
            'Jaalan Bani Bu Hasan' => 'جعلان بني بو حسن',
            'Jaalan Ni Bu Hassan' => 'جعلان ني بو حسن',
            'Jabal Akhdar' => 'الجبل الأخضر',
            'Jibroo' => 'جبرو',
            'Khabourah' => 'الخابورة',
            'Khadra' => 'الخضراء',
            'Khasab' => 'خصب',
            'Khatmat Malaha' => 'خطمة ملاحة',
            'Liwa' => 'لوى',
            'Lizugh' => 'لزغ',
            'Madha' => 'مدحاء',
            'Madinat Sultan Qaboos' => 'مدينة السلطان قابوس',
            'Mahdha' => 'محضة',
            'Mahout' => 'محوت',
            'Majis' => 'ماجس',
            'Manah' => 'منح',
            'Marmul' => 'مرمول',
            'Masirah' => 'مصيرة',
            'Matrah' => 'مطرح',
            'Mawaleh' => 'موالح',
            'Mirbat' => 'مرباط',
            'Misfah' => 'مصفاة',
            'Muladdah' => 'ملدة',
            'Musannah' => 'المصنعة',
            'Muscat' => 'مسقط',
            'Muttrah' => 'مطرح',
            'Nakhl' => 'نخل',
            'Nizwa' => 'نزوى',
            'Qantab' => 'قنتب',
            'Qatana' => 'قطنة',
            'Qurayat' => 'قريات',
            'Qurum' => 'قرم',
            'Ras Alhadd' => 'رأس الحد',
            'Raysut' => 'ريسوت',
            'Rumais' => 'رميس',
            'Rusayl' => 'رسيل',
            'Rustaq' => 'الرستاق',
            'Ruwi' => 'روي',
            'Sadah' => 'سدح',
            'Sahalnoot' => 'سهلنوت',
            'Saham' => 'صحار',
            'Salalah' => 'صلالة',
            'Salalah Port' => 'ميناء صلالة',
            'Samad Al Shan' => 'صمد الشان',
            'Samayil' => 'سمائل',
            'Seeb' => 'السيب',
            'Shinas' => 'شناص',
            'Sidab' => 'سداب',
            'Sinaw' => 'سنو',
            'Sohar' => 'صحار',
            'Suhar' => 'صحار',
            'Sunaina' => 'سنينة',
            'Sur' => 'صور',
            'Suwaiq' => 'السويق',
            'Taqah' => 'طاقة',
            'Thamrait' => 'ثمريت',
            'Tharmad' => 'ثرمد',
            'The Wave' => 'الموجة',
            'Tiwi' => 'تيوي',
            'Wadi Adai' => 'وادي عدي',
            'Wadi Al Maawil' => 'وادي المعاول',
            'Wadi Aljizi' => 'وادي الجزي',
            'Wadi Bani Khalid' => 'وادي بني خالد',
            'Wadi Hibi' => 'وادي حبي',
            'Wadi Kabir' => 'وادي كبير',
            'Wattaya' => 'وطاية',
            'Wudam Al Sahil' => 'ودم الساحل',
            'Yankul' => 'ينقل',
            'Yiti' => 'يتي',
        ];

        foreach ($locations as $key => $city) {
            City::updateOrCreate(
                ['name' => $key],
                [
                    'name' => $key,
                    'name_salla' => $city,
                    'name_aramex' => $key,
                    'name_ar' => $city,
                    'country_id' => 4,
                ]
            );
        }

    }
}
