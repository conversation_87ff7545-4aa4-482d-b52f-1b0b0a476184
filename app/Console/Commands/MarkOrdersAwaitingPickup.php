<?php

namespace App\Console\Commands;

use App\Enums\CourierIdentifierEnum;
use App\Enums\OrderStatusEnum;
use App\Models\Order;
use App\Models\Scopes\MerchantScope;
use Carbon\Carbon;
use Illuminate\Console\Command;

class MarkOrdersAwaitingPickup extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:mark-orders-awaiting-pickup {--all} {--year=} {--month=}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Update orders to AWAITING_PICKUP if only SH014 is found in tracking';

    /**
     * Execute the console command.
     */
    public function handle(): void
    {
        $updatedCount = 0;

        $this->info('🔄 Checking Aramex orders for "SH014" status...');

        $all = $this->option('all');
        $year = $this->option('year');
        $month = $this->option('month');

        $query = Order::withoutGlobalScope(MerchantScope::class)
            ->where('shipment_company', CourierIdentifierEnum::ARAMEX->value)
            ->whereNotIn('status', [OrderStatusEnum::AWAITING_PICKUP->value, OrderStatusEnum::RETURNED->value, OrderStatusEnum::CANCELED->value])
            ->whereNotNull('shipment_reference');

        if (! $all) {
            if ($month) {
                $year = $year ? (int) $year : now()->year;
                $month = (int) $month;

                $start = Carbon::createFromDate($year, $month, 1)->startOfMonth();
                $end = Carbon::createFromDate($year, $month, 1)->endOfMonth();

                $query->whereBetween('created_at', [$start, $end]);
                $this->info("📅 Filtering orders from {$start->toDateString()} to {$end->toDateString()}");
            } else {
                $days = 30;
                $query->where('created_at', '>=', now()->subDays($days));
                $this->info("📅 Filtering orders from the past {$days} days");
            }
        } else {
            $this->info('📅 Processing all orders with Aramex tracking');
        }

        $orders = $query->get();

        $this->info("📦 Found {$orders->count()} orders to process");

        foreach ($orders as $order) {
            $shipmentId = $order->shipment_reference;

            $histories = $order->histories;

            if ($histories->count() > 2 || optional($histories->first())->description !== 'Record created.') {
                continue;
            }

            $oldStatus = $order->status;
            $order->update(['status' => OrderStatusEnum::AWAITING_PICKUP->value]);

            $this->info("✅ Order #{$order->id} updated from {$oldStatus} to {$order->status}.");
            $updatedCount++;
        }

        $this->newLine();
        $this->info('🎉 Finished.');
        $this->info("✅ Updated: {$updatedCount}");
    }
}
