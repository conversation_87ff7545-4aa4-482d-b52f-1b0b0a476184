<?php

namespace App\Console\Commands;

use App\Enums\SalesChannelEnum;
use App\Models\Merchant;
use App\Models\Order;
use App\Models\Scopes\MerchantScope;
use App\Services\ShopifyAuthService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class FixShopifyFulfillments extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:fix-shopify-fulfillments';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Fix Shopify fulfillments for orders that were shipped before the bulk shipment fix';

    /**
     * Execute the console command.
     */
    public function handle(): void
    {
        $processedCount = 0;
        $successCount = 0;
        $errorCount = 0;
        $skippedCount = 0;

        $this->info('🔄 Finding Shopify orders that need fulfillment updates...');

        $orders = Order::withoutGlobalScope(MerchantScope::class)
            ->where('source', SalesChannelEnum::SHOPIFY->value)
            ->whereNotNull('external_id')
            ->whereNotNull('shipment_reference')
            ->whereNotNull('shipment_tracking_link')
            ->whereIn('status', ['awaiting_pickup', 'out_for_delivery', 'delivered', 'returned'])
            ->get();

        $this->info("📦 Found {$orders->count()} Shopify orders to process");

        if ($orders->isEmpty()) {
            $this->info('✅ No orders found that need fixing');

            return;
        }

        $this->newLine();
        $bar = $this->output->createProgressBar($orders->count());
        $bar->start();

        foreach ($orders as $order) {
            $processedCount++;

            try {
                // Skip if no external_id or shipment_reference
                if (empty($order->external_id) || empty($order->shipment_reference)) {
                    $skippedCount++;
                    $bar->advance();

                    continue;
                }

                $merchant = Merchant::withoutGlobalScopes()->findOrFail($order->merchant_id);
                $shopifyService = new ShopifyAuthService($merchant);

                // Create fulfillment with tracking information
                $shopifyService->updateOrderStatus(
                    orderId: $order->external_id,
                    trackingInfo: [
                        'tracking_number' => $order->shipment_reference,
                        'tracking_company' => $order->shipment_company ?? 'Custom',
                        'tracking_url' => $order->shipment_tracking_link ?? '',
                    ]
                );

                $successCount++;

            } catch (\Exception $exception) {
                $errorCount++;
                Log::error('Failed to fix Shopify fulfillment for order: '.$order->id, [
                    'order_id' => $order->id,
                    'order_number' => $order->order_number,
                    'external_id' => $order->external_id,
                    'error' => $exception->getMessage(),
                ]);

                // Show error for significant issues
                if ($this->getOutput()->isVerbose()) {
                    $this->newLine();
                    $this->error("❌ Failed to update order #{$order->id}: ".$exception->getMessage());
                }
            }

            $bar->advance();
        }

        $bar->finish();
        $this->newLine(2);

        // Summary
        $this->info('🎉 Process completed!');
        $this->table(
            ['Metric', 'Count'],
            [
                ['Total Processed', $processedCount],
                ['Successfully Updated', $successCount],
                ['Errors', $errorCount],
                ['Skipped', $skippedCount],
            ]
        );

        if ($errorCount > 0) {
            $this->warn("⚠️ {$errorCount} orders had errors. Check the logs for details.");
        }
    }
}
