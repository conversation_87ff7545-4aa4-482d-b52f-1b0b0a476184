<?php

namespace App\Console\Commands;

use App\Enums\SalesChannelEnum;
use App\Jobs\UpdateShopifyFulfillmentJob;
use App\Models\Order;
use App\Models\Scopes\MerchantScope;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class DispatchShopifyFulfillmentJobs extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'shopify:dispatch-fulfillment-jobs {--debug : Run in debug mode to preview without dispatching jobs}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Dispatch UpdateShopifyFulfillmentJob for orders with Shopify source and shipment references';

    /**
     * Execute the console command.
     */
    public function handle(): void
    {
        $isDebugMode = $this->option('debug');

        if ($isDebugMode) {
            $this->info('🔍 Running in DEBUG mode - no jobs will be dispatched');
        }

        $this->info('🔄 Finding Shopify orders with shipment references...');

        // Find orders with Shopify source and shipment references
        $orders = Order::withoutGlobalScope(MerchantScope::class)
            ->where('source', SalesChannelEnum::SHOPIFY->value)
            ->whereNotNull('shipment_reference')
            ->whereNotNull('external_id')
            ->get();

        $this->info("📦 Found {$orders->count()} Shopify orders with shipment references");

        if ($orders->isEmpty()) {
            $this->info('✅ No orders found that need Shopify fulfillment updates');

            return;
        }

        $processedCount = 0;
        $successCount = 0;
        $errorCount = 0;
        $skippedCount = 0;

        $this->newLine();
        $bar = $this->output->createProgressBar($orders->count());
        $bar->start();

        foreach ($orders as $order) {
            $processedCount++;

            try {
                // Skip if no external_id or shipment_reference
                if (empty($order->external_id) || empty($order->shipment_reference)) {
                    $skippedCount++;
                    $bar->advance();

                    continue;
                }

                // Prepare tracking information
                $trackingNumber = $order->shipment_reference;
                $trackingCompany = $order->shipment_company ?? 'Custom';
                $trackingUrl = $order->shipment_tracking_link ?? null;

                if ($isDebugMode) {
                    // Debug mode: just log what would be done
                    $this->newLine();
                    $this->line("🔍 DEBUG: Would dispatch job for Order #{$order->order_number} (ID: {$order->id})");
                    $this->line("   - External ID: {$order->external_id}");
                    $this->line("   - Tracking Number: {$trackingNumber}");
                    $this->line("   - Tracking Company: {$trackingCompany}");
                    $this->line('   - Tracking URL: '.($trackingUrl ?: 'null'));
                    $this->newLine();
                    $successCount++;
                } else {
                    // Dispatch the job
                    UpdateShopifyFulfillmentJob::dispatch(
                        orderId: $order->id,
                        trackingNumber: $trackingNumber,
                        trackingCompany: $trackingCompany,
                        trackingUrl: $trackingUrl
                    );

                    Log::info('Dispatched Shopify fulfillment job', [
                        'order_id' => $order->id,
                        'order_number' => $order->order_number,
                        'external_id' => $order->external_id,
                        'tracking_number' => $trackingNumber,
                        'tracking_company' => $trackingCompany,
                    ]);

                    $successCount++;
                }

            } catch (\Exception $exception) {
                $errorCount++;
                Log::error('Failed to dispatch Shopify fulfillment job for order: '.$order->id, [
                    'order_id' => $order->id,
                    'order_number' => $order->order_number,
                    'external_id' => $order->external_id,
                    'error' => $exception->getMessage(),
                ]);

                if ($this->getOutput()->isVerbose()) {
                    $this->newLine();
                    $this->error("❌ Failed to dispatch job for order #{$order->order_number}: ".$exception->getMessage());
                }
            }

            $bar->advance();
        }

        $bar->finish();
        $this->newLine(2);

        // Summary
        $this->info('🎉 Process completed!');
        $this->table(
            ['Metric', 'Count'],
            [
                ['Total Processed', $processedCount],
                ['Successfully Dispatched', $successCount],
                ['Errors', $errorCount],
                ['Skipped', $skippedCount],
            ]
        );

        if ($errorCount > 0) {
            $this->warn("⚠️ {$errorCount} orders had errors. Check the logs for details.");
        }

        if ($isDebugMode) {
            $this->info('🔍 This was a debug run. Use --debug=false to actually dispatch jobs.');
        } else {
            $this->info('✅ Jobs have been dispatched to the queue.');
        }
    }
}
