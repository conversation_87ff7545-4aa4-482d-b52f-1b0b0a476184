<?php

namespace App\Console\Commands;

use App\Dto\ShippingRateDto;
use App\Enums\CourierIdentifierEnum;
use App\Enums\OrderStatusEnum;
use App\Models\Merchant;
use App\Models\Order;
use App\Models\OrderHistory;
use App\Models\Scopes\MerchantScope;
use App\Models\Scopes\UserScope;
use App\Models\User;
use App\Models\Warehouse;
use App\Services\AramexService;
use App\Services\SalesChannelService;
use App\Services\ShippingServiceFactory;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class UpdateAramexShipments extends Command
{
    protected $signature = 'shipments:update-aramex {--all} {--year=} {--month=} {--shipment_reference=}';

    protected $description = 'Fetch shipments individually from Aramex and update order statuses';

    public function __construct()
    {
        parent::__construct();
    }

    public function handle(): void
    {
        $all = $this->option('all');
        $year = $this->option('year');
        $month = $this->option('month');
        $shipmentReference = $this->option('shipment_reference');

        $this->info('Fetching shipments individually from Aramex...');
        // Fetch all orders with shipment IDs
        $query = Order::withoutGlobalScope(MerchantScope::class)
            ->where('shipment_company', CourierIdentifierEnum::ARAMEX->value)
            ->whereNotNull('shipment_reference');

        $fromDays = 30;
        if ($shipmentReference) {
            $query->where('shipment_reference', $shipmentReference);
            $selected_order = Order::where('shipment_reference', $shipmentReference)->first();
            $selected_order->orderHistories()->delete();
            $this->info('Order histories deleted.');
            $fromDays = 150;
            $this->info("Filtering by shipment reference: {$shipmentReference}");
        }

        if (! $all) {

            if ($month) {
                $year = $year ? (int) $year : now()->year;
                $month = (int) $month;
                $start = Carbon::createFromDate($year, $month, 1)->startOfMonth();
                $end = Carbon::createFromDate($year, $month, 1)->endOfMonth();
                $query->whereBetween('created_at', [$start, $end]);
            } else {
                $query->where('created_at', '>', now()->subDays($fromDays));
            }
        }
        $query->orderBy('created_at');
        $orders = $query->get();
        $this->info((string) count($orders));

        if ($orders->isEmpty()) {
            $this->warn('No shipments found to update.'.$query->toRawSql());

            return;
        }

        /** @var Order[] $orders */
        foreach ($orders as $order) {
            $shipmentId = $order->shipment_reference;
            if (! $shipmentId) {
                continue;
            }
            $this->info("Fetching shipment status for Order #{$order->id}, Shipment ID: {$shipmentId}...");

            $factory = app(ShippingServiceFactory::class);
            /** @var AramexService $service */
            $service = $factory->create(serviceName: CourierIdentifierEnum::ARAMEX->value, useGlobalConfig: $order->createdWithGlobalConfig(), order: $order);
            // Fetch shipment status for the current shipment ID
            $response = $service->trackShipments([$shipmentId]);
            if (! isset($response['TrackingResults']) || ! is_array($response['TrackingResults'])) {
                $this->warn("No tracking results returned for Order #{$order->id}, Shipment ID: {$shipmentId}.");

                continue;
            }
            foreach ($response['TrackingResults'] as $trackingResult) {
                $waybillNumber = $trackingResult['Key'] ?? null;
                $result = $trackingResult['Value'];
                $result = array_reverse($result);
                if (! is_array($trackingResult['Value'])) {
                    $this->warn("No tracking events found for Waybill #{$waybillNumber}.");

                    continue;
                }
                foreach ($result as $trackingEvent) {
                    $updateCode = $trackingEvent['UpdateCode'] ?? null;
                    $description = $trackingEvent['UpdateDescription'] ?? null;
                    $location = $trackingEvent['UpdateLocation'] ?? null;
                    $dateTime = $this->convertAramexDate($trackingEvent['UpdateDateTime'] ?? null);
                    $comments = $trackingEvent['Comments'] ?? null;
                    if (! $updateCode) {
                        continue;
                    }
                    $orderHistoryAlreadyExist = OrderHistory::where(['shipment_status' => $updateCode, 'shipment_id' => $shipmentId, 'action_time' => $dateTime, 'description' => $description])->first();
                    if ($orderHistoryAlreadyExist) {
                        continue;
                    }
                    if ($description && $dateTime) {
                        try {
                            $appStatus = OrderStatusEnum::fromAramexStatus($updateCode)->value;
                        } catch (\Exception $e) {
                            Log::info($trackingEvent);
                            throw $e;
                        }
                        OrderHistory::create([
                            'order_id' => $order->id,
                            'event_type' => $appStatus,
                            'shipment_id' => $shipmentId,
                            'description' => $description,
                            'shipment_status' => $updateCode,
                            'action_time' => $dateTime,
                            'shipment_company' => CourierIdentifierEnum::ARAMEX->value,
                            'performed_by' => null,
                            'additional_info' => json_encode([
                                'location' => $location,
                                'comments' => $comments,
                            ]),
                        ]);

                        $this->info("Logged history for Order #{$order->id}, Event: {$updateCode} ({$description})");

                    }
                }
            }
            $merchant = Merchant::withoutGlobalScope(UserScope::class)->where('id', $order->merchant_id)->first();
            $warehouse = Warehouse::withoutGlobalScope(UserScope::class)->where('id', $order->warehouse_id)->first();

            $chargeableWeight = $response['TrackingResults'][0]['Value'][0]['ChargeableWeight'] ?? null;
            if ($chargeableWeight !== null && is_numeric($chargeableWeight)) {
                $chargeableWeightInGrams = (int) (floatval($chargeableWeight) * 1000);

                if (($order->shipment_total_weight !== $chargeableWeightInGrams)) {
                    $old_shipment_total_weight = $order->shipment_total_weight;
                    $order->shipment_total_weight = $chargeableWeightInGrams;
                    $this->info("Order #{$order->id} updated with :");
                    $order->save();
                    $this->info("-Total Weight form {$old_shipment_total_weight} to { $order->shipment_total_weight}");

                    if ($order->createdWithGlobalConfig()) {
                        $factory = app(ShippingServiceFactory::class);
                        $service = $factory->create(serviceName: CourierIdentifierEnum::ARAMEX->value, useGlobalConfig: $order->createdWithGlobalConfig(), order: $order);
                        $old_shipment_cost = $order->shipment_cost;
                        $old_shipment_cost_without_cod = $order->shipment_cost_without_cod;
                        $old_base_shipment_cost = $order->base_shipment_cost;
                        $order->shipment_cost = $service->getRates(ShippingRateDto::fromOrder(order: $order, isShipmentTotalWeightCorrected : true));
                        $order->shipment_cost_without_cod = $service->getRates(ShippingRateDto::fromOrder(order: $order, ignoreCod: true, isShipmentTotalWeightCorrected : true));
                        $order->base_shipment_cost = $service->getCosts(ShippingRateDto::fromOrder(order: $order, isShipmentTotalWeightCorrected : true));
                        $order->save();
                        $this->info("-Total Shipment cost form {$old_shipment_cost} to { $order->shipment_cost}");
                        $this->info("-Shipment Cost Without Cod form {$old_shipment_cost_without_cod} to { $order->shipment_cost_without_cod}");
                        $this->info("-Total Shipment cost form {$old_base_shipment_cost} to { $order->base_shipment_cost}");

                        $user = User::where('id', $warehouse?->user_id)
                            ->orwhere('id', $merchant?->user_id)
                            ->first();

                        $costDifference = $old_shipment_cost - $order->shipment_cost;
                        if ($user && $costDifference < 0) {

                            $user->walletTransactions()->create([
                                'type' => 'debit',
                                'amount' => abs($costDifference),
                                'description' => 'رسوم شحنة إضافية بعد التثبت من الوزن:'.trans('translation.'.CourierIdentifierEnum::ARAMEX->value),
                                'order_id' => $order->id,
                            ]);
                            $this->info("-wallet transaction amount : {$costDifference}");
                            $user->wallet_balance -= abs($costDifference);
                            $user->save();
                        }

                    }
                }
            }

            $status = $response['TrackingResults'][0]['Value'][0]['UpdateCode'] ?? null;

            $merchant = Merchant::withoutGlobalScope(UserScope::class)->where('id', $order->merchant_id)->first();
            if ($status === null) {
                Log::info("{$order->id} is missing a status from aramex?");

                continue;
            }
            $newStatus = OrderStatusEnum::fromAramexStatus($status)->value;

            if ($newStatus === OrderStatusEnum::NOT_CHANGED->value && isset($response['TrackingResults'][0]['Value'][1])) {
                $this->warn("Order #{$order->id} not updated: Last aramex status can't change order status in treek.");
                $status = $response['TrackingResults'][0]['Value'][1]['UpdateCode'] ?? null;
                $newStatus = OrderStatusEnum::fromAramexStatus($status)->value;
            }

            if ($newStatus === OrderStatusEnum::NOT_CHANGED->value && isset($response['TrackingResults'][0]['Value'][2])) {
                $this->warn("Order #{$order->id} not updated: Last aramex status can't change order status in treek.");
                $status = $response['TrackingResults'][0]['Value'][2]['UpdateCode'] ?? null;
                $newStatus = OrderStatusEnum::fromAramexStatus($status)->value;
            }

            if ($newStatus === $order->status) {
                $this->info("Order #{$order->id}: Status unchanged ({$newStatus})");

                continue;
            }
            $this->warn("Order #{$order->id} will be updated to {$newStatus} from {$order->status}");
            // Update the order status
            if ($order->status !== OrderStatusEnum::RETURNED->value) {
                $order->updateStatus($newStatus);
                $this->info("Order #{$order->id} updated with status: {$newStatus}");
            }

            // Update Shopify COD orders to paid if status is delivered
            if ($newStatus === OrderStatusEnum::DELIVERED->value && $order->isCod() && $order->source === 'shopify' && $order->external_id) {
                try {
                    $shopifyService = new \App\Services\ShopifyAuthService($merchant);
                    $shopifyService->updateOrderToPaid($order->external_id);
                    $this->info("Order #{$order->id}: Updated Shopify COD order to paid");
                    Log::info('Updated Shopify COD order to paid via Aramex shipment update', [
                        'order_id' => $order->id,
                        'order_number' => $order->order_number,
                        'external_id' => $order->external_id,
                        'merchant_id' => $order->merchant_id,
                        'shipment_company' => 'aramex',
                    ]);
                } catch (\Exception $e) {
                    $this->error("Order #{$order->id}: Failed to update Shopify COD order to paid: {$e->getMessage()}");
                    Log::error('Failed to update Shopify COD order to paid via Aramex shipment update', [
                        'order_id' => $order->id,
                        'order_number' => $order->order_number,
                        'external_id' => $order->external_id,
                        'error' => $e->getMessage(),
                    ]);
                }
            }

            if (! $all && ! $year && ! $month && ! $shipmentReference && $merchant) {
                $salesChannelService = new SalesChannelService($merchant);
                $salesChannelService->updateStatus($order);
            }

        }

        $this->info('Shipment statuses updated successfully.');
    }

    /**
     * Convert Aramex date format to standard DateTime.
     */
    private function convertAramexDate(?string $aramexDate): ?string
    {
        if (! $aramexDate) {
            return null;
        }

        // Extract timestamp from the Aramex date string
        if (preg_match('/\/Date\((\d+)\+/', $aramexDate, $matches)) {
            return date('Y-m-d H:i:s', (int) ($matches[1] / 1000));
        }

        return null;
    }
}
