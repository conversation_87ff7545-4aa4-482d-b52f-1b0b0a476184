<?php

namespace App\Console\Commands;

use App\Enums\CodWalletStatusEnum;
use App\Enums\OrderHistoryEnum;
use App\Enums\PaymentMethodEnum;
use App\Models\CodWallet;
use App\Models\Order;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Console\Command;

class GenerateCodWallet extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'codwallet:generate';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle(): void
    {
        $oneWeekAgo = Carbon::now()->subWeek()->startOfWeek();

        $users = User::whereHas('warehouses', function ($query) use ($oneWeekAgo) {
            // @phpstan-ignore-next-line
            $query->withoutGlobalScopes()
                ->whereHas('orders', function ($query) use ($oneWeekAgo) {
                    $query->withoutGlobalScopes()
                        ->where('payment_method', PaymentMethodEnum::COD)
                        ->whereHas('orderHistories', function ($query) use ($oneWeekAgo) {
                            $query
                                ->where('created_at', '>=', $oneWeekAgo)
                                ->where('event_type', OrderHistoryEnum::DELIVERED->value);
                        });
                });
        })
            ->orWhereHas('merchants', function ($query) use ($oneWeekAgo) {
                $query->withoutGlobalScopes()
                    ->whereHas('orders', function ($query) use ($oneWeekAgo) {
                        $query->withoutGlobalScopes()
                            ->where('payment_method', PaymentMethodEnum::COD)
                            ->whereHas('orderHistories', function ($query) use ($oneWeekAgo) {
                                $query
                                    ->where('created_at', '>=', $oneWeekAgo)
                                    ->where('event_type', OrderHistoryEnum::DELIVERED->value);
                            });
                    });
            })
            ->get();

        if ($users->isEmpty()) {
            $this->info('No users found with COD delivered orders.');

            return;
        }

        foreach ($users as $user) {
            $this->info("Processing CodWallet for User ID: {$user->id}");

            $totalAmount = Order::withoutGlobalScopes()
                ->where(function ($query) use ($user): void {
                    $query->whereHas('merchant', function ($q) use ($user) {
                        $q->withoutGlobalScopes()
                            ->where('user_id', $user->id);
                    })->orWhereHas('warehouse', function ($q) use ($user) {
                        $q->withoutGlobalScopes()
                            ->where('user_id', $user->id);
                    });
                })
                ->where('payment_method', PaymentMethodEnum::COD)
                ->where('cod_wallet_id', null)
                ->where('shipment_credentials_type', 'application')
                ->whereHas('orderHistories', function ($query) use ($oneWeekAgo) {
                    $query
                        ->where('created_at', '>=', $oneWeekAgo)
                        ->where('event_type', OrderHistoryEnum::DELIVERED->value);
                })
                ->sum('order_grand_total');

            $this->info("Total Amount for User ID {$user->id}: {$totalAmount}");

            if ($totalAmount > 0) {
                // Create the CodWallet
                $codWallet = CodWallet::create([
                    'user_id' => $user->id,
                    'amount' => $totalAmount,
                    'status' => CodWalletStatusEnum::ACCEPTED->value,
                    'type' => PaymentMethodEnum::COD->value,
                ]);

                $this->info("CodWallet created for User ID {$user->id} with amount {$totalAmount}");

                // Update orders with the newly created cod_wallet_id
                $updatedOrders = Order::withoutGlobalScopes()
                    ->where(function ($query) use ($user): void {
                        $query->whereHas('merchant', function ($q) use ($user) {
                            $q->withoutGlobalScopes()
                                ->where('user_id', $user->id);
                        })->orWhereHas('warehouse', function ($q) use ($user) {
                            $q->withoutGlobalScopes()
                                ->where('user_id', $user->id);
                        });
                    })
                    ->where('payment_method', PaymentMethodEnum::COD)
                    ->where('cod_wallet_id', null)
                    ->where('shipment_credentials_type', 'application')
                    ->whereHas('orderHistories', function ($query) use ($oneWeekAgo) {
                        $query
                            ->where('created_at', '>=', $oneWeekAgo)
                            ->where('event_type', OrderHistoryEnum::DELIVERED->value);
                    })
                    ->update(['cod_wallet_id' => $codWallet->id]);
                // Log updated orders
                if ($updatedOrders > 0) {
                    $this->info("CodWallet created for User ID {$user->id} with amount {$totalAmount}");
                    $this->info("Orders updated with CodWallet ID: {$codWallet->id}");

                    // Optionally, log the specific orders updated
                    $ordersWithCodWallet = Order::where('cod_wallet_id', $codWallet->id)->get(['id']);
                    foreach ($ordersWithCodWallet as $order) {
                        $this->info("Order ID {$order->id} updated with CodWallet ID {$codWallet->id}");
                    }
                } else {
                    $this->info("No orders were updated for User ID {$user->id}");
                }
            } else {
                $this->info("⚠️ Skipping User ID {$user->id} as total amount is zero.");
            }
        }

        $this->info('CodWallet generation process completed.');
    }
}
