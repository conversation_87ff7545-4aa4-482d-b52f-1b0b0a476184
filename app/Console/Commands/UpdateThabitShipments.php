<?php

namespace App\Console\Commands;

use App\Enums\CourierIdentifierEnum;
use App\Enums\OrderStatusEnum;
use App\Models\Merchant;
use App\Models\Order;
use App\Models\OrderHistory;
use App\Models\Scopes\MerchantScope;
use App\Models\Scopes\UserScope;
use App\Services\AlthabitService;
use App\Services\SalesChannelService;
use App\Services\ShippingServiceFactory;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class UpdateThabitShipments extends Command
{
    protected $signature = 'shipments:update-thabit {--all} {--year=} {--month=} {--shipment_reference=}';

    protected $description = 'Fetch shipments individually from Thabit and update order statuses';

    public function __construct()
    {
        parent::__construct();
    }

    public function handle(): void
    {
        $all = $this->option('all');
        $year = $this->option('year');
        $month = $this->option('month');
        $shipmentReference = $this->option('shipment_reference');

        $this->info('Fetching shipments individually from Thabit...');
        Log::info('Fetching shipments individually from Thabit...');
        // Fetch all orders with shipment IDs
        $query = Order::withoutGlobalScope(MerchantScope::class)
            ->where('shipment_company', CourierIdentifierEnum::THABIT->value)
            ->whereNot('status', OrderStatusEnum::RETURNED->value)
            ->whereNotNull('shipment_reference');

        // If specific shipment reference is provided, filter by it
        if ($shipmentReference) {
            $query->where('shipment_reference', $shipmentReference);
        } elseif (! $this->option('all')) {
            if ($month) {
                $year = $year ? (int) $year : now()->year;
                $month = (int) $month;
                $start = Carbon::createFromDate($year, $month, 1)->startOfMonth();
                $end = Carbon::createFromDate($year, $month, 1)->endOfMonth();
                $query->whereBetween('created_at', [$start, $end]);
            } else {
                $query->where('created_at', '>', now()->subDays(30));
            }
        }
        $orders = $query->get();

        $this->info((string) count($orders));
        if ($orders->isEmpty()) {
            $this->warn('No shipments found to update.');

            return;
        }
        // $i=0;
        try {
            /** @var Order[] $orders */
            foreach ($orders as $order) {
                $shipmentId = $order->shipment_reference;
                if (! $shipmentId) {
                    continue;
                }

                $this->info("Fetching shipment status for Order #{$order->id}, Shipment ID: {$shipmentId}...");

                $factory = app(ShippingServiceFactory::class);
                /** @var AlthabitService $service */
                $service = $factory->create(serviceName: CourierIdentifierEnum::THABIT->value, useGlobalConfig: $order->createdWithGlobalConfig(), order: $order);
                // Fetch shipment status for the current shipment ID
                $response = $service->trackShipment($shipmentId);
                $statusList = array_reverse($response['data']['list']);
                foreach ($statusList as $trackingResult) {
                    $reference = $trackingResult['id'];
                    $updateCode = $trackingResult['status'];
                    $description = $trackingResult['status_desc'];
                    $dateTime = $trackingResult['date'];
                    $appStatus = OrderStatusEnum::fromThabitStatus($updateCode)->value;
                    $orderHistoryAlreadyExist = OrderHistory::where(['shipment_status' => $updateCode, 'shipment_id' => $shipmentId, 'reference' => $reference])->first();
                    if ($orderHistoryAlreadyExist) {
                        echo "history exist \n";

                        continue;
                    }
                    OrderHistory::create([
                        'order_id' => $order->id,
                        'event_type' => $appStatus,
                        'shipment_id' => $shipmentId,
                        'description' => $description,
                        'shipment_status' => $updateCode,
                        'reference' => $reference,
                        'action_time' => $this->convertAramexDate($dateTime),
                        'shipment_company' => CourierIdentifierEnum::THABIT->value,
                        'performed_by' => null,
                    ]);

                    $this->info("Logged history for Order #{$order->id}, Event: {$updateCode} ({$description})");

                    $status = $trackingResult['status'] ?? null;

                    $merchant = Merchant::withoutGlobalScope(UserScope::class)
                        ->where('id', $order->merchant_id)
                        ->first();
                    if ($status === null) {
                        Log::info("{$order->id} is missing a status from Thabit?");

                        continue;
                    }

                    if ($appStatus === $order->status) {
                        $this->info("Order #{$order->id}: Status unchanged ({$appStatus})");

                        continue;
                    }

                    // Update the order status
                    $order->updateStatus($appStatus);
                    $this->info("Order #{$order->id} updated with status: {$appStatus}");

                    // Update Shopify COD orders to paid if status is delivered
                    if ($appStatus === OrderStatusEnum::DELIVERED->value && $order->isCod() && $order->source === 'shopify' && $order->external_id) {
                        try {
                            $shopifyService = new \App\Services\ShopifyAuthService($merchant);
                            $shopifyService->updateOrderToPaid($order->external_id);
                            $this->info("Order #{$order->id}: Updated Shopify COD order to paid");
                            Log::info('Updated Shopify COD order to paid via Thabit shipment update', [
                                'order_id' => $order->id,
                                'order_number' => $order->order_number,
                                'external_id' => $order->external_id,
                                'merchant_id' => $order->merchant_id,
                                'shipment_company' => 'thabit',
                            ]);
                        } catch (\Exception $e) {
                            $this->error("Order #{$order->id}: Failed to update Shopify COD order to paid: {$e->getMessage()}");
                            Log::error('Failed to update Shopify COD order to paid via Thabit shipment update', [
                                'order_id' => $order->id,
                                'order_number' => $order->order_number,
                                'external_id' => $order->external_id,
                                'error' => $e->getMessage(),
                            ]);
                        }
                    }

                    if (! $all && ! $year && ! $month && $merchant) {
                        $salesChannelService = new SalesChannelService($merchant);
                        $salesChannelService->updateStatus($order);
                    }

                }
            }

        } catch (\Exception $e) {
            $this->error('Error fetching shipments: '.$e->getMessage());
        }

        $this->info('Shipment statuses updated successfully.');
    }

    /**
     * Convert Aramex date format to standard DateTime.
     */
    private function convertAramexDate(?string $aramexDate): ?string
    {
        if (! $aramexDate) {
            return null;
        }

        // Extract timestamp from the Aramex date string
        if (preg_match('/\/Date\((\d+)\+/', $aramexDate, $matches)) {
            return date('Y-m-d H:i:s', (int) ($matches[1] / 1000));
        }

        return null;
    }
}
