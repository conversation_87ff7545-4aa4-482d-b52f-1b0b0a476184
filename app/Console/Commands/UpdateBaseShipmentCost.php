<?php

namespace App\Console\Commands;

use App\Dto\ShippingRateDto;
use App\Enums\CourierIdentifierEnum;
use App\Models\Order;
use App\Services\ShippingServiceFactory;
use Illuminate\Console\Command;

class UpdateBaseShipmentCost extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:update-base-shipment-cost';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle(): void
    {
        $orders = Order::withoutGlobalScopes()
            ->whereNotNull('shipment_company')
            ->where('shipment_company', '!=', '')
            ->where('shipment_credentials_type', 'application')
            ->get();

        foreach ($orders as $order) {
            $this->updateBaseShipmentCost($order);
        }
        $this->info("✅ Done. Total updated orders: {$this->modifiedCount}");
    }

    public int $modifiedCount = 0;

    public function updateBaseShipmentCost(Order $order): void
    {
        $this->info("Start order {$order->order_number}:");
        $courier = $order->shipment_company;
        $createdWithGlobalConfig = $order->createdWithGlobalConfig();
        $factory = app(ShippingServiceFactory::class);
        $isShipmentTotalWeightCorrected = false;
        if (($courier === CourierIdentifierEnum::ARAMEX->value) && ($order->shipment_total_weight > 10000)) {
            $isShipmentTotalWeightCorrected = true;
        }
        $service = $factory->create(serviceName: $courier, useGlobalConfig: $createdWithGlobalConfig, order: $order);
        $base_shipment_cost = $service->getCosts(ShippingRateDto::fromOrder(order: $order, isShipmentTotalWeightCorrected: $isShipmentTotalWeightCorrected));
        if ($order->base_shipment_cost === $base_shipment_cost) {
            return;
        }
        $this->modifiedCount++;
        $this->info("Order #{$order->order_number} | Company: {$courier} | Old: {$order->base_shipment_cost} | New: {$base_shipment_cost}");
        $order->timestamps = false;
        $order->update([
            'base_shipment_cost' => $base_shipment_cost,
        ]);

    }
}
