<?php

namespace App\Console\Commands;

use App\Models\Order;
use Illuminate\Console\Command;

class CorrectReceiverPhone extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:orders:update-receiver-receiver-phone';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Update receiver_phone field for all orders';

    /**
     * Execute the console command.
     */
    public int $modifiedCount = 0;

    public function handle(): void
    {
        $this->info('Starting to update orders reveivers phones ...');

        // Fetch all orders
        $query = Order::withoutGlobalScopes()
            ->whereNotNull('receiver_country_id');

        $orders = $query->get();

        $this->info("Found {$orders->count()} orders to update.");

        foreach ($orders as $order) {
            $this->info("start processing order #{$order->id}");
            if (! isPhoneInvalid($order->receiver_phone, $order->receiver_country_id)) {

                $this->info("skipping #{$order->id} : reveiver phone ( {$order->receiver_phone} ) correct");

                continue;
            }

            $updatedReceiverPhone = phoneCorrector($order->receiver_phone, $order->receiver_country_id);

            if (isPhoneInvalid($updatedReceiverPhone, $order->receiver_country_id)) {

                $this->warn("failed to update reveiver phone from  {$order->receiver_phone}  to {$updatedReceiverPhone}");

                continue;
            }

            $oldReceiverPhone = $order->receiver_phone;
            $order->receiver_phone = $updatedReceiverPhone;
            $order->save();
            $this->modifiedCount++;
            $this->info("reveiver phone updated from  {$oldReceiverPhone} to {$updatedReceiverPhone}");

        }

        $this->info("✅ Done. Total updated orders: {$this->modifiedCount}");
    }
}
