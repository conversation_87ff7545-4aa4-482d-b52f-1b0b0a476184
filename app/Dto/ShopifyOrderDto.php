<?php

declare(strict_types=1);

namespace App\Dto;

class ShopifyOrderDto
{
    /**
     * @param  ShopifyOrderItemDto[]  $items
     */
    public function __construct(
        public ?int $webhookId,
        public int $externalId,
        public int $orderNumber,
        public string $status,
        public string $date,
        public float $orderGrandTotal,
        public int $cashToBeCollected,
        public string $description,
        public string $paymentMethod,
        public string $receiverFirstName,
        public string $receiverLastName,
        public string $receiverPhone,
        public ?string $receiverEmail,
        public ?string $receiverCountry,
        public string $receiverAddressLine,
        public ?string $receiverStreetName,
        public ?string $receiverCity,
        public ?string $receiverCountryCode,
        public ?string $receiverPostalCode,
        public ?float $receiverLatitude,
        public ?float $receiverLongitude,
        public string $shipperName,
        public ?string $shipperEmail,
        public ?string $shipperPhone,
        public string $shipperAddressLine,
        public ?string $shipperLatitude,
        public ?string $shipperLongitude,
        public string $shipperCity,
        public float $shipmentTotalWeight,
        public int $merchantId,
        public int $tax = 0,
        public int $boxesCount = 0,
        public array $items = []
    ) {}
}
