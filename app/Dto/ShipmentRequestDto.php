<?php

namespace App\Dto;

readonly class ShipmentRequestDto
{
    /**
     * @param  array<string, mixed>  $payload
     * @param  array<string, string>  $headers
     */
    public function __construct(
        public string $url,
        public array $payload,
        public array $headers = [],
        public string $method = 'POST',
        public string $contentType = 'json',
    ) {}

    /**
     * @param  array<string, mixed>  $payload
     * @param  array<string, string>  $headers
     */
    public static function create(
        string $url,
        array $payload,
        array $headers = [],
        string $method = 'POST',
        string $contentType = 'json'
    ): self {
        return new self($url, $payload, $headers, $method, $contentType);
    }
}
