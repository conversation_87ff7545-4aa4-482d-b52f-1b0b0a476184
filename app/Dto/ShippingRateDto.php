<?php

declare(strict_types=1);

namespace App\Dto;

use App\Models\Order;
use App\Models\User;
use App\Services\SettingsService;

class ShippingRateDto
{
    /**
     * @param  array<int[]>|null  $boxes
     */
    public function __construct(
        public ?array $boxes,
        public ?int $weight,
        public ?string $fromLocation,
        public ?string $toLocation,
        public string $receiverCountryCode,
        public bool $isCod = false,
        public bool $ignoreCod = false,
        public bool $isInternational = false,
        public ?User $user = null
    ) {}

    /**
     * Create a DTO from an Order model.
     */
    public static function fromOrder(Order $order, bool $ignoreCod = false, bool $isReverseShipment = false, bool $isShipmentTotalWeightCorrected = false): self
    {

        return new self(
            boxes: $order->boxes->toArray(),
            weight: null, // get weight by kg
            fromLocation: $isReverseShipment ? $order->receiverCity->name_ar : $order->shipper_city,
            toLocation: $isReverseShipment ? $order->shipper_city : $order->receiverCity->name_ar,
            receiverCountryCode: $order->receiverCountry->code_country ?? $order->receiver_country_code,
            isCod: $order->isCod(),
            ignoreCod: $ignoreCod,
            isInternational: $order->isInternational(),
            user: $order->user
        );
    }

    /**
     * Calculate total weight from an array of boxes by kg.
     */
    public function calculateWeightFromBoxes(int $volumetricDivisor): int
    {
        if ($this->weight !== null) {
            return $this->weight;
        }

        return $this->getMaxWeight($volumetricDivisor);
    }

    public function getWeightVolume(?int $volumetricDivisor): int
    {
        $divisor = $volumetricDivisor ?? app(SettingsService::class)->getVolumetricDivisor();
        $totalWeightVolume = collect($this->boxes)->sum(function ($box) use ($divisor) {
            return ($box['length'] * $box['width'] * $box['height']) / $divisor;
        });

        return (int) ceil($totalWeightVolume);
    }

    public function getWeight(): int
    {

        $weight = collect($this->boxes)->sum(function ($box) {
            return $box['weight'];
        });

        return (int) $weight;
    }

    public function getMaxWeight(int $volumetricDivisor): int
    {
        if ($this->getWeightVolume($volumetricDivisor) > $this->getWeight()) {
            return $this->getWeightVolume($volumetricDivisor);
        }

        return $this->getWeight();
    }
}
