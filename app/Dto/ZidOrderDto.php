<?php

namespace App\Dto;

class ZidOrderDto
{
    public function __construct(
        public readonly ?int $webhookId,
        public readonly string $externalId,
        public readonly string $orderNumber,
        public readonly string $status,
        public readonly string $date,
        public readonly int $orderGrandTotal,
        public readonly int $cashToBeCollected,
        public readonly string $description,
        public readonly string $paymentMethod,

        public readonly string $receiverFirstName,
        public readonly ?string $receiverLastName,
        public readonly string $receiverPhone,
        public readonly ?string $receiverEmail,
        public readonly string $receiverCountry,
        // public readonly ?int $receiverCountryId,
        public readonly ?string $receiverAddressLine,
        public readonly ?string $receiverStreetName,
        // public readonly ?int $receiverCityId,
        public readonly string $receiverCity,
        public readonly ?string $receiverPostalCode,
        public readonly ?string $receiverLatitude,
        public readonly ?string $receiverLongitude,

        public readonly string $shipperName,
        public readonly ?string $shipperEmail,
        public readonly ?string $shipperPhone,
        public readonly string $shipperCountry,
        public readonly string $shipperCountryCode,
        public readonly string $shipperAddressLine,
        public readonly ?string $shipperLatitude,
        public readonly ?string $shipperLongitude,
        public readonly string $shipperCity,

        public readonly int $shipmentTotalWeight,
        public readonly int $merchantId,
        public readonly ?int $warehouseId,
        public readonly int $tax,
        public readonly int $boxesCount,

        /** @var ZidOrderItemDto[] */
        public readonly array $items,
    ) {}
}
