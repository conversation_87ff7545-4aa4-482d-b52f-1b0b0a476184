<?php

namespace App\Dto;

class SallaOrderDto
{
    /**
     * @param  SallaOrderItemDto[]  $items
     */
    public function __construct(
        public ?int $webhookId,
        public int $externalId,
        public int $orderNumber,
        public string $status,
        public string $date,
        public float $orderGrandTotal,
        public int $cashToBeCollected,
        public string $description,
        public string $paymentMethod,
        public string $receiverFirstName,
        public string $receiverLastName,
        public string $receiverPhone,
        public string $receiverEmail,
        public string $receiverCountry,
        public string $receiverCountryCode,
        public ?int $receiverCountryId,
        public string $receiverAddressLine,
        public string $receiverStreetName,
        public readonly ?int $receiverCityId,
        public string $receiverCity,
        public ?string $receiverPostalCode,
        public ?float $receiverLatitude,
        public ?float $receiverLongitude,
        public string $shipperName,
        public ?string $shipperEmail,
        public string $shipperPhone,
        public string $shipperCountry,
        public string $shipperCountryCode,
        public string $shipperAddressLine,
        public string $shipperLatitude,
        public string $shipperLongitude,
        public string $shipperCity,
        public float $shipmentTotalWeight,
        public int $merchantId,
        public ?int $warehouseId,
        public int $tax = 0,
        public int $boxesCount = 0,
        public array $items = []
    ) {}
}
