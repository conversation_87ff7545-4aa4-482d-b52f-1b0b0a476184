<?php

namespace App\Dto;

class PricingConfigDto
{
    public function __construct(
        public readonly int $basePrice,
        public readonly int $extraWeightFrom,
        public readonly int $additionalWeightCost,
        public readonly int $cashOnDeliveryCost,
        public readonly int $distanceCost,
        public readonly int $volumetricDivisor,
        public readonly ?int $fuel = null,
        public readonly ?string $codType = null,
    ) {}
}
