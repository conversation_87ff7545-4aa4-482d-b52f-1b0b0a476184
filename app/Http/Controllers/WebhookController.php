<?php

declare(strict_types=1);

namespace App\Http\Controllers;

use App\Dto\SallaOrderDto;
use App\Dto\SallaOrderItemDto;
use App\Dto\ShopifyOrderDto;
use App\Dto\ShopifyOrderItemDto;
use App\Dto\WoocommerceOrderDto;
use App\Dto\WoocommerceOrderItemDto;
use App\Dto\ZidOrderDto;
use App\Dto\ZidOrderItemDto;
use App\Enums\OrderStatusEnum;
use App\Enums\PaymentMethodEnum;
use App\Enums\SalesChannelEnum;
use App\Enums\WeightUnitsEnum;
use App\Models\City;
use App\Models\Country;
use App\Models\Merchant;
use App\Models\Order;
use App\Models\Scopes\UserScope;
use App\Models\Webhook;
use App\Services\OrderStatusMapper;
use App\Services\SallaAuthService;
use App\Services\ShopifyAuthService;
use App\Services\WoocommerceAuthService;
use App\Services\ZidAuthService;
use Auth;
use Carbon\Carbon;
use Exception;
use Illuminate\Foundation\Application;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\Routing\Redirector;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class WebhookController extends Controller
{
    public function index(string $platform): JsonResponse
    {
        Log::info($platform.' Webhook route: Webhook Received');
        switch ($platform) {
            case SalesChannelEnum::SHOPIFY->value:
                $this->handleShopifyWebhook();
                break;
            case SalesChannelEnum::SALLA->value:
                $this->handleSallaWebhook();
                break;
            case SalesChannelEnum::ZID->value:
                $this->handleZidWebhook();
                break;
            case SalesChannelEnum::WOOCOMMERCE->value:
                $this->handleWoocommerceWebhook();
                break;
            default:
                return response()->json(['error' => 'Invalid platform'], 400);
        }

        return response()->json(['status' => 'success']);
    }

    public function install(string $storeName): RedirectResponse|Redirector|Application|null
    {
        if ($storeName === SalesChannelEnum::ZID->value) {
            $queries = http_build_query([
                'client_id' => config('services.zid.client_id'),
                'redirect_uri' => config('app.url').'/store/'.$storeName.'/callback',
                'response_type' => 'code',
            ]);
            Log::info('https://oauth.zid.sa'.'/oauth/authorize?'.$queries);
            Log::info(config('app.url').'/store/'.$storeName.'/callback');

            return redirect('https://oauth.zid.sa'.'/oauth/authorize?'.$queries);
        }
        if ($storeName === SalesChannelEnum::SHOPIFY->value) {
            return $this->installShopify();
        }

        return null;
    }

    public function callback(string $storeName): RedirectResponse|Redirector|Application
    {
        if ($storeName === SalesChannelEnum::ZID->value) {
            return $this->zidCallback();
        }

        if ($storeName === SalesChannelEnum::SHOPIFY->value) {
            return $this->handleShopifyCallback();
        }

        throw new Exception('Unknown store name');
    }

    public function installShopify(): RedirectResponse|Redirector|Application|null
    {
        Log::info('SHOPIFY: Webhook Received', request()->all());
        $shopifyDomain = request('shop');
        $scopes = ['read_products', 'write_products']; // Add necessary scopes for your app

        $query = http_build_query([
            'client_id' => 'a3a41f6bae261d4eacf7d9cc9f6a91c5',
            'redirect_uri' => 'https://www.gotreek.com'.'/store/shopify/callback',
            'scope' => implode(',', $scopes),
            'state' => \Str::random(40),
        ]);

        // Construct the OAuth authorization URL
        $authorizationUrl = "https://{$shopifyDomain}/admin/oauth/authorize?{$query}";

        //        Log::info('authorizationUrl', "$authorizationUrl");
        return redirect($authorizationUrl);
    }

    public function handleShopifyCallback(): RedirectResponse|Redirector|Application
    {
        $code = request('code');
        // TODO: Check the state on callback
        //        $state = request('state');

        //        if ($state === null || !hash_equals(csrf_token(), $state)) {
        //            abort(403, 'Invalid state parameter.');
        //        }
        // Extract store domain from the callback URL
        $storeDomain = request('shop'); // 'shop' is the query parameter containing the store domain

        // Construct the OAuth endpoint URL for the specific store
        $oauthEndpointUrl = "https://{$storeDomain}/admin/oauth/access_token";

        $response = Http::post($oauthEndpointUrl, [
            'client_id' => 'a3a41f6bae261d4eacf7d9cc9f6a91c5',
            'client_secret' => '7b8e40b0fde10e2e0d58df4a52a7eb6f',
            'code' => $code,
        ]);
        //        $shopifyAuthService = new ShopifyAuthService();
        //        $shopifyAuthService->getStore();
        $responseBody = $response->json();
        if (! isset($responseBody['access_token'])) {
            throw new Exception('Access token not found in response.');
        }
        $merchant = Merchant::withoutGlobalScopes()
            ->where('type', SalesChannelEnum::SHOPIFY->value)
            ->where('domain', $storeDomain)
            ->where('user_id', auth()->id())
            ->first();
        if ($merchant && $responseBody['access_token'] !== $merchant->access_token) {
            $merchant->update([
                'access_token' => $responseBody['access_token'],
                'refresh_token' => $responseBody['access_token'],
            ]);
        }
        if (! $merchant) {
            $merchant = Merchant::create([
                'name' => $storeDomain,
                'domain' => $storeDomain,
                'merchant_id' => 645155,
                'user_id' => Auth::id() ?? 21,
                'access_token' => $responseBody['access_token'],
                'refresh_token' => $responseBody['access_token'],
                'type' => SalesChannelEnum::SHOPIFY->value,
            ]);
            $merchant = Merchant::withoutGlobalScopes()->where('id', $merchant->id)->first();
            $shopifyAuthService = new ShopifyAuthService($merchant, isNew: true);
            $shopInfo = $shopifyAuthService->getShop();
            $merchant->merchant_id = $shopInfo['shop']['id'];
            $merchant->name = $shopInfo['shop']['name'];
            $merchant->save();
            Log::info('create merchant with type'.SalesChannelEnum::SHOPIFY->value);
        }
        $merchantId = $merchant->id;

        return redirect("merchant/shopify-merchants/$merchantId/edit");
    }

    private function zidCallback(): RedirectResponse
    {
        if (! auth()->check()) {
            return redirect()->route('filament.merchant.auth.login');
        }
        Log::info('Webhook Received', request()->all());
        $response = Http::post('https://oauth.zid.sa'.'/oauth/token', [
            'grant_type' => 'authorization_code',
            'client_id' => config('services.zid.client_id'),
            'client_secret' => config('services.zid.client_secret'),
            'redirect_uri' => config('app.url').'/store/zid/callback',
            'code' => request()->code,
        ]);
        $responseContent = $response->json();
        $zidAuthService = new ZidAuthService;

        $storeInfo = $zidAuthService->getStoreWithAccessToken($responseContent['authorization'], $responseContent['access_token']);
        $existMerchant = Merchant::where('merchant_id', (int) $storeInfo['id'])->first();
        if ($existMerchant) {
            return redirect('merchant/exist-merchant-alert');
        }

        Log::info('Webhook Received', $responseContent);
        // TODO: FIX MERCHANT_ID
        $merchant = Merchant::create([
            'name' => 'ZID',
            'merchant_id' => '111',
            'user_id' => auth()->id(),
            'access_token' => $responseContent['access_token'],
            'authorization' => $responseContent['authorization'],
            'refresh_token' => $responseContent['refresh_token'],
            'expires_in' => $responseContent['expires_in'],
            'type' => SalesChannelEnum::ZID->value,
        ]);
        Log::info('Webhook Received', $response->json());
        $zidAuthService->createWebhook($merchant);
        $storeInfo = $zidAuthService->getStore($merchant);
        $merchant->merchant_id = (int) $storeInfo['id'];
        $merchant->name = $storeInfo['title'];
        $merchant->save();
        $merchantId = $merchant->id;

        return redirect("merchant/zid-merchants/$merchantId/edit");
    }

    private function handleShopifyWebhook(): void
    {
        $webhookId = null;
        try {
            $webhookId = $this->storeWebhook(SalesChannelEnum::SHOPIFY->value);
            $merchant = Merchant::withoutGlobalScope(UserScope::class)->where(['domain' => request()->header('x-shopify-shop-domain')])->firstOrFail();
            $merchantService = new ShopifyAuthService($merchant);
            $productData = request();
            $event = request()->header('x-shopify-topic');
            if (! $event) {
                throw new Exception('event not set');
            }
            Log::info('Shopify Webhook Received'.$event);
            $productData = $this->handleShopifyOrderEvent($productData->toArray(), $merchant, $webhookId);
            $merchantService->forUser($merchant);
            $merchantService->handleOrderCreated($productData);
            Webhook::where('id', $webhookId)->update(['status' => 'success']);
        } catch (\Exception $exception) {
            Log::error($exception->getMessage());
            Log::error(request());
            Webhook::where('id', $webhookId)->update([
                'status' => 'failed',
                'error_message' => '"'.$exception->getMessage().'"',
                'stack_trace' => $exception->getTrace(),
            ]);
            throw $exception;
        }

    }

    private function handleSallaWebhook(): void
    {
        $webhookId = null;
        try {
            $webhookId = $this->storeWebhook(SalesChannelEnum::SALLA->value);

            if (request('webhookUrl')) {
                $merchant = Merchant::withoutGlobalScope(UserScope::class)->whereLike('webhook_url', '%'.request('webhookUrl').'%')->firstOrFail();
            } else {
                $merchant = Merchant::withoutGlobalScope(UserScope::class)->where(['merchant_id' => request('merchant')])->firstOrFail();
            }
            $event = request('event');
            if (! $event) {
                throw new Exception('event not set');
            }
            if ($event !== 'order.created') {
                throw new Exception('event no supported');
            }
            $productData = request('data');
            $productData = $this->handleOrderEvent($productData, $merchant, $webhookId);
            $sallaAuthService = new SallaAuthService;
            $sallaAuthService->forUser($merchant);
            $sallaAuthService->handleOrderCreated($productData);
            Webhook::where('id', $webhookId)->update(['status' => 'success']);
        } catch (\Exception $exception) {
            Log::error($exception->getMessage());
            Log::error(request());
            Webhook::where('id', $webhookId)->update([
                'status' => 'failed',
                'error_message' => '"'.$exception->getMessage().'"',
                'stack_trace' => $exception->getTrace(),
            ]);
            throw $exception;
        }

    }

    private function handleZidWebhook(): void
    {
        $webhookId = null;
        $event = request()->header('webhook-event');
        if (! $event) {
            Log::info('event not set');

            return;
        }
        if ($event !== 'order.status.update') {
            Log::info('event no supported'.$event);

            return;
        }
        $orderRequest = request()->toArray();
        if (empty($orderRequest)) {
            Log::info('empty request  ');

            return;
        }
        if ($orderRequest['order_status']['code'] === 'cancelled') {
            $order = Order::withoutGlobalScopes()->where('external_id', $orderRequest['id'])->first();
            $order->cancelOrder();
            Log::info('order cancelled from zid to treek');

            return;
        }
        if ($orderRequest['order_status']['code'] !== 'ready') {
            Log::info($orderRequest['order_status']);

            return;
        }
        try {
            $webhookId = $this->storeWebhook(SalesChannelEnum::ZID->value);
            $merchant = Merchant::withoutGlobalScope(UserScope::class)->where(['merchant_id' => request('store_id'), 'type' => SalesChannelEnum::ZID->value])->firstOrFail();
            // if ($event === 'app.market.application.uninstall') {
            //     // $merchant->delete();
            //     Log::info($event);
            //     return;
            // }
            $orderRequestData = $this->handleZidOrderEvent($orderRequest, $merchant, $webhookId);
            $zidAuthService = new ZidAuthService;
            $zidAuthService->forUser($merchant);

            $zidAuthService->handleZidOrderCreated($orderRequestData);
            Webhook::where('id', $webhookId)->update(['status' => 'success']);
        } catch (\Exception $exception) {
            Log::error($exception->getMessage());
            Log::error(request());
            Webhook::where('id', $webhookId)->update([
                'status' => 'failed',
                'error_message' => '"'.$exception->getMessage().'"',
                'stack_trace' => $exception->getTrace(),
            ]);
            throw $exception;
        }

    }

    private function handleWoocommerceWebhook(): void
    {
        $webhookId = null;
        try {
            $webhookId = $this->storeWebhook(SalesChannelEnum::WOOCOMMERCE->value);
            $merchant = Merchant::withoutGlobalScope(UserScope::class)
                ->whereLike('webhook_url', '%'.request('webhookUrl').'%')
                ->where('type', SalesChannelEnum::WOOCOMMERCE->value)->first();
            if (! $merchant) {
                return;
            }
            $orderRequest = request()->toArray();

            if (! isset($orderRequest['id'])) {
                return;
            }
            $orderRequestData = $this->handleWoocommerceOrderEvent($orderRequest, $merchant, $webhookId);
            $woocommerceAuthService = new WoocommerceAuthService;
            $woocommerceAuthService->forUser($merchant);
            $woocommerceAuthService->handleOrderCreated($orderRequestData);
            Webhook::where('id', $webhookId)->update(['status' => 'success']);
        } catch (\Exception $exception) {
            Log::error($exception->getMessage());
            Log::error(request());
            Webhook::where('id', $webhookId)->update([
                'status' => 'failed',
                'error_message' => '"'.$exception->getMessage().'"',
                'stack_trace' => $exception->getTrace(),
            ]);
            throw $exception;
        }
    }

    /**
     * @param  array<mixed>  $payload
     */
    public function handleOrderEvent(array $payload, Merchant $merchant, ?int $webhookId): SallaOrderDto
    {
        try {
            $customer = $payload['customer'];
            $shipping = $payload['shipping'] ?? [];
            $shipper = $shipping['shipper'] ?? [];
            $pickupAddress = $shipping['pickup_address'] ?? [];
            $countryId = Country::findByString($shipping['address']['country'], $shipping['address']['country_code']);
            $cityId = City::findByString($shipping['address']['city']);
            $shipments = $payload['shipments'] ?? [];
            if (! isset($shipper['company_name'])) {
                throw new Exception('Order coming from Salla does not have shipment'.$payload['id'].' merchant: '.$merchant->id);
            }

            return new SallaOrderDto(
                webhookId: $webhookId,
                externalId: $payload['id'],
                orderNumber: $payload['reference_id'] ?? '',
                status: OrderStatusMapper::mapStatus(SalesChannelEnum::SALLA->value, $payload['status']['slug'] ?? 'unknown')->value,
                date: $payload['date']['date'] ?? now(),
                orderGrandTotal: $payload['amounts']['total']['amount'] ?? 0,
                cashToBeCollected: $payload['amounts']['cash_on_delivery']['amount'] ? (int) $payload['amounts']['cash_on_delivery']['amount'] * 100 : 0,
                description: $payload['source_details']['type'] ?? 'Order imported from Salla',
                paymentMethod: $payload['payment_method'] ?? '',
                receiverFirstName: $customer['first_name'] ?? '',
                receiverLastName: $customer['last_name'] ?? '',
                receiverPhone: $customer['mobile_code'].$customer['mobile'],
                receiverEmail: $customer['email'] ?? '',
                receiverCountry: $shipping['address']['country'] ?? '',
                receiverCountryCode: $shipping['address']['country_code'] ?? '',
                receiverCountryId: $countryId,
                receiverAddressLine: $shipping['address']['shipping_address'] ?? '',
                receiverStreetName: $shipping['address']['street_number'] ?? '',
                receiverCityId: $cityId,
                receiverCity: $shipping['address']['city'] ?? '',
                receiverPostalCode: $shipping['address']['postal_code'] ?? null,
                receiverLatitude: $shipping['address']['geo_coordinates']['lat'] ?? null,
                receiverLongitude: $shipping['address']['geo_coordinates']['lng'] ?? null,
                shipperName: $shipper['company_name'],
                shipperEmail: $shipper['email'],
                shipperPhone: $shipper['phone'],
                shipperCountry: $pickupAddress['country'],
                shipperCountryCode: $pickupAddress['country_code'],
                shipperAddressLine: $pickupAddress['shipping_address'],
                shipperLatitude: (string) $pickupAddress['geo_coordinates']['lat'],
                shipperLongitude: (string) $pickupAddress['geo_coordinates']['lng'],
                shipperCity: $pickupAddress['city'],
                shipmentTotalWeight: $shipments[0]['total_weight']['value'],
                merchantId: $merchant->id,
                warehouseId: $merchant->warehouse_id ?? null,
                tax: $payload['amounts']['tax']['amount']['amount'] ? (int) ($payload['amounts']['tax']['amount']['amount'] * 100) : 0,
                boxesCount: count($payload['items'] ?? []),
                items: array_map(
                    fn ($item) => new SallaOrderItemDto(
                        name: $item['name'] ?? '',
                        sku: $item['sku'] ?? '',
                        quantity: $item['quantity'] ?? 1,
                        weight: (int) ($item['weight'] * 100),
                        price: (int) $item['amounts']['price_without_tax']['amount'],
                        totalPrice: (int) ($item['amounts']['total']['amount'] * 100),
                        tax: (int) ($item['amounts']['tax']['amount']['amount'] * 100)
                    ),
                    $payload['items'] ?? [],
                )
            );
        } catch (\Exception $e) {
            Log::error('Error processing order event', [
                'error' => $e->getMessage(),
                'payload' => $payload,
            ]);
            throw $e;
        }
    }

    /**
     * @param  array<mixed>  $payload
     */
    public function handleShopifyOrderEvent(array $payload, Merchant $merchant, ?int $webhookId): ShopifyOrderDto
    {
        try {
            $customer = $payload['customer'] ?? null;
            $shippingAddress = $payload['shipping_address'] ?? $payload['customer']['default_address'] ?? null;

            // Detect COD orders by checking various indicators
            $isCodOrder = $this->isShopifyCodOrder($payload);
            $orderTotal = (float) ($payload['current_total_price'] ?? 0);

            // Map payment method to proper enum values
            $paymentMethod = $isCodOrder ? PaymentMethodEnum::COD->value : PaymentMethodEnum::PAID->value;

            // Set cash to be collected for COD orders
            $cashToBeCollected = $isCodOrder ? (int) ($orderTotal * 100) : 0;

            return new ShopifyOrderDto(
                webhookId: $webhookId,
                externalId: $payload['id'],
                orderNumber: $payload['order_number'],
                status: OrderStatusEnum::PENDING->value,
                date: $payload['created_at'] ? Carbon::parse($payload['created_at'])->toDateTimeString() : now()->toDateTimeString(),
                orderGrandTotal: $orderTotal,
                cashToBeCollected: $cashToBeCollected,
                description: $payload['note'] ?? 'Order imported from Shopify',
                paymentMethod: $paymentMethod,

                receiverFirstName: $shippingAddress['first_name'] ?? ($customer['first_name'] ?? ''),
                receiverLastName: $shippingAddress['last_name'] ?? ($customer['last_name'] ?? ''),
                receiverPhone: $shippingAddress['phone'] ?? '',
                receiverEmail: $shippingAddress['email'] ?? ($payload['contact_email'] ?? null),
                receiverCountry: $shippingAddress['country'] ?? null,
                receiverAddressLine: $shippingAddress['address1'] ?? ($shippingAddress['address2'] ?? ''),
                receiverStreetName: $shippingAddress['address2'] ?? null,
                receiverCity: $shippingAddress['city'] ?? null,
                receiverCountryCode: $shippingAddress['country_code'] ?? null,
                receiverPostalCode: $shippingAddress['zip'] ?? null,
                receiverLatitude: $shippingAddress['latitude'] ?? null,
                receiverLongitude: $shippingAddress['longitude'] ?? null,

                shipperName: $merchant->name,
                shipperEmail: null,
                shipperPhone: null,
                shipperAddressLine: '',
                shipperLatitude: null,
                shipperLongitude: null,
                shipperCity: '',

                shipmentTotalWeight: $payload['total_weight'],
                merchantId: $merchant->id,
                tax: isset($payload['total_tax']) ? (int) ((float) $payload['total_tax'] * 100) : 0,
                boxesCount: count($payload['line_items'] ?? []),
                items: array_map(
                    fn ($item) => new ShopifyOrderItemDto(
                        name: $item['name'] ?? '',
                        sku: $item['sku'] ?? '',
                        quantity: $item['quantity'] ?? 1,
                        weight: $item['grams'] ?? null,
                        price: isset($item['price']) ? (int) ((float) $item['price'] * 100) : null,
                        totalPrice: isset($item['price'])
                            ? (int) (
                                ((float) $item['price'] * 100) * ($item['quantity'] ?? 1) +
                                ((float) ($item['tax_lines'][0]['price'] ?? 0) * 100)
                            )
                            : null,
                        tax: isset($item['tax_lines'][0]['price']) ? (int) ((float) $item['tax_lines'][0]['price'] * 100) : 0
                    ),
                    $payload['line_items'] ?? [],
                )
            );
        } catch (\Exception $e) {
            Log::error('Error processing order event', [
                'error' => $e->getMessage(),
                'payload' => $payload,
            ]);
            throw $e;
        }
    }

    private function storeWebhook(string $platform): int
    {
        try {
            $webhook = Webhook::create([
                'payload' => request()->all(),
                'url' => request()->url(),
                'status' => 'pending',
            ]);

            Log::info("Webhook stored for platform: $platform");
        } catch (\Exception $e) {
            Log::error('Error storing webhook: '.$e->getMessage());
            throw $e;
        }

        return $webhook->id;
    }

    /**
     * @param  array<mixed>  $payload
     *
     * @throws Exception
     */
    public function handleZidOrderEvent(array $payload, Merchant $merchant, ?int $webhookId): ZidOrderDto
    {
        try {
            $shippingAddress = $payload['shipping']['address'];
            $meta = $shippingAddress['meta'] ?? null;
            $customer = $payload['customer'];
            $consignee = $payload['consignee'] ?? null;

            $products = $payload['products'] ?? [];

            $paymentMethod = $payload['payment']['method']['code'];
            Log::info(print_r($payload, true));

            return new ZidOrderDto(
                webhookId: $webhookId,
                externalId: (string) $payload['id'],
                orderNumber: (string) $payload['id'],
                status: OrderStatusEnum::PENDING->value,
                date: $payload['created_at'] ?? now(),
                orderGrandTotal: (int) $payload['order_total'],
                cashToBeCollected: (int) ((float) ($payload['invoice']['zid_cod'] ?? 0) * 100),
                description: $payload['customer_note'] ?? '',
                paymentMethod: $paymentMethod === 'zid_cod' ? PaymentMethodEnum::COD->value : PaymentMethodEnum::PAID->value,
                receiverFirstName: $consignee['name'] ?? ($customer['name'] ?? ''),
                receiverLastName: '.',
                receiverPhone: $consignee['mobile'] ?? ($customer['mobile'] ?? ''),
                receiverEmail: $consignee['email'] ?? ($customer['email'] ?? ''),
                receiverCountry: $shippingAddress['country']['name'],
                receiverAddressLine: ($shippingAddress['street'] ?? '').' '.($shippingAddress['district'] ?? ''),
                receiverStreetName: $shippingAddress['street'] ?? '',
                receiverCity: $shippingAddress['city']['name'],
                receiverPostalCode: $meta['postcode'] ?? null,
                receiverLatitude: $shippingAddress['lat'] ? (string) $shippingAddress['lat'] : null,
                receiverLongitude: $shippingAddress['lng'] ? (string) $shippingAddress['lng'] : null,
                shipperName: $merchant->name,
                shipperEmail: null,
                shipperPhone: null,
                shipperCountry: '',
                shipperCountryCode: '',
                shipperAddressLine: '',
                shipperLatitude: null,
                shipperLongitude: null,
                shipperCity: '',
                shipmentTotalWeight: $payload['weight'] ?? 1,
                merchantId: $merchant->id,
                warehouseId: $merchant->warehouse_id ?? null,
                tax: isset($products[0]['tax_amount']) ? (int) ((float) $products[0]['tax_amount'] * 100) : 0,
                boxesCount: $payload['packages_count'] ? (int) $payload['packages_count'] : 1,
                items: array_map(
                    fn ($item) => new ZidOrderItemDto(
                        name: $item['name'] ?? '',
                        sku: $item['sku'] ?? '',
                        quantity: (int) ($item['quantity'] ?? 1),
                        weight_unit: WeightUnitsEnum::KG->value,
                        weight: isset($item['weight']) ? (int) ((float) $item['weight']['value']) : 0,
                        price: isset($item['price']) && is_numeric($item['price']) ? (int) ((float) $item['price'] * 100) : 0,
                        totalPrice: isset($item['total']) && is_numeric($item['total']) ? (int) ((float) $item['total'] * 100) : 0,
                        tax: isset($item['tax_amount']) && is_numeric($item['tax_amount']) ? (int) ((float) $item['tax_amount'] * 100) : 0
                    ),
                    $products
                )
            );
        } catch (\Exception $e) {
            Log::error('Error processing Zid order event', [
                'error' => $e->getMessage(),
                'payload' => $payload,
            ]);
            throw $e;
        }
    }

    /**
     * @param  array<mixed>  $payload
     *
     * @throws Exception
     */
    public function handleWoocommerceOrderEvent(array $payload, Merchant $merchant, ?int $webhookId): WoocommerceOrderDto
    {
        try {
            $billingAdress = $payload['billing'];
            $shippingAddress = $payload['shipping'];

            return new WoocommerceOrderDto(
                webhookId: $webhookId,
                externalId: $payload['id'] ?? null,
                orderNumber: $payload['id'] ? request('webhookUrl').''.$payload['id'] : '',
                status: $payload['status'] ?? OrderStatusEnum::PENDING->value,
                date: isset($payload['date_created']) ? Carbon::parse($payload['date_created'])->toDateTimeString() : now()->toDateTimeString(),
                orderGrandTotal: (float) ($payload['total'] ?? 0),
                description: $payload['customer_note'] ?? 'Order imported from WooCommerce',
                paymentMethod: $payload['payment_method_title'] ?? '',

                receiverFirstName: $shippingAddress['first_name'] ?? ($billingAdress['first_name'] ?? ''),
                receiverLastName: $shippingAddress['last_name'] ?? ($billingAdress['last_name'] ?? ''),
                receiverPhone: $shippingAddress['phone'] ?? ($billingAdress['phone'] ?? ''),
                receiverEmail: $shippingAddress['email'] ?? ($billingAdress['email'] ?? null),
                receiverCountry: $shippingAddress['country'] ?? ($billingAdress['country'] ?? null),
                receiverAddressLine: $shippingAddress['address_1'] ?? ($shippingAddress['address_2'] ?? ($billingAdress['address_1'] ?? ($billingAdress['address_2'] ?? ''))),
                receiverStreetName: $shippingAddress['address_2'] ?? ($billingAdress['address_2'] ?? ''),
                receiverCity: $shippingAddress['city'] ?? ($billingAdress['state'] ?? null),
                receiverCountryCode: $shippingAddress['country'] ?? ($billingAdress['country'] ?? null),
                receiverPostalCode: $shippingAddress['postcode'] ?? null,
                receiverLatitude: null,
                receiverLongitude: null,

                shipperName: $merchant->name,
                shipperEmail: null,
                shipperPhone: null,
                shipperAddressLine: '',
                shipperLatitude: null,
                shipperLongitude: null,
                shipperCity: '',

                shipmentTotalWeight: 0,
                merchantId: $merchant->id,
                tax: isset($payload['total_tax']) ? (int) ((float) $payload['total_tax'] * 100) : 0,
                boxesCount: count($payload['line_items'] ?? []),
                items: array_map(
                    fn ($item) => new WoocommerceOrderItemDto(
                        name: $item['name'] ?? '',
                        sku: $item['sku'] ?? '',
                        quantity: $item['quantity'] ?? 1,
                        weight: 0,
                        price: isset($item['price']) ? (int) ((float) $item['price'] * 100) : null,
                        totalPrice: isset($item['total']) ? (int) (((float) $item['total'] * 100) +
                                (
                                    (isset($item['total_tax']) && (float) $item['total_tax'] > 0)
                                        ? ((float) $item['total_tax'] * 100)
                                        : ((float) $item['total'] * 100 * 0.15)
                                )) : null,
                        tax: isset($item['total_tax']) ? (int) ((float) $item['total_tax'] * 100) : 0
                    ),
                    $payload['line_items'] ?? [],
                )
            );
        } catch (\Exception $e) {
            Log::error('Error processing WooCommerce order event', [
                'error' => $e->getMessage(),
                'payload' => $payload,
            ]);
            throw $e;
        }
    }

    /**
     * Detect if a Shopify order is a COD (Cash on Delivery) order
     *
     * @param  array<mixed>  $payload
     */
    private function isShopifyCodOrder(array $payload): bool
    {
        // Check financial status - pending indicates COD (payment not collected)
        $financialStatus = strtolower($payload['financial_status'] ?? '');

        return $financialStatus === 'pending';
    }
}
