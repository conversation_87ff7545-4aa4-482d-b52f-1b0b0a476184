<?php

namespace App\Http\Controllers;

use App\Enums\CourierIdentifierEnum;
use App\Models\Order;
use App\Services\JtExpressService;
use App\Services\ShippingServiceFactory;
use Illuminate\Http\Client\ConnectionException;

class JtExpressController extends Controller
{
    public function __construct(protected JtExpressService $jtExpressService) {}

    /**
     * @return array<string, int|string>|string
     *
     * @throws ConnectionException
     */
    public function createShipment(): array|string
    {
        $order = Order::withoutGlobalScopes()->find(5922);
        $factory = app(ShippingServiceFactory::class);
        /** @var JtExpressService $service */
        $service = $factory->create(serviceName: CourierIdentifierEnum::JT->value, useGlobalConfig: true, order: Order::withoutGlobalScopes()->find(5922));

        return $service->returnParcelAfterPickedUp($order);
    }
}
