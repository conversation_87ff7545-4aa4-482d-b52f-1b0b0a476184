<?php

namespace App\Http\Controllers;

use App\Models\Order;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class OrderController extends Controller
{
    public function search(Request $request): JsonResponse
    {
        $request->validate([
            'tracking_number' => 'nullable|string',
            'order_number' => 'nullable|string',
        ]);

        $trackingNumber = $request->tracking_number;
        $orderNumber = $request->order_number;

        $orders = Order::withoutGlobalScopes()
            ->when($trackingNumber, fn ($query) => $query->where('shipment_reference', $trackingNumber))
            ->when($orderNumber, fn ($query) => $query->orWhere('order_number', $orderNumber))
            ->with(['histories' => function ($query) {
                $query->orderBy('action_time', 'desc');
            }])
            ->get();

        return response()->json($orders);
    }
}
