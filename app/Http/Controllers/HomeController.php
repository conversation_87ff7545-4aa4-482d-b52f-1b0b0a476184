<?php

namespace App\Http\Controllers;

use App\Models\Faq;
use Illuminate\Http\Request;
use Illuminate\View\View;

class HomeController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware(['auth', 'verified']);
    }

    public function home(): View
    {
        return view('home');
    }

    public function calculator(): View
    {
        return view('calculator');
    }

    public function shipment(Request $request): View
    {
        return view('shipment', [
            'orderNumber' => $request->query('orderNumber'),
            'trackingNumber' => $request->query('trackingNumber'),
        ]);
    }

    public function solutions(): View
    {
        return view('solutions');
    }

    public function about(): View
    {
        return view('about');
    }

    public function privacy(): View
    {
        return view('privacy');
    }

    public function questions(): View
    {
        $servicesFaq = Faq::where('section', 'services')->get();
        $shipmentFaq = Faq::where('section', 'shipment')->get();
        $linkFaq = Faq::where('section', 'link')->get();

        return view('questions', compact('servicesFaq', 'shipmentFaq', 'linkFaq'));
    }
}
