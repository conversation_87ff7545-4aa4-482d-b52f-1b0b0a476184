<?php

namespace App\Http\Controllers;

use App\Models\Merchant;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class SallaTokenController extends Controller
{
    public function update(Request $request): JsonResponse
    {
        $request->validate([
            'merchant_id' => 'required|exists:merchants,id',
            'access_token' => 'required|string',
            'refresh_token' => 'required|string',
        ]);

        $merchant = Merchant::firstOrFail($request->merchant_id);
        $merchant->access_token = $request->access_token;
        $merchant->refresh_token = $request->refresh_token;
        $merchant->expires_in = $request->expires_in;
        $merchant->save();

        return response()->json(['success' => true, 'message' => 'Tokens updated.']);
    }
}
