<?php

namespace App\Http\Controllers;

use App\Console\Commands\UpdateSPLShipments;
use Illuminate\Http\JsonResponse;

class SPLWebhookController extends Controller
{
    protected UpdateSPLShipments $updateSPLShipments;

    /**
     * Inject the UpdateSPLShipments Command
     */
    public function __construct(UpdateSPLShipments $updateSPLShipments)
    {
        $this->updateSPLShipments = $updateSPLShipments;
    }

    /**
     * Handle SPL webhook to update shipments.
     */
    public function handleWebhook(): JsonResponse
    {
        try {
            // Call the command's handle() method directly
            $this->updateSPLShipments->handle();

            return response()->json([
                'success' => true,
                'message' => 'The shipment update command has been executed successfully.',
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'An error occurred while executing the shipment update command.',
                'error' => $e->getMessage(),
            ], 500);
        }
    }
}
