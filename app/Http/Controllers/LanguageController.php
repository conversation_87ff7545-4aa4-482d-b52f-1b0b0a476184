<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Session;

class LanguageController extends Controller
{
    public function changeLanguage(Request $request): \Illuminate\Http\RedirectResponse
    {
        $language = $request->input('language');

        if (in_array($language, ['ar', 'en'])) {
            Session::put('locale', $language);
            App::setLocale($language);
        }

        return redirect()->back(); // Redirect back to the previous page
    }
}
