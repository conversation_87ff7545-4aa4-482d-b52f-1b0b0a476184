<?php

namespace App\Interfaces;

use App\Dto\ShipmentRequestDto;
use App\Dto\ShippingRateDto;
use App\Models\Order;

interface ShipmentCourierInterface
{
    const string SHIPMENT_TRACKING_URL = '';
    //    /**
    //     * Authenticate with the courier service.
    //     *
    //     * @return string Authentication token
    //     */
    //    public function login(string $username, string $password);
    //
    //    /**
    //     * Refresh the authentication token.
    //     *
    //     * @return string New authentication token
    //     */
    //    public function refreshToken(): string;

    /**
     * Get the available shipping rates for a shipment.
     */
    public function getRates(ShippingRateDto $shippingRateDto): int;

    /**
     * Get the cost shipping rates for a shipment.
     */
    public function getCosts(ShippingRateDto $shippingRateDto): int;

    /**
     * Create a shipment with the courier.
     */
    public function createShipment(Order $order): ShipmentRequestDto;

    /**
     * Create a reverse shipment with the courier.
     *
     * @return string[]
     */
    public function createReverseShipment(Order $order): array;

    /**
     * Track a shipment with the courier.
     *
     * @param  array<string|int>  $trackingNumbers
     * @return array<mixed>
     */
    public function trackShipments(array $trackingNumbers): array;

    /**
     * Print a shipment label
     */
    public function printLabel(Order $order): ShipmentRequestDto;

    /**
     * Extract print label URL from API response
     *
     * @param  mixed  $response  - Can be array (JSON), Http response object, or Order for local generation
     * @param  Order|null  $order  - Order object for services that need it
     */
    public function extractPrintLabelUrl($response, ?Order $order = null): string;

    /**
     * Cancel a shipment with the courier.
     *
     * @return string[]
     */
    public function cancelShipment(string $orderNumber): array;
}
