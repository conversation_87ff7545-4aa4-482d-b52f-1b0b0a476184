# Treek

## Overview
Treek is a Laravel 12 application built with PHP 8.3, using Filament admin panel and Livewire for interactive UI components. The application provides a robust platform for managing shipments, orders, and merchant services.

[![Laravel Forge Site Deployment Status](https://img.shields.io/endpoint?url=https%3A%2F%2Fforge.laravel.com%2Fsite-badges%2F7481dedf-8381-4ef8-9062-6e4744836070%3Fdate%3D1%26label%3D1%26commit%3D1&style=plastic)](https://forge.laravel.com/servers/919269/sites/2785092)
## Features
- Admin panel with Filament for easy management
- Interactive UI components with Livewire
- Multi-user roles and permissions
- Shipment tracking and management
- Order processing system
- Merchant dashboard
- Wallet transactions
- Integration with various shipping couriers

## Tech Stack
- **Backend**: PHP 8.3, Laravel 11.9
- **Frontend**: TailwindCSS, Vite
- **Admin Panel**: Filament with numerous plugins
- **Database**: MySQL 8.0
- **Development Environment**: <PERSON><PERSON> Sail (Docker)
- **Testing**: Pest PHP

## Installation

### Prerequisites
- Docker and Docker Compose
- Composer
- Node.js and npm

### Getting Started
1. Clone the repository
   ```bash
   git clone https://github.com/your-organization/treek.git
   cd treek
   ```

2. Install dependencies
   ```bash
   composer install
   ```

3. Set up environment
   ```bash
   cp .env.example .env
   # Configure your .env file with appropriate settings
   ```

4. Start the development environment
   ```bash
   make up
   ```

5. Run migrations
   ```bash
   make migrate
   ```

6. Generate IDE helper files
   ```bash
   make helper
   ```

## Development

### Project Structure
The project follows standard Laravel structure with some key directories:
- `app/`: Application code
  - `Filament/`: Admin panel components
  - `Services/`: Business logic services
  - `Interfaces/`: Interfaces for dependency injection
  - `Enums/`: PHP enumerations
- `config/`: Configuration files
- `database/`: Migrations and seeders
- `resources/`: Frontend assets and views
- `routes/`: API and web routes
- `tests/`: Unit and Feature tests

### Architecture Diagram
Below is an architectural dependency map showing the relationships between the main components of the application:

```
                                +-------------------+
                                |                   |
                                |    HTTP Request   |
                                |                   |
                                +--------+----------+
                                         |
                                         v
+---------------+            +-----------+-----------+
|               |            |                       |
|   Middleware  +<-----------+        Routes         |
|               |            |                       |
+-------+-------+            +-----------+-----------+
        |                                |
        v                                v
+-------+-------+            +-----------+-----------+
|               |            |                       |
|  Controllers  +----------->+      Livewire         |
|               |            |                       |
+-------+-------+            +-----------+-----------+
        |                                |
        v                                v
+-------+-------+            +-----------+-----------+
|               |            |                       |
|   Services    +<-----------+   Filament Panels     |
|               |            |                       |
+-------+-------+            +-----------+-----------+
        |                      |                   |
        |                      |                   |
        v                      v                   v
+-------+-------+    +---------+---------+    +----+-------------+
|               |    |                   |    |                  |
|  Interfaces   |    |   Admin Panel     |    |  Merchant Panel  |
|               |    |                   |    |                  |
+-------+-------+    +-------------------+    +------------------+
        |
        |
        v
+-------+-------+
|               |
|    Models     |
|               |
+-------+-------+
        |
        |
        v
+-------+-------+
|               |
|   Database    |
|               |
+---------------+

External Integrations:
+-------------------+    +-------------------+    +-------------------+
|                   |    |                   |    |                   |
| Shipping Couriers |    | E-commerce APIs   |    | Payment Gateways  |
| (Aramex, Barq,    |    | (Salla, Shopify,  |    |                   |
|  JT Express, etc) |    |  Zid, etc)        |    |                   |
+-------------------+    +-------------------+    +-------------------+
```

The application follows a layered architecture with clear separation of concerns:

1. **User Interface Layer**:
   - Filament Admin Panel for administrators
   - Filament Merchant Panel for merchants
   - Livewire components for interactive UI elements

2. **Application Layer**:
   - Controllers handle HTTP requests
   - Services contain business logic
   - Interfaces define contracts for dependency injection

3. **Domain Layer**:
   - Models represent the core business entities
   - Enums provide type-safe constants

4. **Infrastructure Layer**:
   - External integrations with shipping couriers (Aramex, Barq, JT Express, etc.)
   - E-commerce platform integrations (Salla, Shopify, Zid, etc.)
   - Database access and persistence

### Common Commands
The project includes a Makefile with useful commands:

#### Docker/Sail
- `make up`: Start Docker containers
- `make down`: Stop Docker containers
- `make bbash`: Access bash shell in container

#### Code Quality
- `make pint`: Run Laravel Pint code formatter
- `make stan`: Run PHPStan static analysis
- `make ci`: Run both Pint and PHPStan

#### Database
- `make migrate`: Run migrations
- `make reset-db`: Reset database and run seeders
- `make import-db`: Import remote database dump

## Testing
Tests are written using Pest PHP:
- Run all tests:
  ```bash
  ./vendor/bin/sail artisan test
  ```
- Run specific test:
  ```bash
  ./vendor/bin/sail artisan test --filter=TestName
  ```

## Best Practices
1. Follow PSR-12 coding standards (enforced by Laravel Pint)
2. Write tests for new features
3. Use static analysis to catch potential issues
4. Use type hints and docblocks
5. Follow Laravel conventions for naming and structure
6. Use dependency injection and interfaces for testability
7. Keep controllers thin, move business logic to services

## CI/CD
The project uses GitHub Actions for continuous integration:
- PHPStan for static analysis
- Laravel Pint for code style checking

## Accessing Tools
- Application: http://localhost (or configured APP_PORT)
- PHPMyAdmin: http://localhost:8081

## Contributing
Please read our contribution guidelines before submitting pull requests.

## License
This project is licensed under the MIT License - see the LICENSE file for details.
