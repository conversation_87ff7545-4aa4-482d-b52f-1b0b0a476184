<?php

use App\Models\User;
use Illuminate\Support\Facades\Hash;
use Tests\TestCase;

uses(TestCase::class);

test('true is true', function () {
    expect(true)->toBeTrue();
})->group('example');

test('can create a user', function () {
    // Arrange
    $userData = [
        'first_name' => 'Test',
        'last_name' => 'User',
        'email' => '<EMAIL>',
        'password' => Hash::make('password'),
        'role' => 'merchant',
    ];

    // Act
    $user = User::create($userData);

    // Assert
    expect($user)->toBeInstanceOf(User::class)
        ->and($user->first_name)->toBe('Test')
        ->and($user->last_name)->toBe('User')
        ->and($user->email)->toBe('<EMAIL>')
        ->and($user->role)->toBe('merchant');
});
