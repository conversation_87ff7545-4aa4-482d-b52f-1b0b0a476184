#!/bin/bash
set -e

echo "Deployment started ..."
#cp .env.ci .env
npm ci
NODE_OPTIONS="--max_old_space_size=5120" npm run build
composer install --no-dev --prefer-dist --optimize-autoloader

# Set proper ownership before running artisan commands
chown -R apache:apache .

#php artisan migrate:fresh --seed
# Run artisan commands as apache user to prevent root-owned files
sudo -u apache php artisan migrate --force
sudo -u apache php artisan config:clear
sudo -u apache php artisan route:clear
sudo -u apache php artisan view:clear
sudo -u apache php artisan optimize:clear
sudo -u apache php artisan livewire:publish --assets

sudo -u apache php artisan filament:assets
sudo -u apache php artisan filament:optimize
sudo -u apache php artisan optimize

# Ensure proper ownership of the entire application
chown -R apache:apache .

# Set specific permissions for storage and bootstrap/cache directories
chown -R apache:apache storage bootstrap/cache
chmod -R 775 storage bootstrap/cache

# Set group sticky bit on directories so new files inherit group ownership
find storage -type d -exec chmod g+s {} \;
find bootstrap/cache -type d -exec chmod g+s {} \;

# Ensure logs directory has proper permissions
chmod 775 storage/logs
chmod g+s storage/logs

echo "Deployment finished!"
