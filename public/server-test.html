<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Server Performance Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .header { color: #2c3e50; border-bottom: 2px solid #3498db; padding-bottom: 10px; }
        .metric { background: #ecf0f1; padding: 10px; margin: 10px 0; border-radius: 5px; }
        .good { background: #d5f4e6; color: #27ae60; }
        .warning { background: #fef9e7; color: #f39c12; }
        .bad { background: #fadbd8; color: #e74c3c; }
    </style>
</head>
<body>
    <h1 class="header">🚀 Server Performance Test</h1>
    
    <div class="metric">
        <strong>Test File:</strong> server-test.html<br>
        <strong>Server:</strong> gotreek.com<br>
        <strong>Purpose:</strong> Static HTML response test
    </div>

    <h2>Performance Expectations:</h2>
    <div class="metric good">✅ TTFB: &lt; 200ms (Good)</div>
    <div class="metric warning">⚠️ TTFB: 200-500ms (Acceptable)</div>
    <div class="metric bad">❌ TTFB: &gt; 500ms (Poor)</div>

    <h2>Test Instructions:</h2>
    <ol>
        <li>Open browser developer tools (F12)</li>
        <li>Go to Network tab</li>
        <li>Refresh this page</li>
        <li>Check the timing for this HTML file</li>
    </ol>

    <h2>Command Line Test:</h2>
    <code>curl -w "TTFB: %{time_starttransfer}s, Total: %{time_total}s\n" https://www.gotreek.com/server-test.html</code>

    <script>
        // Record page load time
        window.addEventListener('load', function() {
            const loadTime = performance.timing.loadEventEnd - performance.timing.navigationStart;
            console.log('Page Load Time:', loadTime + 'ms');
            
            // Add load time to page
            const loadDiv = document.createElement('div');
            loadDiv.className = loadTime < 1000 ? 'metric good' : loadTime < 2000 ? 'metric warning' : 'metric bad';
            loadDiv.innerHTML = `<strong>Actual Load Time:</strong> ${loadTime}ms`;
            document.body.appendChild(loadDiv);
        });
    </script>
</body>
</html> 