<?php

use App\Http\Controllers\SallaTokenController;
use App\Http\Controllers\SPLWebhookController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

Route::get('/spl-webhook', [SPLWebhookController::class, 'handleWebhook']);
Route::any('webhook/{platform}/{webhookUrl}', [App\Http\Controllers\WebhookController::class, 'index'])->name('indexWebhook');
Route::any('webhook/{platform}/{webhookUrl?}', [App\Http\Controllers\WebhookController::class, 'index'])->name('indexWebhook2');
Route::post('webhookUpdates', [App\Http\Controllers\AramexWebhookController::class, 'webhookUpdates'])->name('webhookUpdates');
Route::middleware('auth:sanctum')->get('/user', function (Request $request) {
    return $request->user();
});
Route::any('webhooks', function () {
    return new \Illuminate\Http\JsonResponse('NOT OK', 401);
});
Route::post('/salla/update-tokens', [SallaTokenController::class, 'update'])->name('salla.update-tokens');

Route::middleware('auth:sanctum')->group(function () {
    // ... existing routes ...
});
