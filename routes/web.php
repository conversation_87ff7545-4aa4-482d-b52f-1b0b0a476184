<?php

use App\Dto\ShippingRateDto;
use App\Enums\CourierIdentifierEnum;
use App\Http\Controllers\CityController;
use App\Http\Controllers\HomeController;
use App\Http\Controllers\LanguageController;
use App\Http\Controllers\OrderController;
use App\Models\Order;
use App\Services\ShippingServiceFactory;
use App\Services\SPLApiService;
use Illuminate\Foundation\Auth\EmailVerificationRequest;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/
Route::get('/search-order', [OrderController::class, 'search'])->name('order.search');
Route::get('spl/token', function () {
    $factory = app(ShippingServiceFactory::class);
    $order = \App\Models\Order::withoutGlobalScopes()->find(8975);
    /** @var SPLApiService $service */
    $service = $factory->create(serviceName: 'spl', useGlobalConfig: true, order: $order);

    return response()->json($service->getAccessToken());
});
Route::get('/ping', fn () => 'pong');

Route::get('/cost/{orderNumber}', function ($orderNumber) {
    $factory = app(ShippingServiceFactory::class);
    $order = Order::withoutGlobalScopes()->where('order_number', $orderNumber)->first();
    $service = $factory->create(serviceName: \App\Enums\CourierIdentifierEnum::ARAMEX->value, useGlobalConfig: true, order: $order);
    echo 'Aramex:'.$service->getCosts(ShippingRateDto::fromOrder(order: $order)).'<br>';
    $service = $factory->create(serviceName: \App\Enums\CourierIdentifierEnum::TRANSCORP->value, useGlobalConfig: true, order: $order);
    echo 'TRANSCORP:'.$service->getCosts(ShippingRateDto::fromOrder(order: $order)).'<br>';
    $service = $factory->create(serviceName: \App\Enums\CourierIdentifierEnum::THABIT->value, useGlobalConfig: true, order: $order);
    echo 'Thabit:'.$service->getCosts(ShippingRateDto::fromOrder(order: $order)).'<br>';
    $service = $factory->create(serviceName: \App\Enums\CourierIdentifierEnum::JT->value, useGlobalConfig: true, order: $order);
    echo 'JT:'.$service->getCosts(ShippingRateDto::fromOrder(order: $order)).'<br>';
    $service = $factory->create(serviceName: \App\Enums\CourierIdentifierEnum::BARQ->value, useGlobalConfig: true, order: $order);
    echo 'Barq:'.$service->getCosts(ShippingRateDto::fromOrder(order: $order)).'<br>';

});
// Route::get('spl/cities', function () {
//    $service = new SPLApiService;
//
//    return response()->json($service->getCitiesByRegion());
// });
//
// Route::get('spl/regions', function () {
//    $service = new SPLApiService;
//
//    return response()->json($service->getAllRegions());
// });
//
// Route::get('spl/countries', function () {
//    $service = new SPLApiService;
//
//    return response()->json($service->getCountries());
// });
//
// Route::get('spl/districts', function () {
//    $service = new SPLApiService;
//
//    return response()->json($service->getDistricts());
// });
//
// Route::get('spl/postal-office', function () {
//    $service = new SPLApiService;
//
//    return response()->json($service->getPostalOfficeByCity());
// });

Route::get('spl/pickup-delivery/', function () {
    $factory = app(ShippingServiceFactory::class);
    $order = \App\Models\Order::withoutGlobalScopes()->find(15818);
    /** @var SPLApiService $service */
    $service = $factory->create(serviceName: 'spl', useGlobalConfig: true, order: $order);
    $response = $service->createShipment($order);
});

Route::get('spl/item-events', function () {
    $service = new SPLApiService;

    return response()->json($service->getItemEvents());
});

Route::get('spl/getItemEvents/{id}/{CRMAccountId}', function ($id) {
    $order = \App\Models\Order::withoutGlobalScopes()->find($id);

    $shipmentService = new \App\Services\ShipmentService;
    $shipmentService->createShipment(
        order: $order,
        createdWithGlobalConfig: true,
        courier: CourierIdentifierEnum::SPL->value
    );

    return response()->json('test');
});

Route::any('store/{storeName}/callback', [App\Http\Controllers\WebhookController::class, 'callback'])->name('callback');
Route::get('store/{storeName}/install', [App\Http\Controllers\WebhookController::class, 'install'])->name('install');

Route::get('/orders/download-label/{fileName}', function ($fileName) {
    $filePath = storage_path('app/temp/'.$fileName);
    if (! file_exists($filePath)) {
        abort(404, 'Label file not found.');
    }

    return response()->download($filePath)->deleteFileAfterSend(true);
})->name('orders.download-label');

Route::get('/', [HomeController::class, 'home'])->name('homepage')->withoutMiddleware(['auth', 'verified']);
Route::get('solutions', [HomeController::class, 'solutions'])->name('solutions')->withoutMiddleware(['auth', 'verified']);
Route::get('about', [HomeController::class, 'about'])->name('about')->withoutMiddleware(['auth', 'verified']);
Route::get('questions', [HomeController::class, 'questions'])->name('questions')->withoutMiddleware(['auth', 'verified']);
Route::get('calculator', [HomeController::class, 'calculator'])->name('calculator')->withoutMiddleware(['auth', 'verified']);
Route::get('shipment_tracking', [HomeController::class, 'shipment'])->name('shipment')->withoutMiddleware(['auth', 'verified']);
Route::get('/privacy', [HomeController::class, 'privacy'])->withoutMiddleware(['auth', 'verified']);

Route::group(['middleware' => 'auth'], function () {
    Route::get('/filament', function () {
        return redirect()->route('filament.merchant.pages.dashboard');
    })->name('root');

});

Route::post('/change-language', [LanguageController::class, 'changeLanguage'])->name('change.language');

// Email Verification Routes
Route::get('/email/verify/{id}/{hash}', function (EmailVerificationRequest $request) {
    $request->fulfill();

    return redirect('/merchant');
})->middleware(['auth', 'signed'])->name('verification.verify');
// Email Verification Routes
Route::get('/email/verify/{id}/{hash}', function (EmailVerificationRequest $request) {
    $request->fulfill();

    return redirect('/merchant');
})->middleware(['auth', 'signed'])->name('verification.verify');

Route::get('/merchant/email-verification/verify/{id}/{hash}', function (EmailVerificationRequest $request) {
    $request->fulfill();

    return redirect('/merchant');
})->middleware(['auth', 'signed'])->name('filament.merchant.auth.email-verification.verify');
Route::post('/email/verification-notification', function (\Illuminate\Http\Request $request) {
    $request->user()->sendEmailVerificationNotification();

    return back()->with('message', 'Verification link sent!');
})->middleware(['auth', 'throttle:6,1'])->name('verification.send');

Route::get('/search-cities', [CityController::class, 'search']);

Route::get('/view-label', function () {
    $labelUrl = request()->query('url');
    $proformaUrl = session('proforma_url');

    return view('pdfs.label-viewer', [
        'labelUrl' => $labelUrl,
        'proformaUrl' => $proformaUrl,
    ]);
})->name('view-label');
Route::get('/email/verify', function () {
    return view('auth.verify-email');
})->middleware('auth')->name('verification.notice');

Route::get('merchant/exist-merchant-alert', function () {
    return view('filament.merchant.pages.exist-merchant-alert');
})->middleware('auth');
