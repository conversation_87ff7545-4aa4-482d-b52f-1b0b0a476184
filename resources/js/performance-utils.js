/**
 * Lightweight Performance Utilities for Treek Application
 */

// Optimized debounce function
window.debounce = function(func, wait, immediate) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            timeout = null;
            if (!immediate) func(...args);
        };
        const callNow = immediate && !timeout;
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
        if (callNow) func(...args);
    };
};

// Throttle function for high-frequency events
window.throttle = function(func, limit) {
    let inThrottle;
    return function executedFunction(...args) {
        if (!inThrottle) {
            func.apply(this, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    };
};

// Optimized scroll handler with RAF
window.optimizedScrollHandler = function(callback, delay = 10) {
    return window.debounce(() => {
        requestAnimationFrame(callback);
    }, delay);
};

// Lazy load images with intersection observer
window.lazyLoadImages = function() {
    if ('IntersectionObserver' in window) {
        const imageObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const img = entry.target;
                    if (img.dataset.src) {
                        img.src = img.dataset.src;
                        img.removeAttribute('data-src');
                        img.classList.add('loaded');
                        imageObserver.unobserve(img);
                    }
                }
            });
        }, {
            rootMargin: '50px 0px',
            threshold: 0.1
        });

        document.querySelectorAll('img[data-src]').forEach(img => {
            imageObserver.observe(img);
        });
    } else {
        // Fallback for older browsers
        document.querySelectorAll('img[data-src]').forEach(img => {
            img.src = img.dataset.src;
            img.removeAttribute('data-src');
        });
    }
};

// Preload critical resources
window.preloadResource = function(href, as, type = null) {
    const link = document.createElement('link');
    link.rel = 'preload';
    link.href = href;
    link.as = as;
    if (type) link.type = type;
    link.onload = () => console.log(`Preloaded: ${href}`);
    document.head.appendChild(link);
};

// Optimize AOS initialization
window.initOptimizedAOS = function() {
    if (typeof AOS !== 'undefined') {
        AOS.init({
            duration: 600,  // Reduced from 800ms
            once: true,     // Only animate once for better performance
            easing: 'ease-out',
            offset: 50,     // Start animation earlier
            delay: 0,       // Remove delays for faster perception
            throttleDelay: 50 // Reduced throttle for smoother animations
        });
    }
};

// Efficient DOM ready state checker
window.whenReady = function(callback) {
    if (document.readyState === 'complete' || document.readyState === 'interactive') {
        setTimeout(callback, 0);
    } else {
        document.addEventListener('DOMContentLoaded', callback);
    }
};

// Performance-optimized event delegation
window.delegate = function(selector, event, handler) {
    document.addEventListener(event, function(e) {
        if (e.target.matches(selector) || e.target.closest(selector)) {
            handler.call(e.target.closest(selector) || e.target, e);
        }
    });
};

// Memory-efficient animation frame scheduler
window.scheduleWork = function(callback) {
    if ('requestIdleCallback' in window) {
        requestIdleCallback(callback, { timeout: 50 });
    } else {
        setTimeout(callback, 16); // ~60fps fallback
    }
};

// Initialize all performance optimizations
window.initPerformanceOptimizations = function() {
    // Preload critical fonts
    if (document.fonts && document.fonts.load) {
        Promise.all([
            document.fonts.load('400 16px "Noto Kufi Arabic"'),
            document.fonts.load('600 16px "Noto Kufi Arabic"')
        ]).then(() => {
            document.documentElement.classList.add('fonts-loaded');
        }).catch(() => {
            document.documentElement.classList.add('fonts-fallback');
        });
    }

    // Initialize lazy loading
    window.scheduleWork(() => {
        window.lazyLoadImages();
    });

    // Optimize AOS
    window.scheduleWork(() => {
        window.initOptimizedAOS();
    });
};

// Auto-initialize when script loads
window.whenReady(window.initPerformanceOptimizations); 