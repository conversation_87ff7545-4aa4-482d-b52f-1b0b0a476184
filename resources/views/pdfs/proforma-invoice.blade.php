<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <title>Proforma Invoice</title>
    <style>
        * { font-family: DejaVu Sans, sans-serif; font-size: 0.85rem}

        body {
            font-family: DejaVu Sans, serif;
            line-height: 1;
        }
        .header {
            text-align: center;
            margin-bottom: 20px;
        }
        .table {
            width: 100%;
            border-spacing: 0;
            margin-bottom: 20px;
        }
        .table th, .table td {
            border: 1px solid #ddd;
            padding: 5px;
            text-align: left;
        }

    </style>
</head>
<body>

<div class="header">
    <h1>Proforma Invoice</h1>
</div>
<table class="table">
    <thead>
            <tr>
                <th colspan="2" >Consignee Information</th>
                <th colspan="2">Shipper Information</th>
            </tr>
            <tr>
                <th colspan="2">Company/Store Name / {{$order->receiver_fullname}}</th>
                <th colspan="2">Company/Store Name / {{$order->shipper_name}}</th>
            </tr>
            <tr>
                <th>Name</th>
                <th>{{$order->receiver_fullname}}</th>
                <th>Name</th>
                <th>{{$order->shipper_name}}</th>
            </tr>
            <tr>
                <th>Address</th>
                <th>{{$order->receiver_address_line}}</th>
                <th>Name</th>
                <th>{{$order->shipper_address_line}}</th>
            </tr>
            <tr>
                <th>Telephone</th>
                <th>{{$order->receiver_phone}}</th>
                <th>Telephone</th>
                <th>{{$order->shipper_phone}}</th>
            </tr>
            <tr>
                <th>Fax</th>
                <th></th>
                <th>Fax</th>
                <th></th>
            </tr>
            <tr>
                <th>VAT Number</th>
                <th></th>
                <th>VAT Number</th>
                <th>312403741600003</th>
            </tr>
            <tr>
                <th>Country</th>
                <th>{{$order->receiver_country_code}}</th>
                <th>Country</th>
                <th>{{$order->shipperCountry->code_country}}</th>
            </tr>
        <tr>
        </tr>
    </thead>

</table>
<table class="table">
    <thead>
    <tr>
        <th colspan="4">Shipping Information</th>
    </tr>
    <tr>
        <th>Date of Export</th>
        <th>{{now()->format('Y-m-d')}}</th>
        <th>AWB Number</th>
        <th>{{$order->order_number}}</th>
    </tr>
    <tr>
        <th>Payment Mode</th>
        <th>{{$order->payment_method}}</th>
        <th>Contents</th>
        <th></th>
    </tr>
    </thead>
    <tbody>
    </tbody>
</table>

<table class="table">
    <thead>
    <tr>
        <th>Number of Pieces</th>
        <th>Specification of Commodities</th>
        <th>HS Code</th>
        <th>Country of Origin</th>
        <th>Quantity</th>
        <th>Unit Price</th>
        <th>Tax</th>
        <th>Total Amount</th>
    </tr>
    </thead>
    <tbody>
    @foreach ($order->items as $item)
        <tr>
            <td>{{ $item->quantity }}</td>
            <td>{{ $item->name }}</td>
            <td></td>
            <td>SA</td>
            <td>{{$item->quantity}}</td>
            <td>{{$item->price}}</td>
            <td>{{$item->tax / 100}}</td>
            <td>{{$item->total_price / 100}}</td>
        </tr>
    @endforeach
    </tbody>
</table>
<table class="table">
    <thead>
    <tr>
        <th>Total Amount</th>
        <th>Currency</th>
    </tr>
    <tr>
        <th>{{$order->order_grand_total}}</th>
        <th>SAR</th>
    </tr>
    </thead>
    <tbody>
    </tbody>
</table>
<table class="table">
    <thead>
    <tr>
        <th>Reason of Sending</th>
    </tr>
    </thead>
    <tbody>
    </tbody>
</table>

<table class="table">
    <thead>
    <tr>
        <th>I hereby certify that the items listed above are true and correct.</th>
    </tr>
    <tr>
        <th>Signature</th>
        <th>Date</th>
        <th>Last Name</th>
        <th>First Name</th>
    </tr>

    <tr>
        <th rowspan="6"></th>
        <th rowspan="6"></th>
        <th rowspan="6"></th>
        <th rowspan="6"></th>
    </tr>
    </thead>
</table>
</body>
</html>
