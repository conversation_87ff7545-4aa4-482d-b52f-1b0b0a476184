<!DOCTYPE html>
<html lang="{{ app()->getLocale() }}" dir="{{ app()->getLocale() === 'ar' ? 'rtl' : 'ltr' }}">
@include('layouts.head')

<style>
    .price-item{
        background-color: white;
        color: #6f42c1;
        border: 2px solid black;
        padding: 10px;
        border-radius: 10px;
        box-shadow: 2px 2px 0 #000000;
        transition: 0.3s ease;
        font-style: normal;
    }
</style>

<body>
<div class="nav-bg"></div>
<div class="p-0 container-fluid container-lg">
    <!-- Navbar Section -->
    @include('navbar')

    <!-- Shipping Price Comparison Section -->
    <section
        class="shipping-comparison py-5"
    >
        <div class="container text-center">
            <h1
                class="mb-4 font-kufi fw-bold"
            >
                {{ __('translation.shipping_comparison_title') }}
            </h1>
            <h6
                class="mb-5 font-kufi lead-color fw-medium fs-5"
            >
                {{ __('translation.shipping_comparison_subtitle') }}
            </h6>
            <livewire:calculator-form/>
        </div>
    </section>
</div>
@include('components.footer')



<script>
    // Add scrolled class to navbar and nav-bg on scroll
    window.addEventListener("scroll", function () {
        const navbar = document.querySelector(".navbar");
        const navBg = document.querySelector(".nav-bg");

        if (window.scrollY > 50) {
            if (navbar) navbar.classList.add("scrolled");
            if (navBg) navBg.classList.add("scrolled");
        } else {
            if (navbar) navbar.classList.remove("scrolled");
            if (navBg) navBg.classList.remove("scrolled");
        }
    });
</script>
<script>
    document.addEventListener("DOMContentLoaded", function () {
        function setupAutocomplete(inputId, listId, livewireProperty) {
            const input = document.getElementById(inputId);
            const cityList = document.getElementById(listId);

            input.addEventListener("input", function () {
                let query = input.value.trim();
                if (query.length < 1) {
                    cityList.style.display = "none";
                    return;
                }

                fetch(`/search-cities?query=${encodeURIComponent(query)}`)
                    .then(response => response.json())
                    .then(data => {
                        cityList.innerHTML = "";
                        if (data.length > 0) {
                            data.forEach(city => {
                                let item = document.createElement("div");
                                item.classList.add("dropdown-item");
                                item.innerHTML = `<strong>${city.name}</strong> - <span style="color: gray;">${city.name_ar}</span>`;

                                item.addEventListener("click", function () {
                                    input.value = city.name; // Set Arabic name in input
                                    cityList.style.display = "none";

                                        Livewire.dispatch("citySelected", { 
                                            property: livewireProperty, 
                                            value: city.name 
                                        });
                                });

                                cityList.appendChild(item);
                            });
                            cityList.style.display = "block";
                        } else {
                            cityList.style.display = "none";
                        }
                    })
                    .catch(error => console.error("Error fetching cities:", error));
            });

            document.addEventListener("click", function (e) {
                if (!input.contains(e.target) && !cityList.contains(e.target)) {
                    cityList.style.display = "none";
                }
            });
        }

        // Apply autocomplete with Livewire integration
        setupAutocomplete("departure", "departureList", "shipper");
        setupAutocomplete("destination", "destinationList", "receiver");
    });


</script>


@livewireScripts
</body>
</html>
