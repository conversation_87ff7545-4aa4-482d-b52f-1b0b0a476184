<footer
    class="footerhome py-5"
    data-aos="fade-up"
    data-aos-duration="800"
    data-aos-once="true"
>
    <div class="container">
        <h2 class="footer-heading font-noto">تواصل معنا</h2>
        <p class="footer-description font-kufi">
            <span>
                            إذا لم تجد ما تبحث عنه، تواصل معنا، يسعدنا الرد على جميع استفساراتكم

            </span>
            <br>


            <a dir="ltr" class="footer-email font-kufi" href="tel:+966115200879"
            >
                <span class="glyphicon glyphicon-earphone"
                ></span>+966 11 520 0879
            </a>


        </p>

        <p class="footer-email-description font-kufi">
            أو من خلال بريدنا الإلكتروني
        </p>
        <a href="mailto:<EMAIL> " class="footer-email font-kufi">
            <EMAIL> </a>
        <p class="footer-social-description font-kufi">
            أو تابعنا على شبكات التواصل الإجتماعي
        </p>

        <!-- Social Media Icons -->
        <div class="social-icons">
            {{--            <a href="#" class="social-icon">--}}
            {{--                <i class="fab fa-facebook fa-2x"></i>--}}
            {{--            </a>--}}
            {{--            <a href="#" class="social-icon">--}}
            {{--                <i class="fab fa-twitter fa-2x"></i>--}}
            {{--            </a>--}}
            <a href="https://www.instagram.com/gotreekcom?igsh=bjRqNGRnb3lzaHY2" class="social-icon" target="_blank"
               rel="noopener noreferrer">
                <i class="fab fa-instagram fa-2x"></i>
            </a>
            <a href="https://www.tiktok.com/@gotreekcom?_t=ZS-8t7TaHjZ2f4&_r=1" class="social-icon" target="_blank"
               rel="noopener noreferrer">
                <i class="fab fa-tiktok fa-2x"></i>
            </a>

        </div>
    </div>
</footer>
<!-- This site is converting visitors into subscribers and customers with https://respond.io -->
<script id="respond__widget"
        src="https://cdn.respond.io/webchat/widget/widget.js?cId=a5fe5edbfbe777224b5ea83715857f3"></script><!-- https://respond.io -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.4/jquery.min.js"></script>

<!-- Slick Carousel JS -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/slick-carousel/1.8.1/slick.min.js"></script>

<script src="https://cdnjs.cloudflare.com/ajax/libs/aos/2.3.4/aos.js"></script>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>

<script src="{{ URL::asset('build/js/script.js')}}"></script>

<script>
    // +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
    $(document).ready(function () {
        $(".your-class").slick({
            slidesToShow: 4,
            slidesToScroll: 1,
            dots: true,
            arrows: true,
            infinite: true,
            autoplay: true,
            autoplaySpeed: 1000,
            responsive: [
                {
                    breakpoint: 768,
                    settings: {
                        slidesToShow: 2,
                        slidesToScroll: 1,
                    },
                },
                {
                    breakpoint: 480,
                    settings: {
                        slidesToShow: 1,
                        slidesToScroll: 1,
                    },
                },
            ],
        });

        // Force Slick to refresh on window load and resize
        $(window).on("load resize", function () {
            $(".your-class").slick("setPosition");
        });

        AOS.init({
            duration: 1000,
            once: false,
        });

        window.addEventListener("scroll", function () {
            const navbar = document.querySelector(".navbar");
            const navBg = document.querySelector(".nav-bg");

            if (window.scrollY > 5) {
                if (navbar) navbar.classList.add("scrolled");
                if (navBg) navBg.classList.add("scrolled");
            } else {
                if (navbar) navbar.classList.remove("scrolled");
                if (navBg) navBg.classList.remove("scrolled");
            }
        });

        const navbarToggler = document.querySelector(".navbar-toggler");
        if (navbarToggler) {
            navbarToggler.addEventListener("click", function () {
                document.querySelector(".nav-bg").classList.toggle("active");
            });
        }
    });
    $(document).ready(function () {
        $(".your-class1").slick({
            slidesToShow: 4,
            slidesToScroll: 1,
            dots: true,
            arrows: true,
            infinite: true,
            autoplay: true,
            autoplaySpeed: 1000,
            responsive: [
                {
                    breakpoint: 768,
                    settings: {
                        slidesToShow: 2,
                        slidesToScroll: 1,
                    },
                },
                {
                    breakpoint: 480,
                    settings: {
                        slidesToShow: 1,
                        slidesToScroll: 1,
                    },
                },
            ],
        });

        // Force Slick to refresh on window load and resize
        $(window).on("load resize", function () {
            $(".your-class").slick("setPosition");
        });

        AOS.init({
            duration: 1000,
            once: false,
        });

        window.addEventListener("scroll", function () {
            const navbar = document.querySelector(".navbar");
            const navBg = document.querySelector(".nav-bg");

            if (window.scrollY > 5) {
                if (navbar) navbar.classList.add("scrolled");
                if (navBg) navBg.classList.add("scrolled");
            } else {
                if (navbar) navbar.classList.remove("scrolled");
                if (navBg) navBg.classList.remove("scrolled");
            }
        });

        const navbarToggler = document.querySelector(".navbar-toggler");
        if (navbarToggler) {
            navbarToggler.addEventListener("click", function () {
                document.querySelector(".nav-bg").classList.toggle("active");
            });
        }
    });
    $(document).ready(function () {
        $(".navbar-toggler").on("click", function () {
            $(".nav-bg").toggleClass("bg-white");
        });
    });
    // +++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++

</script>
