<div class="my-3 flex justify-between items-center text-base border rounded-lg relative w-full">
    <div class="flex items-center">
        <div class="p-3">
            <img style="width: 60.4px; height: 60.4px" class="w-15 h-15" src="{{asset($salesChannel['image'])}}" alt=""/>
        </div>
        <div class="ml-3">
            <span class="block text-black font-bold">{{$salesChannel['name']}}</span>
            <span class="block text-base font-normal">{{$salesChannel['name']}}</span>
        </div>
    </div>
    <div class="p-2 flex items-center">

        <label class="inline-flex items-center cursor-pointer" style="margin-left: 10px">
            <input
                type="checkbox"
                @checked($salesChannel['active'])
                wire:click="toggleStatus({{$salesChannel['id']}})"
                class="rounded border-gray-300 text-primary-600 shadow-sm focus:ring-primary-500"
            />
        </label>

        <!-- Edit Link -->
        <a class="block text-blue-600 text-sm font-medium rounded-lg border px-3 py-1 mr-3 hover:bg-gray-100"
           href="{{$salesChannel['formUrl']}}">
            <i class="fas fa-edit"></i>
            <span class="ml-1">@lang('translation.edit')</span>
        </a>

        @if($salesChannel['type'] === 'shopify' && $salesChannel['webhook_is_linked'] == false)
            <button
                type="button"
                class="block text-blue-600 text-sm font-medium rounded-lg border px-3 py-1 mr-3 hover:bg-gray-100"
                @if(empty($salesChannel['access_token']) || empty($salesChannel['api_key']) || empty($salesChannel['api_secret_key']))
                    disabled
                    title="@lang('translation.missing_credentials')"
                @endif
                wire:click="setupWebhook({{ $salesChannel['id'] }})"
                wire:loading.attr="disabled"
            >
                <i class="heroicon-m-link"></i>
                <span class="ml-1">@lang('translation.setup_webhook')</span>
            </button>
        @endif

        <!-- Ellipsis Link -->
        <a id="link11" class="block text-gray-400 px-2"
           href="{{$salesChannel['formUrl']}}">
            <i class="fa-solid fa-ellipsis"></i>
        </a>
    </div>
</div>
