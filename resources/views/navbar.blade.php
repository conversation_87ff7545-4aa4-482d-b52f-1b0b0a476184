<nav class="navbar navbar-expand-xl navbar-light w-100">
    <div class="container-fluid">
        <a class="navbar-brand pe-3" href="{{ route('homepage') }}">
            <img src="{{ URL::asset('build/images/treek-logo-name.png') }}" alt="logo" class="logo"/>
        </a>
        <div class="ps-3">
            <button
                class="navbar-toggler"
                type="button"
                data-bs-toggle="collapse"
                data-bs-target="#navbarNav"
                aria-controls="navbarNav"
                aria-expanded="false"
                aria-label="Toggle navigation"
            >
                <span class="navbar-toggler-icon"></span>
            </button>
        </div>
        <div class="collapse navbar-collapse justify-content-end" id="navbarNav">
            <ul class="navbar-nav">
                <li class="nav-item me-2">
                    <a class="nav-link text-black {{ Route::currentRouteName() === 'homepage' ? 'active-link' : '' }}"
                       href="{{ route('homepage') }}">{{ __('translation.home') }}</a>
                </li>
                <li class="nav-item me-2">
                    <a class="nav-link text-black {{ Route::currentRouteName() === 'solutions' ? 'active-link' : '' }}"
                       href="{{ route('solutions') }}">{{ __('translation.solutions') }}</a>
                </li>
                <li class="nav-item me-2">
                    <a class="nav-link text-black {{ Route::currentRouteName() === 'questions' ? 'active-link' : '' }}"
                       href="{{ route('questions') }}">{{ __('translation.faqs') }}</a>
                </li>
                <li class="nav-item me-2">
                    <a class="nav-link text-black {{ Route::currentRouteName() === 'calculator' ? 'active-link' : '' }}"
                       href="{{ route('calculator') }}">{{ __('translation.price_calculator') }}</a>
                </li>
                <li class="nav-item me-2">
                    <a class="nav-link text-black {{ Route::currentRouteName() === 'about' ? 'active-link' : '' }}"
                       href="{{ route('about') }}">{{ __('translation.our_story') }}</a>
                </li>
                <li class="nav-item me-2">
                    <a class="nav-link text-black {{ Route::currentRouteName() === 'shipment' ? 'active-link' : '' }}"
                       href="{{ route('shipment') }}">
                        <span>{{ __('translation.track_your_shipment') }}</span>
                    </a>
                </li>
                <li class="nav-item me-2">
                    <a class="nav-link font-noto main-color fw-bolder"
                       href="{{ route('filament.merchant.auth.login') }}">{{ __('translation.log_in') }}</a>
                </li>
                <li class="nav-item mx-2 py-2">
                    <a class="nav-link custom3-button font-noto main-color fw-bolder text-decoration-none"
                       href="{{ route('filament.merchant.auth.register') }}">{{ __('translation.register_now') }}</a>
                </li>
                <li class="d-flex justify-content-center align-items-center mt-3 mt-lg-0">
                    <form method="POST" action="{{ route('change.language') }}" class="d-inline">
                        @csrf
                        <div class="dropdown">
                            <button class="btn btn-light dropdown-toggle d-flex align-items-center" type="button"
                                    id="languageDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                                <img src="{{ asset('build/images/flags/' . app()->getLocale() . '.png') }}" alt="Flag"
                                     width="20" class="mx-2">
                                {{ app()->getLocale() === 'ar' ? __('translation.arabic') : __('translation.english') }}
                            </button>
                            <ul class="dropdown-menu" aria-labelledby="languageDropdown">
                                <li>
                                    <button type="submit" name="language" value="ar"
                                            class="dropdown-item d-flex align-items-center {{ app()->getLocale() === 'ar' ? 'active' : '' }}">
                                        <img src="{{ asset('build/images/flags/ar.png') }}" alt="Arabic Flag" width="20"
                                             class="mx-2"> {{ __('translation.arabic') }}
                                    </button>
                                </li>
                                <li>
                                    <button type="submit" name="language" value="en"
                                            class="dropdown-item d-flex align-items-center {{ app()->getLocale() === 'en' ? 'active' : '' }}">
                                        <img src="{{ asset('build/images/flags/en.png') }}" alt="English Flag"
                                             width="20" class="mx-2"> {{ __('translation.english') }}
                                    </button>
                                </li>
                            </ul>
                        </div>
                    </form>
                </li>
            </ul>
        </div>
    </div>
</nav>

<script>
    // Optimized navbar scroll handling
    window.whenReady(function() {
        const navbar = document.querySelector(".navbar");
        const navBg = document.querySelector(".nav-bg");
        const navbarToggler = document.querySelector(".navbar-toggler");

        // Use optimized scroll handler
        if (navbar || navBg) {
            const optimizedScrollCallback = window.optimizedScrollHandler(() => {
                const shouldAddScrolled = window.scrollY > 5;
                if (navbar) navbar.classList.toggle("scrolled", shouldAddScrolled);
                if (navBg) navBg.classList.toggle("scrolled", shouldAddScrolled);
            });
            
            window.addEventListener("scroll", optimizedScrollCallback, { passive: true });
        }

        // Navbar toggler with performance optimization
        if (navbarToggler && navBg) {
            navbarToggler.addEventListener("click", function () {
                navBg.classList.toggle("active");
            });
        }
    });
</script>
