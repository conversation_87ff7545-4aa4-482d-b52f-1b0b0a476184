@php use Carbon\Carbon; @endphp
    <!DOCTYPE html>
<html lang="ar">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>

    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Invoice</title>

    <style>
        * {
            font-family: DejaVu Sans, sans-serif;
        }

        /*body {*/
        /*    src: url('http://fonts.googleapis.com/earlyaccess/droidarabickufi.css');*/
        /*    font-family: 'Arial Unicode MS', serif;*/
        /*    direction: rtl;*/
        /*    text-align: right;*/
        /*}*/
    </style>

</head>

<style>
    body {
        font-size: 14px;
        margin: 0;
        padding: 10px;
    }

    .invoice-header {
        width: 100%;
        margin-bottom: 12px;
    }

    .invoice-header .company-logo {
        text-align: left;
        width: 65%;
        vertical-align: top;
    }

    .invoice-header .invoice-title {
        text-align: right;
        width: 35%;
        vertical-align: top;
    }

    .invoice-header .title {
        font-size: 20px;
    }

    .header img {
        width: 100px;
    }

    .invoice-container {
        width: 100%;
    }

    .header h2 {
        margin: 5px 0;
    }

    .company-details, .customer-details {
        width: 100%;
        margin-bottom: 12px;
    }

    .company-details td, .customer-details td {
        padding: 5px;
    }

    .customer-details .company-info {
        text-align: left;
        width: 50%;
        vertical-align: top;
    }

    .customer-details .company-info-invoice {
        text-align: right;
        vertical-align: top;
    }

    .invoice-details {
        width: 100%;
        margin-bottom: 12px;
    }

    .invoice-details td {
        padding: 5px;
    }

    .invoice-table {
        font-size: 10px;
        width: 100%;
        border-collapse: collapse;
        margin-bottom: 20px;
    }

    .invoice-table th, .invoice-table td {
        padding: 10px;
        text-align: center;
    }

    .invoice-table th {
        background-color: #8d51af;
        color: #fff;
    }

    .invoice-table tbody tr {
        border-bottom: 1px solid #8d51af
    }

    .invoice-summary-table {
        font-size: 10px;
        width: 100%;
        border-collapse: collapse;
    }

    .invoice-summary-table td {
        padding: 12px 8px;
        text-align: right;
    }

    .total-with-tva {
        background-color: #F5F4F3;
    }

    .tax-table {
        font-size: 10px;
        width: 100%;
        border-collapse: collapse;
        margin-bottom: 20px;
    }

    .tax-table th, .tax-table td {
        padding: 10px;
        text-align: center;
    }

    .tax-table th {
        background-color: #8d51af;
        color: #fff;
    }

    .tax-table tbody tr, .tax-table tfoot tr {
        border-bottom: 1px solid #8d51af;
    }

    .footer {
        text-align: left;
        margin-top: 20px;
    }
</style>
<body>

<table class="invoice-header">
    <tr>
        <td class="company-logo">
            <img src="{{ public_path('images/treek-logo-name.png') }}" height="100" alt="Treek Logo"><br><br>
            <strong>TREEK The Best Shipping Companies On One Platform</strong><br>
        </td>
        <td class="invoice-title">
            <p class="title"><strong>TAX SALES INVOICE</strong></p>
            <strong># INV-{{ $wallet->invoice->id }}</strong>
        </td>
    </tr>
</table>


<!-- Company & Customer Details -->
<table class="company-details">


    <tr>
        <td>
{{--             <p>(9159)</p>--}}
            <p>شركة مسار تريك لتقنية المعلومات</p>
            <p>(Jeddah, Saudi Arabia 22321)</p>
            <p><strong>(CR Number:</strong> 4030552775)</p>
            <p><strong>(VAT Number:</strong> 311250710800003)</p>
        </td>
    </tr>
</table>

<table class="customer-details">
    <tr>
        <td class="company-info">
            <p><strong>Customer Name:</strong> {{ $wallet->user->company_name ?? '' }}</p>
            <p><strong>State:</strong> {{ $wallet->user->companyCity->name ?? '' }}</p>
            <p><strong>CR Number:</strong> {{ $wallet->user->company_commercial_registration_number ?? '' }}</p>
            <p><strong>Vat Number:</strong> {{ $wallet->user->company_tax_number ?? '' }}</p>
        </td>
        <td class="company-info-invoice">
            <p><strong>Invoice Date:</strong> {{ Carbon::parse($wallet->created_at)->format('d M Y') }}</p>
            <p><strong>Payment Terms:</strong> Due On Receipt</p>
            <p><strong>Due Date:</strong> {{ Carbon::parse($wallet->created_at)->format('d M Y') }}</p>
        </td>
    </tr>
</table>

<!-- Invoice Table -->
<table class="invoice-table">
    <thead>
    <tr>
        <th>#</th>
        <th>Service Package & Description</th>
        <th>Qty</th>
        <th>Unit Price</th>
        <th>Taxable Amount</th>
        <th>Tax Rate</th>
        <th>Tax Amount</th>
        <th>Total Before Tax</th>
    </tr>
    </thead>
    <tbody>
    <tr>
        <td>1</td>
        <td>Treek Credit</td>
        <td>1</td>
        <td>{{ number_format($wallet->amount, 2) }}</td>
        <td>{{ number_format($wallet->amount, 2) }}</td>
        <td>15%</td>
        <td>{{ number_format($wallet->amount * 0.15, 2) }}</td>
        <td>{{ number_format($wallet->amount, 2) }}</td>
    </tr>
    </tbody>
</table>

<!-- Summary Table -->
<table class="invoice-summary-table">
    <tr>
        <td style="width: 40%;"></td> <!-- Empty left side -->
        <td style="width: 60%;">
            <table style="width: 100%; border-collapse: collapse;">
                <tr>
                    <td><strong>Total Before VAT:</strong></td>
                    <td>{{ number_format($wallet->amount, 2) }} SAR</td>
                </tr>
                <tr>
                    <td><strong>Total Taxable Amount:</strong></td>
                    <td>{{ number_format($wallet->amount, 2) }} SAR</td>
                </tr>
                <tr>
                    <td><strong>Value Added Tax - VAT (15%):</strong></td>
                    <td>{{ number_format($wallet->amount * 0.15, 2) }} SAR</td>
                </tr>
                <tr class="total-with-tva">
                    <td><strong>Total with VAT:</strong></td>
                    <td><strong>{{ number_format($wallet->amount * 1.15, 2) }} SAR</strong></td>
                </tr>
            </table>
        </td>
    </tr>
</table>

<!-- tax Table -->
<p>Tax Summary</p>
<table class="tax-table">
    <thead>
    <tr>
        <th style="width: 60%; text-align: left;">Tax Details</th>
        <th style="width: 20%; text-align: right;">Taxable Amount (SAR)</th>
        <th style="width: 20%; text-align: right;">Tax Amount (SAR)</th>
    </tr>
    </thead>
    <tbody>
    <tr>
        <td style="text-align: left;">Value Added Tax (VAT) (15%)</td>
        <td style="text-align: right;">SAR {{ number_format($wallet->amount, 2) }}</td>
        <td style="text-align: right;">{{ number_format($wallet->amount * 0.15, 2) }} SAR</td>
    </tr>
    </tbody>
    <tfoot>
    <tr class="total-tax">
        <td style="text-align: left;"><strong>Total</strong></td>
        <td style="text-align: right;"><strong>{{ number_format($wallet->amount, 2) }} SAR</strong></td>
        <td style="text-align: right;"><strong>{{ number_format($wallet->amount * 0.15, 2) }} SAR</strong></td>
    </tr>
    </tfoot>
</table>

<!-- Footer -->
<div class="footer">
    <p>Thanks for your business.</p>
</div>
{{-- Start new page --}}
<div style="page-break-before: always;"></div>

<div style="text-align: center; margin-top: 100px;">
    @if(isset($qrImage))
        <img src="data:image/png;base64,{{ $qrImage }}" alt="QR Code" style="width: 200px; margin-bottom: 20px;">
    @endif

    <p style="font-size: 14px; font-weight: bold;">
        This QR code has been generated as per ZATCA's regulations.
    </p>
</div>

</body>
</html>
