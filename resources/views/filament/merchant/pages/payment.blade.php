<x-filament-panels::page>


    @if ($transactionStatus)
        <pre>{{ json_encode($transactionStatus, JSON_PRETTY_PRINT) }}</pre>
    @else
        <p>Transaction is being processed...</p>
    <div class="text-center">
        <!-- Icon with animation -->
        <div class="flex justify-center mb-6">
            <div class="animate-spin rounded-full h-16 w-16 border-t-4 border-purple-500 border-opacity-50"></div>
        </div>
        <div class="flex justify-center mb-6">
            <img src="{{ URL::asset('build/images/payment_processing.png') }}" alt="Payment Processing" class="h-72 w-72">
        </div>
        <!-- Payment processing message -->
        <p class="text-xl font-semibold text-gray-800">
            {{ __('translation.payment_processing') }}
        </p>
    </div>
    @endif

</x-filament-panels::page>
