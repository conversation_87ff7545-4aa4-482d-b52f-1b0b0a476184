<x-filament-panels::page>
    <div class="mx-auto p-4">
        <!-- Main Content -->
        <div class="flex flex-wrap gap-x-8  ">
            <!-- Right Section -->
            <div class="flex items-center w-1/2">
                <a href="https://sahem.ksrelief.org/" target="_blank"><img src="{{ URL::asset('build/images/ghazza.png')}}" alt="صورة" class="rounded-lg "></a>
            </div>
            <!-- Left Section -->
            <div class="flex flex-col space-y-4 flex-1 gap-y-4">
                <!-- Card 1 -->
                <div class="shadow-md p-2 rounded-lg text-start max-w-xl ">
                    <div class="flex gap-x-3 items-center">
                        <div>
                            <lord-icon
                                src="https://cdn.lordicon.com/illfwbol.json"
                                trigger="hover"
                                colors="primary:#8930e8,secondary:#8930e8"
                                style="width:50px;height:50px">
                            </lord-icon>
                        </div>
                        <div>
                            <h3 class="font-bold text-lg">@lang('translation.setup_pickup_location')</h3>
                            <p class="text-gray-600">@lang('translation.setup_pickup_location_description')</p>
                        </div>
                    </div>
                    <div class="text-end">
                        <a href="{{route('filament.merchant.resources.warehouses.index')}}"
                           class="ml-auto font-bold bg-white hover:text-white text-purple-500 border border-purple-500 hover:bg-purple-500  px-6 py-1 rounded-lg">@lang('translation.start')</a>
                    </div>
                </div>
                <!-- Card 2 -->
                <div class="shadow-md p-2 rounded-lg text-start max-w-xl ">
                    <div class="flex gap-x-3 items-center">
                        <div>
                            <lord-icon
                                src="https://cdn.lordicon.com/bnxnryzv.json"
                                trigger="hover"
                                colors="primary:#8930e8,secondary:#8930e8"
                                style="width:50px;height:50px">
                            </lord-icon>
                        </div>
                        <div>
                            <h3 class="font-bold text-lg ">@lang('translation.connect_your_store_title')</h3>
                            <p class="text-gray-600">@lang('translation.connect_your_store_description')</p>

                        </div>
                    </div>
                    <div class="text-end">
                        <a href="{{route('filament.merchant.pages.manage-sales-channels')}}"
                           class="ml-auto font-bold bg-white hover:text-white text-purple-500 border border-purple-500 hover:bg-purple-500  px-6 py-1 rounded-lg">@lang('translation.start')</a>
                    </div>
                </div>
                <!-- Card 3 -->
                <div class="shadow-md p-2 rounded-lg text-start max-w-xl ">
                    <div class="flex gap-x-3 items-center">
                        <div>
                            <lord-icon
                                src="https://cdn.lordicon.com/uriapfou.json"
                                trigger="hover"
                                colors="primary:#8930e8,secondary:#8930e8"
                                style="width:50px;height:50px">
                            </lord-icon>
                        </div>
                        <div>
                            <h3 class="font-bold text-lg ">@lang('translation.shipping_box_dimensions')
                                <h3>
                                    <p class="text-gray-600">@lang('translation.shipping_box_time_saver')

                                    </p>
                        </div>
                    </div>
                    <div class="text-end">
                        <a href="{{route('filament.merchant.resources.boxes.index')}}"
                           class=" ml-auto font-bold bg-white hover:text-white text-purple-500 border border-purple-500 hover:bg-purple-500  px-6 py-1 rounded-lg">@lang('translation.start')</a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Bottom Section -->
        <div class="flex flex-wrap  mt-8 justify-center">
            <!-- Block 1 -->
            <div class="flex flex-col items-center  rounded-lg w-full md:w-1/3">
                <div class="text-purple-500 text-3xl mb-2">
                    <lord-icon
                        src="https://cdn.lordicon.com/zqhvubpn.json"
                        trigger="hover"
                        colors="primary:#000000,secondary:#8930e8"
                        style="width:70px;height:70px">
                    </lord-icon>
                </div>
                <h3 class="font-bold text-lg text-center">@lang('translation.contact_via_chat')</h3>
                <p class="text-gray-600 text-center max-w-xs">@lang('translation.submit_issue_via_chat')</p>
            </div>
            <!-- Block 2 -->
            <div class="flex flex-col items-center  rounded-lg w-full md:w-1/3">
                <div class="text-purple-500 text-3xl mb-2">
                    <lord-icon
                        src="https://cdn.lordicon.com/xrdkdttl.json"
                        trigger="hover"
                        colors="primary:#8930e8,secondary:#000000"
                        style="width:70px;height:70px">
                    </lord-icon>
                </div>
                <h3 class="font-bold text-lg text-center">@lang('translation.book_call')</h3>
                <p class="text-gray-600 text-center max-w-xs">@lang('translation.schedule_meeting')</p>
            </div>
            <!-- Block 3 -->
            <div class="flex flex-col items-center  rounded-lg w-full md:w-1/3">
                <div class="text-purple-500 text-3xl mb-2">
                    <lord-icon
                        src="https://cdn.lordicon.com/osqflufj.json"
                        trigger="hover"
                        colors="primary:#8930e8,secondary:#242424"
                        style="width:70px;height:70px">
                    </lord-icon>
                </div>
                <h3 class="font-bold text-lg text-center">@lang('translation.get_more_knowledge')</h3>
                <p class="text-gray-600 text-center max-w-xs">@lang('translation.get_more_knowledge_treek')</p>
            </div>
        </div>
    </div>
</x-filament-panels::page>
