<x-filament-panels::page>
    <div>
        <x-filament::tabs label="Content tabs">
            <!-- Tab 1 -->
            <x-filament::tabs.item
                :active="$activeTab === 'tab1'"
                wire:click="setActiveTab('tab1')"
            >
                @lang('translation.more_connections')
            </x-filament::tabs.item>

            <!-- Tab 2 -->
            <x-filament::tabs.item
                :active="$activeTab === 'tab2'"
                wire:click="setActiveTab('tab2')"
            >
                @lang('translation.connected_sales_channels')
            </x-filament::tabs.item>
        </x-filament::tabs>

        <!-- Tab Content -->
        <div class="mt-4 bg-white">
            @if ($activeTab === 'tab1')
                <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4 mt-4">
                    @foreach ($channels as $channel)
                        @php
                            $channel_name = $channel->name;
                            $channel_btn = $channel->action_btn_label;

                            $channel_description = $channel->description;
                        @endphp
                        <div class="border rounded-lg p-4">
                            <img src="{{ asset($channel->logo) }}" alt="{{ $channel->name }} icon" class="w-10 h-10"/>
                            <h3 class="font-semibold">{{$channel_name}}</h3>
                            <p>{{$channel->description}}</p>
                            <x-filament::button
                                color="primary"
                                wire:click="handleChannelClick({{ $channel }})"
                            >
                                {{ $channel_btn }}

                            </x-filament::button>
                        </div>
                    @endforeach
                </div>
            @elseif ($activeTab === 'tab2')
                <!-- Livewire Component for Connected Sales -->
                <livewire:show-connected-sales/>
            @endif
        </div>
        <!-- Modals -->
        <livewire:channel-modal/>
        <livewire:shopify-channel-modal/>
        <livewire:zid-channel-modal/>
        <livewire:woocommerce-channel-modal/>
    </div>
</x-filament-panels::page>
