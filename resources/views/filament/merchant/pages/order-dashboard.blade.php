@php
    $sharedData = [
        'filters' => $this->filters ?? [],
        ...$this->getWidgetData(),
    ];
@endphp

<x-filament-panels::page class="fi-dashboard-page">
    {{-- You can access the page instance with $this --}}
     <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
    {{-- First widget --}}
    @livewire(\App\Filament\Merchant\Widgets\StatsOverview::class, [$sharedData])

    {{-- Inject your filters form here --}}
    <div class="col-span-1 md:col-span-2">
    @if (method_exists($this, 'filtersForm'))
        <div class="mt-4">
            {{ $this->filtersForm }}
        </div>
    @endif
    </div>

    {{-- Remaining widgets --}}
    @livewire(\App\Filament\Merchant\Widgets\GlobalOverview::class, $sharedData)
    @livewire(\App\Filament\Merchant\Widgets\OrdersWeeklyChart::class, $sharedData)
    @livewire(\App\Filament\Merchant\Widgets\MonthlyOrdersChart::class, $sharedData)
    @livewire(\App\Filament\Merchant\Widgets\TopDestinationsWidget::class, $sharedData)
    @livewire(\App\Filament\Merchant\Widgets\SalesChannelsDistributionWidget::class, $sharedData)
    @livewire(\App\Filament\Merchant\Widgets\ShippingCompaniesDistributionWidget::class, $sharedData)
    </div>
</x-filament-panels::page>