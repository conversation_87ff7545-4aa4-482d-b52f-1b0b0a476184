<x-filament-panels::page>
    <div>
        <div>
            <x-filament::tabs label="Content tabs">
                <!-- Tab 1 -->
                <x-filament::tabs.item
                    :active="$activeTab === 'tab1'"
                    wire:click="setActiveTab('tab1')"
                >
                    @lang('translation.more_connections')
                </x-filament::tabs.item>

                <!-- Tab 2 -->
                <x-filament::tabs.item
                    :active="$activeTab === 'tab2'"
                    wire:click="setActiveTab('tab2')"
                >
                    @lang('translation.connected_shipping_partners')

                </x-filament::tabs.item>
            </x-filament::tabs>

            <!-- Tab Content -->
            <div class="mt-4 bg-white">
                @if ($activeTab === 'tab1')
                    <div class="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 gap-4 p-4">
                        @foreach ($channels as $channel)
                            <div class="bg-white border border-gray-200 rounded shadow hover:shadow-xl hover:scale-105 hover:border-blue-400 hover:bg-blue-50 transition-all duration-300 ease-in-out cursor-pointer p-4 flex flex-col items-center text-center transform hover:-translate-y-1 group"
                                 wire:click="handleChannelClick('{{ $channel->identifier }}')">
                                
                                <!-- Logo -->
                                <div class="mb-3 mx-auto">
                                    <img src="{{asset('build/images/'. $channel->logo_big)}}" 
                                         alt="{{ $channel->name }} icon" 
                                         class="w-24 h-16 object-cover rounded border border-gray-200 group-hover:scale-110 transition-transform duration-300 ease-in-out"/>
                                </div>
                                
                                <!-- Company Name -->
                                <h3 class="text-lg font-bold text-gray-800 mb-2 group-hover:text-blue-600 transition-colors duration-200">
                                    {{ $channel->courier_name }}
                                </h3>
                                
                                <!-- Service Type -->
                                <div class="mb-2">
                                    <span class="inline-block bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full group-hover:bg-blue-500 group-hover:text-white group-hover:scale-105 transition-all duration-200 ease-in-out">
                                        {{ $channel->service_name }}
                                    </span>
                                </div>
                                
                                <!-- Service Details -->
                                <div class="space-y-1 text-xs text-gray-600 mb-3 w-full group-hover:text-gray-800 transition-colors duration-200">
                                    <!-- Delivery Time -->
                                    <div class="flex justify-between items-center">
                                        <span class="font-medium">@lang('translation.delivery_timee'):</span>
                                        <span class="group-hover:font-semibold transition-all duration-200">{{ $channel->delivery_time }}</span>
                                    </div>
                                    
                                    <!-- Base Price -->
                                    <div class="flex justify-between items-center">
                                        <span class="font-medium">@lang('translation.price'):</span>
                                        <span class="font-bold text-green-600 group-hover:text-green-500 group-hover:scale-105 transition-all duration-200">{{ number_format($channel->base_price / 100, 2) }} ر.س</span>
                                    </div>
                                </div>
                                
                                <!-- Action Button -->
                                <button class="w-full bg-blue-600 group-hover:bg-blue-700 group-hover:scale-105 group-hover:shadow-lg text-white font-medium py-2 px-3 text-sm rounded-lg transition-all duration-200 ease-in-out shadow-sm">
                                    @lang('translation.choose')
                                </button>
                            </div>
                        @endforeach
                    </div>
                @elseif ($activeTab === 'tab2')
                    <livewire:ShowConnectedShipments/>
                @endif
            </div>
        </div>

    </div>

</x-filament-panels::page>
