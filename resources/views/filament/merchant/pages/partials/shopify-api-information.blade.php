<div>
    <div>
        <h3 class="font-bold text-lg mb-4">{{ __('translation.shopify_api_connection.shopify_application') }}</h3>
        <p class="card-title-desc mb-6">
            {{ __('translation.shopify_api_connection.shopify_store_connection') }}
        </p>

        <div class="bg-white rounded-lg shadow-sm border p-6">
            <h4 class="font-semibold text-md mb-6 text-gray-800">{{ __('translation.shopify_api_connection.title') }}</h4>

            <div class="mt-10 mb-6 p-6 bg-blue-50 rounded-lg border border-blue-200">
                <div class="text-muted">
                    {!! __('translation.shopify_api_connection.shopify_application_instructions') !!}
                </div>
            </div>

            <!-- Remark Section -->
           <div class="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
               <p class="text-red-700 font-medium">{{ __('translation.shopify_api_connection.remark') }}</p>
            </div>

            <div class="space-y-8">
                <!-- Step 1 -->
                <div class="flex items-start space-x-6">
                    <div class="flex-shrink-0 w-10 h-10 bg-primary-600 text-white rounded-full flex items-center justify-center font-semibold text-lg">
                        1
                    </div>
                    <div class="flex-1 pt-1 mb-3">
                        <p class="text-gray-700  mx-3 mb-5 text-base">{{ __('translation.shopify_api_connection.step_1_title') }}</p>
                        <x-filament::button 
                            color="primary" 
                            tag="a" 
                            href="https://web.zid.sa/account/settings/shipping-requirements/1094" 
                            target="_blank">
                            <x-filament::icon
                                name="heroicon-o-arrow-top-right-on-square"
                                class="w-4 h-4 mr-2"
                            />
                            {{ __('translation.shopify_api_connection.step_1_button_text') }}
                        </x-filament::button>
                        <div class="bg-gray-100 rounded-lg p-6 mt-4 border-2 border-dashed border-gray-300 min-h-[120px] flex items-center justify-center">
                            <div class="text-center mt-4">
                                <img src="{{ asset("build/images/shopify_api_connection_photos/step1.png") }}" style="height: 25rem;">
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Step 2 -->
                <div class="flex items-start space-x-6">
                    <div class="flex-shrink-0 w-10 h-10 bg-primary-600 text-white rounded-full flex items-center justify-center font-semibold text-lg">
                        2
                    </div>
                    <div class="flex-1 pt-1 mb-3">
                        <p class="text-gray-700  mx-3 mb-5 text-base">{{ __('translation.shopify_api_connection.step_2_title') }}</p>
                        <div class="bg-gray-100 rounded-lg p-6 border-2 border-dashed border-gray-300 min-h-[120px] flex items-center justify-center">
                            <div class="text-center">
                                <img src="{{ asset("build/images/shopify_api_connection_photos/step2.png") }}" style="height: 25rem;">
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Step 3 -->
                <div class="flex items-start space-x-6">
                    <div class="flex-shrink-0 w-10 h-10 bg-primary-600 text-white rounded-full flex items-center justify-center font-semibold text-lg">
                        3
                    </div>
                    <div class="flex-1 pt-1 mb-3">
                        <p class="text-gray-700  mx-3 mb-5 text-base">{{ __('translation.shopify_api_connection.step_3_title') }}</p>
                        <div class="bg-gray-100 rounded-lg p-6 border-2 border-dashed border-gray-300 min-h-[120px] flex items-center justify-center">
                            <div class="text-center">
                                <img src="{{ asset("build/images/shopify_api_connection_photos/step3.png") }}" style="height: 25rem;">
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Step 4 -->
                <div class="flex items-start space-x-6">
                    <div class="flex-shrink-0 w-10 h-10 bg-primary-600 text-white rounded-full flex items-center justify-center font-semibold text-lg">
                        4
                    </div>
                    <div class="flex-1 pt-1 mb-3">
                        <p class="text-gray-700  mx-3 mb-5 text-base">{{ __('translation.shopify_api_connection.step_4_title') }}</p>
                        <div class="bg-gray-100 rounded-lg p-6 border-2 border-dashed border-gray-300 min-h-[120px] flex items-center justify-center">
                            <div class="text-center">
                                <img src="{{ asset('build/images/shopify_api_connection_photos/' . Lang::locale() . '/step4_' . Lang::locale() . '.png') }}" style="height: 25rem;">
                            </div>
                        </div>
                    </div>
                </div>

                <div class="mt-8 space-y-4">
                    <div class="p-4 bg-green-50 border border-green-200 rounded-lg">
                        <p class="text-green-800">{{ __('translation.shopify_api_connection.general_descrition_1') }}</p>
                    </div>
                </div>

                <!-- Step 4 -->
                <div class="flex items-start space-x-6">
                    <div class="flex-shrink-0 w-10 h-10 bg-primary-600 text-white rounded-full flex items-center justify-center font-semibold text-lg">
                        4
                    </div>
                    <div class="flex-1 pt-1 mb-3">
                        <p class="text-gray-700  mx-3 mb-5 text-base">{{ __('translation.shopify_api_connection.step_5_title') }}</p>
                        <div class="bg-gray-100 rounded-lg p-6 border-2 border-dashed border-gray-300 min-h-[120px] flex items-center justify-center">
                            <div class="text-center">
                                <img src="{{ asset('build/images/shopify_api_connection_photos/' . Lang::locale() . '/step5_' . Lang::locale() . '.png') }}" style="height: 25rem;">
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Step 4 -->
                <div class="flex items-start space-x-6">
                    <div class="flex-shrink-0 w-10 h-10 bg-primary-600 text-white rounded-full flex items-center justify-center font-semibold text-lg">
                        4
                    </div>
                    <div class="flex-1 pt-1 mb-3">
                        <p class="text-gray-700  mx-3 mb-5 text-base">{{ __('translation.shopify_api_connection.step_6_title') }}</p>
                        <div class="bg-gray-100 rounded-lg p-6 border-2 border-dashed border-gray-300 min-h-[120px] flex items-center justify-center">
                            <div class="text-center">
                                <img src="{{ asset('build/images/shopify_api_connection_photos/' . Lang::locale() . '/step6_' . Lang::locale() . '.png') }}" style="height: 25rem;">
                            </div>
                        </div>
                    </div>
                </div>

            </div>

            <!-- General Descriptions 1 Section -->



            <div class="mt-8 flex justify-center">
                <lord-icon
                    src="https://cdn.lordicon.com/mfblariy.json"
                    trigger="loop"
                    stroke="bold"
                    state="loop-cycle"
                    colors="primary:#121331,secondary:#8930e8"
                    style="width:150px;height:150px">
                </lord-icon>
            </div>
        </div>
    </div>
</div>
