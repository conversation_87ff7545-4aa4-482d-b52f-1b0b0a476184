<div>
    <div>
        <h3 class="font-bold text-lg mb-4">{{ __('translation.shopify_webhook_connection.shopify_webhook_app') }}</h3>
        
        <div class="bg-white rounded-lg shadow-sm border p-6">
            <h4 class="font-semibold text-md mb-6 text-gray-800">{{ __('translation.shopify_webhook_connection.title') }}</h4>

            <div class="mt-10 mb-6 p-6 bg-blue-50 rounded-lg border border-blue-200">
                <div class="text-muted">
                    {!! __('translation.shopify_webhook_connection.shopify_application_instructions') !!}
                </div>
            </div>
        
            <div class="space-y-8">
                <!-- Step 1 -->
                <div class="flex items-start space-x-6">
                    <div class="flex-shrink-0 w-10 h-10 bg-primary-600 text-white rounded-full flex items-center justify-center font-semibold text-lg">
                        1
                    </div>
                    <div class="flex-1 pt-1 mb-3">
                        <p class="text-gray-700  mx-3 mb-5 text-base">{{ __('translation.shopify_webhook_connection.step_1_title') }}</p>
                        <div class="bg-gray-100 rounded-lg p-6 border-2 border-dashed border-gray-300 min-h-[120px] flex items-center justify-center">
                            <div class="text-center">
                                <img src="{{ asset('build/images/shopify_webhook_connection_photos/' . Lang::locale() . '/step1_' . Lang::locale() . '.png') }}" style="height: 25rem;">
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Step 2 -->
                <div class="flex items-start space-x-6">
                    <div class="flex-shrink-0 w-10 h-10 bg-primary-600 text-white rounded-full flex items-center justify-center font-semibold text-lg">
                        2
                    </div>
                    <div class="flex-1 pt-1 mb-3">
                        <p class="text-gray-700  mx-3 mb-5 text-base">{{ __('translation.shopify_webhook_connection.step_2_title') }}</p>
                        <div class="bg-gray-100 rounded-lg p-6 border-2 border-dashed border-gray-300 min-h-[120px] flex items-center justify-center">
                            <div class="text-center">
                                <img src="{{ asset("build/images/shopify_webhook_connection_photos/step2.png") }}" style="height: 25rem;">
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Step 3 -->
                <div class="flex items-start space-x-6">
                    <div class="flex-shrink-0 w-10 h-10 bg-primary-600 text-white rounded-full flex items-center justify-center font-semibold text-lg">
                        3
                    </div>
                    <div class="flex-1 pt-1 mb-3">
                        <p class="text-gray-700 mx-3 mb-5 text-base">{{ __('translation.shopify_webhook_connection.step_3_title') }}</p>
                        <div class="bg-gray-100 rounded-lg p-6 border-2 border-dashed border-gray-300 min-h-[120px] flex items-center justify-center">
                            <div class="text-center">
                                <img src="{{ asset('build/images/shopify_webhook_connection_photos/' . Lang::locale() . '/step3_' . Lang::locale() . '.png') }}" style="height: 25rem;">
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Step 4 -->
                <div class="flex items-start space-x-6">
                    <div class="flex-shrink-0 w-10 h-10 bg-primary-600 text-white rounded-full flex items-center justify-center font-semibold text-lg">
                        4
                    </div>
                    <div class="flex-1 pt-1 mb-3">
                        <p class="text-gray-700 mx-3 mb-5 text-base">{{ __('translation.shopify_webhook_connection.step_4_title') }}</p>
                        <div class="bg-gray-100 rounded-lg p-6 border-2 border-dashed border-gray-300 min-h-[120px] flex items-center justify-center">
                            <div class="text-center">
                                <img src="{{ asset("build/images/shopify_webhook_connection_photos/step4.png") }}" style="height: 25rem;">
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Step 5 -->
                <div class="flex items-start space-x-6">
                    <div class="flex-shrink-0 w-10 h-10 bg-primary-600 text-white rounded-full flex items-center justify-center font-semibold text-lg">
                        5
                    </div>
                    <div class="flex-1 pt-1 mb-3">
                        <p class="text-gray-700 mx-3 mb-5 text-base">{{ __('translation.shopify_webhook_connection.step_5_title') }}</p>
                        <div class="bg-gray-100 rounded-lg p-6 border-2 border-dashed border-gray-300 min-h-[120px] flex items-center justify-center">
                            <div class="text-center">
                                <img src="{{ asset("build/images/shopify_webhook_connection_photos/step5.png") }}" style="height: 25rem;">
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Step 6 -->
                <div class="flex items-start space-x-6">
                    <div class="flex-shrink-0 w-10 h-10 bg-primary-600 text-white rounded-full flex items-center justify-center font-semibold text-lg">
                        6
                    </div>
                    <div class="flex-1 pt-1 mb-3">
                        <p class="text-gray-700 mx-3 mb-5 text-base">{{ __('translation.shopify_webhook_connection.step_6_title') }}</p>
                        <div class="bg-gray-100 rounded-lg p-6 border-2 border-dashed border-gray-300 min-h-[120px] flex items-center justify-center">
                            <div class="text-center">
                                <img src="{{ asset("build/images/shopify_webhook_connection_photos/step6.png") }}" style="height: 25rem;">
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Step 7 -->
                <div class="flex items-start space-x-6">
                    <div class="flex-shrink-0 w-10 h-10 bg-primary-600 text-white rounded-full flex items-center justify-center font-semibold text-lg">
                        7
                    </div>
                    <div class="flex-1 pt-1 mb-3">
                        <p class="text-gray-700 mx-3 mb-5 text-base">{{ __('translation.shopify_webhook_connection.step_7_title') }}</p>
                        <div class="bg-gray-100 rounded-lg p-6 border-2 border-dashed border-gray-300 min-h-[120px] flex items-center justify-center">
                            <div class="text-center">
                                <img src="{{ asset("build/images/shopify_webhook_connection_photos/step7.png") }}" style="height: 25rem;">
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Step 8 -->
                <div class="flex items-start space-x-6">
                    <div class="flex-shrink-0 w-10 h-10 bg-primary-600 text-white rounded-full flex items-center justify-center font-semibold text-lg">
                        8
                    </div>
                    <div class="flex-1 pt-1 mb-3">
                        <p class="text-gray-700 mx-3 mb-5 text-base">{{ __('translation.shopify_webhook_connection.step_8_title') }}</p>
                        <div class="bg-gray-100 rounded-lg p-6 border-2 border-dashed border-gray-300 min-h-[120px] flex items-center justify-center">
                            <div class="text-center">
                                <img src="{{ asset("build/images/shopify_webhook_connection_photos/step8.png") }}" style="height: 25rem;">
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Step 8_2 -->
                <div class="flex items-start space-x-6">
                    <div class="flex-1 pt-1 mb-3">
                        <p class="text-gray-700 mx-3 mb-5 text-base">{{ __('translation.shopify_webhook_connection.step_8_2_title') }}</p>
                        <div class="bg-gray-100 rounded-lg p-6 border-2 border-dashed border-gray-300 min-h-[120px] flex items-center justify-center">
                            <div class="text-center">
                                <img src="{{ asset('build/images/shopify_webhook_connection_photos/' . Lang::locale() . '/step8_2_' . Lang::locale() . '.png') }}" style="height: 25rem;">
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Step 9 -->
                <div class="flex items-start space-x-6">
                    <div class="flex-shrink-0 w-10 h-10 bg-primary-600 text-white rounded-full flex items-center justify-center font-semibold text-lg">
                        9
                    </div>
                    <div class="flex-1 pt-1 mb-3">
                        <p class="text-gray-700 mx-3 mb-5 text-base">{{ __('translation.shopify_webhook_connection.step_9_title') }}</p>
                        <div class="bg-gray-100 rounded-lg p-6 border-2 border-dashed border-gray-300 min-h-[120px] flex items-center justify-center">
                            <div class="text-center">
                                <img src="{{ asset("build/images/shopify_webhook_connection_photos/step9.png") }}" style="height: 25rem;">
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Step 10 -->
                <div class="flex items-start space-x-6">
                    <div class="flex-shrink-0 w-10 h-10 bg-primary-600 text-white rounded-full flex items-center justify-center font-semibold text-lg">
                        10
                    </div>
                    <div class="flex-1 pt-1 mb-3">
                        <p class="text-gray-700 mx-3 mb-5 text-base">{{ __('translation.shopify_webhook_connection.step_10_title') }}</p>
                        <div class="bg-gray-100 rounded-lg p-6 border-2 border-dashed border-gray-300 min-h-[120px] flex items-center justify-center">
                            <div class="text-center">
                                <img src="{{ asset("build/images/shopify_webhook_connection_photos/step10.png") }}">
                            </div>
                            <br>
                            <div class="text-center">
                                <img src="{{ asset("build/images/shopify_webhook_connection_photos/step10_2.png") }}">
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Step 11 -->
                <div class="flex items-start space-x-6">
                    <div class="flex-shrink-0 w-10 h-10 bg-primary-600 text-white rounded-full flex items-center justify-center font-semibold text-lg">
                        11
                    </div>
                    <div class="flex-1 pt-1 mb-3">
                        <p class="text-gray-700 mx-3 mb-5 text-base">{{ __('translation.shopify_webhook_connection.step_11_title') }}</p>
                        <div class="bg-gray-100 rounded-lg p-6 border-2 border-dashed border-gray-300 min-h-[120px] flex items-center justify-center">
                            <div class="text-center">
                                <img src="{{ asset("build/images/shopify_webhook_connection_photos/step11.png") }}" style="height: 25rem;">
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Step 12 -->
                <div class="flex items-start space-x-6">
                    <div class="flex-shrink-0 w-10 h-10 bg-primary-600 text-white rounded-full flex items-center justify-center font-semibold text-lg">
                        12
                    </div>
                    <div class="flex-1 pt-1 mb-3">
                        <p class="text-gray-700 mx-3 mb-5 text-base">{{ __('translation.shopify_webhook_connection.step_12_title') }}</p>
                        <div class="bg-gray-100 rounded-lg p-6 border-2 border-dashed border-gray-300 min-h-[120px] flex items-center justify-center">
                            <div class="text-center">
                                <img src="{{ asset("build/images/shopify_webhook_connection_photos/step12.png") }}">
                            </div>
                            <br>
                            <div class="text-center">
                                <img src="{{ asset("build/images/shopify_webhook_connection_photos/step12_2.png") }}">
                            </div>
                            <br>
                            <div class="text-center">
                                <img src="{{ asset('build/images/shopify_webhook_connection_photos/' . Lang::locale() . '/step12_3_' . Lang::locale() . '.png') }}">
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Step 13 -->
                <div class="flex items-start space-x-6">
                    <div class="flex-shrink-0 w-10 h-10 bg-primary-600 text-white rounded-full flex items-center justify-center font-semibold text-lg">
                        13
                    </div>
                    <div class="flex-1 pt-1 mb-3">
                        <p class="text-gray-700 mx-3 mb-5 text-base">{{ __('translation.shopify_webhook_connection.step_13_title') }}</p>
                        <div class="bg-gray-100 rounded-lg p-6 border-2 border-dashed border-gray-300 min-h-[120px] flex items-center justify-center">
                            <div class="text-center">
                                <img src="{{ asset('build/images/shopify_webhook_connection_photos/' . Lang::locale() . '/step13_' . Lang::locale() . '.png') }}" style="height: 25rem;">
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Step 14 -->
                <div class="flex items-start space-x-6">
                    <div class="flex-shrink-0 w-10 h-10 bg-primary-600 text-white rounded-full flex items-center justify-center font-semibold text-lg">
                        14
                    </div>
                    <div class="flex-1 pt-1 mb-3">
                        <p class="text-gray-700 mx-3 mb-5 text-base">{{ __('translation.shopify_webhook_connection.step_13_title') }}</p>
                        <div class="bg-gray-100 rounded-lg p-6 border-2 border-dashed border-gray-300 min-h-[120px] flex items-center justify-center">
                            <div class="text-center">
                                <img src="{{ asset('build/images/shopify_webhook_connection_photos/' . Lang::locale() . '/step14_' . Lang::locale() . '.png') }}" style="height: 25rem;">
                            </div>
                        </div>
                    </div>
                </div>

            </div>

            <!-- General Descriptions Section -->
            <div class="mt-8 space-y-4">
                <div class="p-4 bg-blue-50 border border-blue-200 rounded-lg">
                    <p class="text-blue-800">{{ __('translation.shopify_webhook_connection.general_descrition_1') }}</p>
                </div>
                <div class="p-4 bg-green-50 border border-green-200 rounded-lg">
                    <p class="text-green-800">{{ __('translation.shopify_webhook_connection.general_descrition_2') }}</p>
                </div>
            </div>

            <div class="mt-8 flex justify-center">
                <lord-icon
                    src="https://cdn.lordicon.com/mfblariy.json"
                    trigger="loop"
                    stroke="bold"
                    state="loop-cycle"
                    colors="primary:#121331,secondary:#8930e8"
                    style="width:150px;height:150px">
                </lord-icon>
            </div>
        </div>
    </div>
</div>
