<!DOCTYPE html>
<html lang="{{ app()->getLocale() }}" dir="{{ app()->getLocale() == 'ar' ? 'rtl' : 'ltr' }}">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ app()->getLocale() == 'ar' ? 'المتجر متصل بالفعل - تريك' : 'Store Already Connected - Treek' }}</title>
    <script src="https://cdn.lordicon.com/lordicon.js"></script>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body { 
            font-family: {{ app()->getLocale() == 'ar' ? "'Cairo', 'Tajawal', sans-serif" : "'Inter', sans-serif" }}; 
        }
        .rtl { direction: rtl; }
        .ltr { direction: ltr; }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Language Switcher -->
    <div class="fixed top-4 right-4 z-50">
        <div class="bg-white shadow-lg rounded-lg p-2">
            <a href="{{ request()->fullUrlWithQuery(['lang' => app()->getLocale() == 'ar' ? 'en' : 'ar']) }}" 
               class="flex items-center space-x-2 px-3 py-2 text-sm font-medium text-gray-700 hover:text-purple-600 transition-colors">
                <span>{{ app()->getLocale() == 'ar' ? '🇺🇸 EN' : '🇸🇦 عربي' }}</span>
            </a>
        </div>
    </div>

    <div class="mx-auto p-4 max-w-4xl">
        <!-- Main Alert Container -->
        <div class="flex flex-col items-center justify-center min-h-[60vh] space-y-8">
            
            <!-- Alert Icon -->
            <div class="text-center">
                <lord-icon
                    src="https://cdn.lordicon.com/tdrtiskw.json"
                    trigger="loop"
                    colors="primary:#8930e8,secondary:#ff6b6b"
                    style="width:120px;height:120px">
                </lord-icon>
            </div>

            <!-- Main Alert Card -->
            <div class="bg-white shadow-lg rounded-xl p-8 max-w-2xl w-full border border-red-100">
                <div class="text-center space-y-4">
                    <!-- Title -->
                    <h1 class="text-3xl font-bold text-gray-800 mb-2">
                        {{ app()->getLocale() == 'ar' ? 'المتجر متصل بالفعل' : 'Store Already Connected' }}
                    </h1>
                    
                    <!-- Subtitle -->
                    <p class="text-lg text-gray-600 leading-relaxed">
                        {{ app()->getLocale() == 'ar' 
                            ? 'يبدو أن هذا المتجر متصل بالفعل بحساب آخر في نظامنا. يمكن ربط كل متجر بحساب واحد فقط في كل مرة.' 
                            : 'It looks like this store is already connected to another account in our system. Each store can only be connected to one account at a time.' }}
                    </p>
                </div>
            </div>

            <!-- Information Cards -->
            <div class="grid md:grid-cols-2 gap-6 w-full max-w-4xl">
                <!-- What Happened Card -->
                <div class="bg-white shadow-md rounded-lg p-6 border-l-4 border-red-400">
                    <div class="flex items-start space-x-4 {{ app()->getLocale() == 'ar' ? 'space-x-reverse' : '' }}">
                        <div class="flex-shrink-0">
                            <lord-icon
                                src="https://cdn.lordicon.com/msiypqop.json"
                                trigger="hover"
                                colors="primary:#8930e8,secondary:#ff6b6b"
                                style="width:50px;height:50px">
                            </lord-icon>
                        </div>
                        <div>
                            <h3 class="font-bold text-lg text-gray-800 mb-2">
                                {{ app()->getLocale() == 'ar' ? 'ماذا حدث؟' : 'What Happened?' }}
                            </h3>
                            <p class="text-gray-600 text-sm leading-relaxed">
                                {{ app()->getLocale() == 'ar' 
                                    ? 'عندما حاولت ربط متجرك، اكتشف نظامنا أنه مسجل بالفعل بحساب آخر. هذا يمنع الاتصالات المكررة ويضمن سلامة البيانات.' 
                                    : 'When you tried to connect your store, our system detected that it\'s already registered with another account. This prevents duplicate connections and ensures data integrity.' }}
                            </p>
                        </div>
                    </div>
                </div>

                <!-- What You Can Do Card -->
                <div class="bg-white shadow-md rounded-lg p-6 border-l-4 border-blue-400">
                    <div class="flex items-start space-x-4 {{ app()->getLocale() == 'ar' ? 'space-x-reverse' : '' }}">
                        <div class="flex-shrink-0">
                            <lord-icon
                                src="https://cdn.lordicon.com/uriapfou.json"
                                trigger="hover"
                                colors="primary:#8930e8,secondary:#4f46e5"
                                style="width:50px;height:50px">
                            </lord-icon>
                        </div>
                        <div>
                            <h3 class="font-bold text-lg text-gray-800 mb-2">
                                {{ app()->getLocale() == 'ar' ? 'ماذا يمكنك أن تفعل؟' : 'What You Can Do' }}
                            </h3>
                            <p class="text-gray-600 text-sm leading-relaxed">
                                {{ app()->getLocale() == 'ar' 
                                    ? 'إذا كنت تعتقد أن هذا متجرك، يرجى الاتصال بفريق الدعم لدينا. يمكننا مساعدتك في استعادة الوصول أو حل أي مشاكل في ملكية الحساب.' 
                                    : 'If you believe this is your store, please contact our support team. We can help you regain access or resolve any account ownership issues.' }}
                            </p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="flex flex-col sm:flex-row gap-4 w-full max-w-md">
                <!-- Contact Support Button -->
                <a href="mailto:<EMAIL>" 
                   class="flex-1 bg-purple-600 hover:bg-purple-700 text-white font-bold py-3 px-6 rounded-lg transition-colors duration-200 flex items-center justify-center space-x-2 {{ app()->getLocale() == 'ar' ? 'space-x-reverse' : '' }}">
                    <lord-icon
                        src="https://cdn.lordicon.com/uriapfou.json"
                        trigger="hover"
                        colors="primary:#ffffff,secondary:#ffffff"
                        style="width:20px;height:20px">
                    </lord-icon>
                    <span>{{ app()->getLocale() == 'ar' ? 'اتصل بالدعم' : 'Contact Support' }}</span>
                </a>

                <!-- Go Back Button -->
                <a href="/merchant/dashboard" 
                   class="flex-1 bg-gray-100 hover:bg-gray-200 text-gray-700 font-bold py-3 px-6 rounded-lg transition-colors duration-200 flex items-center justify-center space-x-2 {{ app()->getLocale() == 'ar' ? 'space-x-reverse' : '' }}">
                    <lord-icon
                        src="https://cdn.lordicon.com/uriapfou.json"
                        trigger="hover"
                        colors="primary:#6b7280,secondary:#6b7280"
                        style="width:20px;height:20px">
                    </lord-icon>
                    <span>{{ app()->getLocale() == 'ar' ? 'العودة' : 'Go Back' }}</span>
                </a>
            </div>

            <!-- Help Section -->
            <div class="bg-gradient-to-r from-purple-50 to-blue-50 rounded-lg p-6 w-full max-w-2xl">
                <div class="text-center space-y-3">
                    <h3 class="font-bold text-lg text-gray-800">
                        {{ app()->getLocale() == 'ar' ? 'تحتاج مساعدة؟' : 'Need Help?' }}
                    </h3>
                    <p class="text-gray-600 text-sm">
                        {{ app()->getLocale() == 'ar' 
                            ? 'فريق الدعم لدينا هنا لمساعدتك في حل هذه المشكلة بسرعة. نستجيب عادةً خلال 24 ساعة.' 
                            : 'Our support team is here to help you resolve this issue quickly. We typically respond within 24 hours.' }}
                    </p>
                    <div class="flex justify-center space-x-6 text-sm text-gray-500 {{ app()->getLocale() == 'ar' ? 'space-x-reverse' : '' }}">
                        <span class="flex items-center space-x-1 {{ app()->getLocale() == 'ar' ? 'space-x-reverse' : '' }}">
                            <lord-icon
                                src="https://cdn.lordicon.com/uriapfou.json"
                                trigger="hover"
                                colors="primary:#6b7280,secondary:#6b7280"
                                style="width:16px;height:16px">
                            </lord-icon>
                            <span>{{ app()->getLocale() == 'ar' ? 'دعم البريد الإلكتروني' : 'Email Support' }}</span>
                        </span>
                        <span class="flex items-center space-x-1 {{ app()->getLocale() == 'ar' ? 'space-x-reverse' : '' }}">
                            <lord-icon
                                src="https://cdn.lordicon.com/uriapfou.json"
                                trigger="hover"
                                colors="primary:#6b7280,secondary:#6b7280"
                                style="width:16px;height:16px">
                            </lord-icon>
                            <span>{{ app()->getLocale() == 'ar' ? 'الدردشة المباشرة' : 'Live Chat' }}</span>
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Language Detection Script -->
    <script>
        // Set language based on URL parameter or browser preference
        const urlParams = new URLSearchParams(window.location.search);
        const langParam = urlParams.get('lang');
        
        if (langParam) {
            document.documentElement.lang = langParam;
            document.documentElement.dir = langParam === 'ar' ? 'rtl' : 'ltr';
        } else {
            // Default to browser language or English
            const browserLang = navigator.language || navigator.userLanguage;
            const isArabic = browserLang.startsWith('ar');
            document.documentElement.lang = isArabic ? 'ar' : 'en';
            document.documentElement.dir = isArabic ? 'rtl' : 'ltr';
        }
    </script>
</body>
</html>
