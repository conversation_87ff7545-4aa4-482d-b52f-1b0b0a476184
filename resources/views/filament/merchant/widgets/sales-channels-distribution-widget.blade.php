<x-filament::card>
    <div class="flex items-center justify-between mb-4">
        <h3 class="text-lg font-bold">{{ $this->getHeading() }}</h3>
    </div>
    <ul id="sales-channels-list" class="space-y-3 w-full">
        @foreach ($this->getData()['salesChannels'] as $channel)
            <li class="flex items-center justify-between w-full">
                <span class="w-1/3">{{ $channel->merchantName }}</span>
                <div class="flex items-center w-2/3">
                    <div class="h-2 bg-gray-300 rounded-full w-full mr-2">
                        <div
                            class="h-2 bg-green-500 rounded-full"
                            style="width: {{ $channel->percentage }}%;"
                        ></div>
                    </div>
                    <span class="ml-2 text-gray-600">{{ $channel->percentage }}%</span>
                </div>
            </li>
        @endforeach
    </ul>
</x-filament::card>
