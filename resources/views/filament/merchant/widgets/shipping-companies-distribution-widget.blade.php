<x-filament::card>
<div class="p-4 bg-white rounded-lg shadow">
    <div class="flex items-center justify-between mb-4">
        <h3 class="text-lg font-bold">{{ $this->getHeading() }}</h3>
{{--        <select class="border-gray-300 rounded" wire:model="filter">--}}
{{--            <option value="30">آخر 30 يومًا</option>--}}
{{--            <option value="7">آخر 7 أيام</option>--}}
{{--        </select>--}}
    </div>
    <ul class="space-y-3">
        @foreach ($this->getData()['shippingCompanies'] as $company)
            <li class="flex items-center justify-between">
                <span>{{ $company->shipmentCompany }}</span>
                <div class="flex items-center w-1/2">
                    <div class="h-2 bg-gray-300 rounded-full w-full mr-2">
                        <div class="h-2 bg-blue-500 rounded-full" style="width: {{ $company->percentage }}%;"></div>
                    </div>
                    <span class="ml-2 text-gray-600">{{ $company->percentage }}%</span>
                </div>
            </li>
        @endforeach
    </ul>
</div>
</x-filament::card>
