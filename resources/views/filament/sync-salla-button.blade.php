@if ($merchant)
    <button
        id="sync-salla-button"
        type="button"
        class="font-bold bg-white hover:text-white text-purple-500 border border-purple-500 hover:bg-purple-500 px-6 py-1 rounded-lg"
    >
        مزامنة سلة
    </button>

    <script>
        document.addEventListener('DOMContentLoaded', function () {
            const syncButton = document.getElementById('sync-salla-button');

            syncButton.addEventListener('click', async function () {
                syncButton.disabled = true;
                syncButton.innerText = 'مزامنة ...';


                try {
                    let response = await fetch('https://api.salla.dev/admin/v2/orders?expanded=true', {
                        method: 'GET',
                        headers: {
                            'Authorization': `Bearer {{$merchant->access_token}}`
                        },
                    });

                    if (response.status === 401) {
                        console.warn('Token expired. Refreshing...');
                        const refreshed = await fetch('https://api.salla.dev/admin/v2/auth/refresh', {
                            method: 'POST',
                            headers: {
                                'Authorization': `Bearer {{$merchant->refresh_token}}`
                            },
                        });

                        if (!refreshed.ok) {
                            throw new Error('Failed to refresh token');
                        }

                        const refreshedData = await refreshed.json();
                        var accessToken = refreshedData.access_token;
                       var refreshToken = refreshedData.refresh_token;

// Update server
                        await fetch('{{ route("salla.update-tokens") }}', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'Accept': 'application/json',
                                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') ?? '',
                            },
                            body: JSON.stringify({
                                merchant_id: {{$merchant->id}},
                                access_token: accessToken,
                                refresh_token: refreshToken,
                                expire_in: refreshedData.expire_in
                            })
                        });

                        // Retry fetch with new access token
                        response = await fetch('https://api.salla.dev/admin/v2/orders?expanded=true', {
                            method: 'GET',
                            headers: {
                                'Authorization': `Bearer ${accessToken}`
                            },
                        });
                    }

                    const result = await response.json();

                    if (response.ok && result.success) {
                        const orders = result.data;

                        console.log('Orders:', orders);

                        for (const order of orders) {
                            const success = await sendToLocalServer(order);

                            if (!success) {
                                dispatchFilamentNotification('danger', 'Error', `Stopped at order ${order.reference_id} due to server error.`);
                                break;
                            }
                        }

                        dispatchFilamentNotification('success', 'Done', 'Finished processing orders.');
                    } else {
                        dispatchFilamentNotification('danger', 'Error', result.message || 'Failed to fetch orders!');
                        console.error('Error response:', result);
                    }
                } catch (error) {
                    dispatchFilamentNotification('danger', 'Network Error', 'Could not reach Salla API.');
                    console.error('Network Error:', error);
                } finally {
                    syncButton.disabled = false;
                    syncButton.innerText = 'مزامنة سلة';
                }
            });

            async function sendToLocalServer(order) {
                try {
                    const payload = {
                        event: "order.created",
                        merchant: 226998834,
                        data: order
                    };

                    const response = await fetch('{{$merchant->webhook_url}}', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'Accept': 'application/json',
                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') ?? '',
                        },
                        body: JSON.stringify(payload),
                    });

                    if (response.status === 500) {
                        console.error('Server error 500 when sending order', order.reference_id);
                        return false;
                    }

                    const result = await response.json();

                    if (!response.ok) {
                        console.error('Failed to send order', order.reference_id, result);
                        return false;
                    } else {
                        console.log('Successfully sent order', order.reference_id, result);
                        return true;
                    }
                } catch (error) {
                    console.error('Error sending order', order.reference_id, error);
                    return false;
                }
            }

            function dispatchFilamentNotification(type, title, body) {
                window.dispatchEvent(new CustomEvent('filament-notify', {
                    detail: { type, title, body }
                }));
            }
        });
    </script>
@else
    <span></span>
@endif
