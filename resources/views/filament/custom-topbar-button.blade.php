{{auth()->user()->wallet_balance_formatted}} {{__('translation.sar')}}

<button
    type="button"
    x-data
    x-on:click="$dispatch('open-modal', { id: 'account-recharge-modal' })"
    class="font-bold bg-white hover:text-white text-purple-500 border border-purple-500 hover:bg-purple-500  px-6 py-1 rounded-lg"
>

    @lang('translation.account_recharge')
</button>
<x-filament::modal id="account-recharge-modal" width="6xl">
    @livewire('account-recharge-wizard')
</x-filament::modal>


<script>
    document.addEventListener('livewire:initialized', () => {
        Livewire.on('closeModal', () => {
            window.dispatchEvent(new CustomEvent('close-modal', { detail: { id: 'account-recharge-modal' } }));
        });
    });
</script>
