<div>
    <x-filament::modal
        id="zid-channel-modal"
        heading="{{ __('translation.choose_connections_method') }}"
        :closeable="true"
        width="4xl"
        wire:model.defer="showModal"
    >
        <div>
            <!-- Connection Options -->
            <div class="flex flex-col gap-4">
                <!-- Zapier Connection Option -->
                <label
                    for="zid_connection_type_zapier"
                    class="flex items-center border p-4 rounded-lg hover:bg-gray-100 transition w-full cursor-pointer justify-between"
                >
                    <div class="mr-4 flex align-items-center">
                        <img
                            src="https://storage.googleapis.com/tryoto-public/sales-channels-logo/zid.png"
                            class="w-24 h-24 object-scale-down"
                            width="90.64px"
                            height="90.64px"
                            alt="{{ __('translation.zid_oauth') }}"
                        />
                        <div class="w-24 h-24 flex items-center justify-center bg-orange-100 rounded-lg">
                            <i class="bx bx-sitemap text-orange-600 text-4xl"></i>
                        </div>
                        <div class="text-right ml-4">
                            <p class="text-lg font-semibold text-gray-800">
                                <i class="bx bx-link-alt text-primary mr-2"></i>
                                {{ __('translation.zid_zapier')}}
                            </p>
                            <p class="text-sm text-gray-600">
                                {{ __('translation.zid_api_connection_description') }}
                            </p>
                        </div>
                    </div>

                    <input type="radio" id="zid_connection_type_zapier" name="zid_connection_type" value="zapier" checked />
                </label>

                <!-- Direct Zid App Connection Option -->
                <label
                    for="zid_connection_type_oauth"
                    class="flex items-center border p-4 rounded-lg hover:bg-gray-100 transition w-full cursor-pointer justify-between"
                >
                    <div class="mr-4 flex align-items-center">
                        <img
                            src="https://storage.googleapis.com/tryoto-public/sales-channels-logo/zid.png"
                            class="w-24 h-24 object-scale-down"
                            width="90.64px"
                            height="90.64px"
                            alt="{{ __('translation.zid_oauth') }}"
                        />
                        <div class="text-right ml-4">
                            <p class="text-lg font-semibold text-gray-800">
                                <i class="bx bx-mobile-alt text-primary mr-2"></i>
                                {{ __('translation.zid_oauth')}}
                            </p>
                            <p class="text-sm text-gray-600">
                                {{ __('translation.zid_oauth_connection_description') }}
                            </p>
                        </div>
                    </div>

                    <input type="radio" id="zid_connection_type_oauth" name="zid_connection_type" value="oauth" />
                </label>
            </div>

            <!-- Footer -->
            <div class="text-left mt-6 space-x-2">
                <x-filament::button wire:click="$set('showModal', false)" color="secondary" outlined>
                    {{ __('translation.cancel_btn')}}
                </x-filament::button>
                <x-filament::button id="zid-next-btn" color="primary">
                    {{ __('translation.next_step_btn')}} <i class="bx bx-send align-middle"></i>
                </x-filament::button>
            </div>
        </div>

        @push('scripts')
            <script>
                document.getElementById('zid-next-btn').addEventListener('click', function () {
                    const chosenType = document.querySelector('input[name=zid_connection_type]:checked').value;
                    if (chosenType === 'zapier') {
                        // For Zapier: Go to manual merchant creation
                        window.location.href = "/merchant/zid-merchants/create";
                    } else if (chosenType === 'oauth') {
                        // For OAuth: Go directly to Zid OAuth authorization
                        window.location.href = "/merchant/zid-merchants/create";
                    }
                });
            </script>
        @endpush
    </x-filament::modal>
</div>
