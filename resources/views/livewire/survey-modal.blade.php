<div>
    @if($showModal)
        <div class="fixed inset-0 z-50 flex items-center justify-center bg-gray-900 bg-opacity-50">
            <div class="bg-white rounded-lg shadow-lg w-11/12 md:w-1/3" style="padding: 40px">
                <div class="modal-header flex flex-col items-center text-center">
                    <!-- Common Header -->
                    <img
                        src="https://storage.googleapis.com/studio1-prod-blob/d3f0a997-608e-40e3-8e5e-e7cf4204c1b5/hi-five-hand-gesture.svg"
                        alt="" width="150" height="120">
                    <br>
                    <h1 class="font-bold text-xl">{{ __('translation.welcome_to_treek') }}
                        . {{ Auth::user()->first_name }}!
                    </h1>
                    <br>
                </div>


                <div class="modal-body text-center">
                    @if ($step === 1)
                        <h3 class="font-semibold text">{{ __('translation.have_ecommerce_store') }}</h3>
                        <br>
                        <p>{!!  __('translation.auto_shipping_info') !!}</p>
                        <br>
                        <p class="font-semibold text">{{ __('translation.have_online_store')}}</p>
                        <br>


                        <div class="form-check form-radio-outline form-radio-primary mb-3 custom-radio mb-2">
                            <label class="form-check-label " for="ecommerceYes">{{ __('translation.yes') }}</label>
                            <input type="radio" class="form-check-input" id="ecommerceYes" value="Yes"
                                   wire:model="ecommerceStore">
                        </div>

                        <div class="form-check form-radio-outline form-radio-primary mb-3 custom-radio">
                            <label class="form-check-label" for="ecommerceNo"> {{ __('translation.no') }}</label>
                            <input type="radio" class="form-check-input" id="ecommerceNo" value="No"
                                   wire:model="ecommerceStore">
                        </div>

                        <button class="btn btn-primary mt-3 custom-button modal-button"
                                wire:click="nextStep"> {{ __('translation.next') }}</button>



                    @elseif ($step === 2)
                        <h3>Which industry is more likely to represent your business?</h3>
                        @foreach ($industries as $industry)
                            <div class="form-check form-radio-outline form-radio-primary mb-3"
                            >

                                <input type="radio" id="industry-{{ $industry }}" class="form-check-input"
                                       value="{{ $industry }}" wire:model="businessIndustry">
                                <label class="form-check-label" for="industry-{{ $industry }}">
                                    {{ $industry }}
                                </label>
                            </div>
                        @endforeach
                        <button class="btn btn-primary mt-3" wire:click="nextStep">Next</button>
                    @elseif ($step === 3)
                        <h3>What is your average monthly number of orders?</h3>
                        @foreach ($orderOptions as $option)
                            <div class="form-check form-radio-outline form-radio-primary mb-3"

                            <input type="radio" id="orders-{{ $option }}" class="form-check-input"
                                   value="{{ $option }}" wire:model="monthlyOrders">
                            <label class="form-check-label" for="orders-{{ $option }}">
                                {{ $option }}
                            </label>
                </div>
                @endforeach
                <button class="btn btn-primary mt-3" wire:click="nextStep">Next</button>
                @elseif ($step === 4)
                    <h3>Would you like to pull your orders by connecting your E-commerce store or create the order
                        manually?</h3>
                    <button class="btn btn-outline-primary"
                            onclick="window.location.href='/sales-channels/online-stores'">Connect Ecommerce Store
                    </button>
                    <button class="btn btn-outline-primary" onclick="window.location.href='/order'">Create Order
                        Manually
                    </button>
                @endif
            </div>
        </div>
</div>
@endif
<style>
    .custom-radio {
        border: 2px solid #4C9BF5;
        border-radius: 8px;
        padding: 10px 15px;
        background-color: #EAF3FF;
        display: flex;
        justify-content: start;
        gap: 5px;
        align-items: center;
    }

    .custom-radio input[type="radio"] {
        width: 18px;
        height: 18px;
        accent-color: #4C9BF5;
    }

    .modal-button {
        background-color: #4C9BF5;
        color: white;
        border: none;
        padding: 10px 20px;
        border-radius: 8px;
        cursor: pointer;
    }

    .modal-button:hover {
        background-color: #3A8BE0;
    }
</style>

