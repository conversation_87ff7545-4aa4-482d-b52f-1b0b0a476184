<div>
    <x-filament::modal
        id="woocommerce-channel-modal"
        heading="{{ __('translation.choose_connections_method') }}"
        :closeable="true"
        width="4xl"
        wire:model.defer="showModal"
    >
        <div>
            <!-- Connection Options -->
            <div class="flex flex-col gap-4">
                <!-- REST API Connection Option -->
                <label
                    for="woocommerce_connection_type_rest"
                    class="flex items-center border p-4 rounded-lg hover:bg-gray-100 transition w-full cursor-pointer justify-between"
                >
                    <div class="mr-4 flex align-items-center">
                        <img
                            src="{{ asset('images/woocommerce-logo.png') }}"
                            class="w-24 h-24 object-scale-down"
                            width="90.64px"
                            height="90.64px"
                            alt="{{ __('translation.woocommerce_rest_api') }}"
                        />
                        <div class="text-right">
                            <p class="text-lg font-semibold text-gray-800">
                                <i class="bx bx-cog text-primary mr-2"></i>
                                {{ __('translation.woocommerce_rest_api')}}
                            </p>
                            <p class="text-sm text-gray-600">
                                {{ __('translation.woocommerce_rest_api_connection_description') }}
                            </p>
                        </div>
                    </div>

                    <input type="radio" id="woocommerce_connection_type_rest" name="woocommerce_connection_type" value="rest" checked />
                </label>

                <!-- Webhook Connection Option -->
                <label
                    for="woocommerce_connection_type_webhook"
                    class="flex items-center border p-4 rounded-lg bg-gray-200 transition w-full cursor-pointer justify-between"
                >
                    <div class="mr-4 flex align-items-center">
                        <img
                            src="{{ asset('images/woocommerce-logo.png') }}"
                            class="w-24 h-24 object-scale-down"
                            width="90.64px"
                            height="90.64px"
                            alt="{{ __('translation.woocommerce_webhook') }}"
                        />
                        <div class="text-right">
                            <p class="text-lg font-semibold text-gray-800">
                                <i class="bx bx-link text-primary mr-2"></i>
                                {{ __('translation.woocommerce_webhook')}}
                            </p>
                            <p class="text-sm text-gray-600">
                                {{ __('translation.woocommerce_webhook_connection_description') }}
                            </p>
                        </div>
                    </div>

                    <input type="radio" id="woocommerce_connection_type_webhook" name="woocommerce_connection_type" value="webhook" disabled />
                </label>
            </div>

            <!-- Footer -->
            <div class="text-left mt-6 space-x-2">
                <x-filament::button wire:click="$set('showModal', false)" color="secondary" outlined>
                    {{ __('translation.cancel_btn')}}
                </x-filament::button>
                <x-filament::button id="woocommerce-next-btn" color="primary">
                    {{ __('translation.next_step_btn')}} <i class="bx bx-send align-middle"></i>
                </x-filament::button>
            </div>
        </div>

        @push('scripts')
            <script>
                document.getElementById('woocommerce-next-btn').addEventListener('click', function () {
                    const chosenType = document.querySelector('input[name=woocommerce_connection_type]:checked').value;
                    if (chosenType === 'rest') {
                        window.location.href = "/merchant/woo-commerce-merchants/create";
                    } else if (chosenType === 'webhook') {
                        window.location.href = "/merchant/woo-commerce-merchants/create?type=webhook";
                    }
                });
            </script>
        @endpush
    </x-filament::modal>
</div>
