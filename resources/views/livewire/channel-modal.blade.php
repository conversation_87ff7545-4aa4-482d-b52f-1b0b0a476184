<div>
    <x-filament::modal
        id="channel-modal"
        heading="{{ __('translation.choose_connections_method') }}"
        :closeable="true"
        width="4xl"
        wire:model.defer="showModal"
    >
        <div>
            <!-- First Option -->
            <div class="flex flex-col gap-4">
{{--                <label--}}
{{--                    for="connection_type_app"--}}
{{--                    class="flex items-center border p-4 rounded-lg hover:bg-gray-100 transition w-full cursor-pointer justify-between"--}}
{{--                >--}}
{{--                    <div class="mr-4 flex align-items-center">--}}
{{--                        <img--}}
{{--                            src="https://storage.googleapis.com/tryoto-public/sales-channels-logo/salla.png"--}}
{{--                            class="w-24 h-24 object-scale-down"--}}
{{--                            width="90.64px"--}}
{{--                            height="90.64px"--}}
{{--                            alt="سلة"--}}
{{--                        />--}}
{{--                        <div class="text-right">--}}
{{--                            <p class="text-lg font-semibold text-gray-800">سلة</p>--}}
{{--                            <p class="text-sm text-gray-600">--}}
{{--                                قم بتوصيل متجرك في سلة مع تطبيق Treek من خلال متجر تطبيقات سلة.--}}
{{--                            </p>--}}
{{--                        </div>--}}
{{--                    </div>--}}

{{--                    <input type="radio" id="connection_type_app" name="connection_type" value="app" disabled/>--}}
{{--                </label>--}}

                <!-- Second Option -->
                <label
                    for="connection_type_webhook"
                    class="flex items-center border p-4 rounded-lg hover:bg-gray-100 transition w-full cursor-pointer justify-between"
                >
                    <div class="mr-4 flex align-items-center">
                        <img
                            src="https://storage.googleapis.com/tryoto-public/sales-channels-logo/salla.png"
                            class="w-24 h-24 object-scale-down"
                            width="90.64px"
                            height="90.64px"
                            alt="{{ __('translation.salla_webhook') }}"
                        />
                        <div class="text-right">
                            <p class="text-lg font-semibold text-gray-800">{{ __('translation.salla_webhook')}}</p> 
                            <p class="text-sm text-gray-600">
                            {{ __('translation.salla_connection_type_webhook_description') }}
                                
                            </p>
                        </div>
                    </div>

                    <input type="radio" id="connection_type_webhook" name="connection_type" value="webhook" />
                </label>
            </div>

            <!-- Footer -->
            <div class="text-left mt-6 space-x-2">
                <x-filament::button wire:click="$set('showModal', false)" color="secondary" outlined>
                    {{ __('translation.cancel_btn')}}
                </x-filament::button>
                <x-filament::button id="next-btn" color="primary">
                    {{ __('translation.next_step_btn')}} <i class="bx bx-send align-middle"></i>
                </x-filament::button>
            </div>
        </div>

        @push('scripts')
            <script>
                document.getElementById('next-btn').addEventListener('click', function () {
                    const chosenType = document.querySelector('input[name=connection_type]:checked').value;
                    if (chosenType === 'app') {
                        window.location.href = "/merchant/salla-setup";
                    } else if (chosenType === 'webhook') {
                        window.location.href = "/merchant/salla-merchants/create";
                    }
                });
            </script>
        @endpush
    </x-filament::modal>
</div>
