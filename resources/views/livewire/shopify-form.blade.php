<div>
    <div class="row">
        <div class="col-lg-12">
            <div class="card">
                <div class="card-body">
                    <div class="row">
                        <div class="col-xl-12">
                            <div>
                                <h5 class="font-size-14">Shopify Credentials</h5>
                                <p class="card-title-desc">Please enter below details to complete the integration</p>

                                <!-- end accordion -->
                            </div>
                        </div>
                        <!-- end col -->
                    </div>
                    <!-- end row -->
                    <div class="row">
                        <div class="col-xl-12">
                            <div class="card">
                                <div class="card-body">
                                    <form wire:submit="save" id="my-form">
                                        @csrf
                                        <div class="row align-items-center">
                                            <div class="col-md-6">
                                                <div class="mb-3">
                                                    <label for="formrow-firstname-input" class="form-label" >Store Name *</label>
                                                    <input type="text" class="form-control" id="formrow-firstname-input" name="name" wire:model="name">
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <label for="formrow-firstname-input" class="form-label">Activate</label>
                                                <div class="form-check form-switch form-switch-md mb-3">
                                                    <input class="form-check-input" type="checkbox" id="flexSwitchCheckChecked" wire:model="active">
                                                    <label class="form-check-label" for="flexSwitchCheckChecked"></label>
                                                </div>
                                            </div>

                                        </div>
                                        <div class="d-flex justify-content-end">
                                            <button
                                                class="btn btn-primary d-flex justify-content-center custom-font blue-border blue-bg btn3 bg-black"
                                                type="submit"
                                                wire:click="testConnection"
                                            >
                                                <span class="d-inline-block"><i class="fas fa-plug me-1"></i></span>
                                                <span class="fw-medium">Test connection</span>
                                            </button>
                                        </div>
                                    </form>
                                </div>
                                <!-- end card body -->
                            </div>
                            <!-- end card -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- end row -->

</div>
