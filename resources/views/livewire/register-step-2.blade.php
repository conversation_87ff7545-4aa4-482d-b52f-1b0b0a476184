
<div class="form-item-1 d-flex flex-column">
    <label for="firstName" class="form-label">First Name <span class="text-danger">*</span></label>
    <input type="text" class="form-control @error('firstName') is-invalid @enderror"
           value="{{ old('firstName') }}" id="firstName" name="firstName" autofocus required
           placeholder="First Name" wire:model="firstName">
    @error('firstName')
    <span class="invalid-feedback" role="alert">
                                                            <strong>{{ $message }}</strong>
                                                        </span>
    @enderror
</div>
<div class="form-item-2 d-flex flex-column">
    <label for="lastName" class="form-label">Last Name <span class="text-danger">*</span></label>
    <input type="text" class="form-control @error('lastName') is-invalid @enderror"
           value="{{ old('lastName') }}" id="lastName" name="lastName" autofocus required
           placeholder="Last Name" wire:model="lastName">
    @error('lastName')
    <span class="invalid-feedback" role="alert">
                                                            <strong>{{ $message }}</strong>
                                                        </span>
    @enderror
</div>

<div class="form-item-2 d-flex flex-column">
    <label class="form-label">Shipping from</label>
    <select class="form-control select2">
        @foreach($countries as $country)
            <option value="{{$country->code}}">{{$country->name}}</option>
        @endforeach
    </select>

</div>
<div class="form-item-2 d-flex flex-column">
    <label for="phoneNumber" class="form-label">Mobile Number <span class="text-danger">*</span></label>
    <input type="tel" class="form-control @error('phoneNumber') is-invalid @enderror"
           value="{{ old('phoneNumber') }}" id="phoneNumber" name="phoneNumber" autofocus required
           placeholder="__ ___ ___" wire:model="phoneNumber">
    @error('phoneNumber')
    <span class="invalid-feedback" role="alert"><strong>{{ $message }}</strong></span>
    @enderror
</div>
<div class="login-account-button d-grid col-12">
    <button
        type="button"
        class="btn btn-primary font fw-medium"
        wire:click="goToThirdStep"
    >
        Create My Free Account
    </button>
</div>
<div class="login-account-button d-grid col-12">
    <button
        type="button"
        class="btn btn-primary font fw-medium"
        wire:click="save"
    >
        Create My Free Account
    </button>
</div>
