<div>
    <x-filament::modal
        id="shopify-channel-modal"
        heading="{{ __('translation.choose_connections_method') }}"
        :closeable="true"
        width="4xl"
        wire:model.defer="showModal"
    >
        <div>
            <!-- Connection Options -->
            <div class="flex flex-col gap-4">
                <!-- API Connection Option -->
                <label
                    for="shopify_connection_type_api"
                    class="flex items-center border p-4 rounded-lg hover:bg-gray-100 transition w-full cursor-pointer justify-between"
                >
                    <div class="mr-4 flex align-items-center">
                        <img
                            src="https://storage.googleapis.com/tryoto-public/sales-channels-logo/shopify.png"
                            class="w-24 h-24 object-scale-down"
                            width="90.64px"
                            height="90.64px"
                            alt="{{ __('translation.shopify_api_app') }}"
                        />
                        <div class="text-right">
                            <p class="text-lg font-semibold text-gray-800">
                                <i class="bx bx-key text-primary mr-2"></i>
                                {{ __('translation.shopify_api_app')}}
                            </p>
                            <p class="text-sm text-gray-600">
                                {{ __('translation.shopify_api_app_connection_description') }}
                            </p>
                        </div>
                    </div>

                    <input type="radio" id="shopify_connection_type_api" name="shopify_connection_type" value="api" checked />
                </label>

                <!-- Webhook Connection Option -->
                <label
                    for="shopify_connection_type_webhook"
                    class="hover:bg-gray-100 flex items-center border p-4 rounded-lg transition w-full cursor-pointer justify-between"
                >
                    <div class="mr-4 flex align-items-center">
                        <img
                            src="https://storage.googleapis.com/tryoto-public/sales-channels-logo/shopify.png"
                            class="w-24 h-24 object-scale-down"
                            width="90.64px"
                            height="90.64px"
                            alt="{{ __('translation.shopify_webhook_app') }}"
                        />
                        <div class="text-right">
                            <p class="text-lg font-semibold text-gray-800">
                                <i class="bx bx-link text-primary mr-2"></i>
                                {{ __('translation.shopify_webhook_app')}}
                            </p>
                            <p class="text-sm text-gray-600">
                                {{ __('translation.shopify_webhook_app_connection_description') }}
                            </p>
                        </div>
                    </div>

                    <input type="radio" id="shopify_connection_type_webhook" name="shopify_connection_type" value="webhook" />
                </label>
            </div>

            <!-- Footer -->
            <div class="text-left mt-6 space-x-2">
                <x-filament::button wire:click="$set('showModal', false)" color="secondary" outlined>
                    {{ __('translation.cancel_btn')}}
                </x-filament::button>
                <x-filament::button id="shopify-next-btn" color="primary">
                    {{ __('translation.next_step_btn')}} <i class="bx bx-send align-middle"></i>
                </x-filament::button>
            </div>
        </div>

        @push('scripts')
            <script>
                document.getElementById('shopify-next-btn').addEventListener('click', function () {
                    const chosenType = document.querySelector('input[name=shopify_connection_type]:checked').value;
                    if (chosenType === 'api') {
                        window.location.href = "/merchant/shopify-merchants/create?type=api";
                    } else if (chosenType === 'webhook') {
                        window.location.href = "/merchant/shopify-merchants/create?type=webhook";
                    }
                });
            </script>
        @endpush
    </x-filament::modal>
</div>
