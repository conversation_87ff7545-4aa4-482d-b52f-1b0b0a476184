<div class="col-xl-3">
    <div class="auth-full-page-content p-md-5 p-4">
        <div class="w-100">

            <div class="d-flex flex-column h-100">
                <div class="mb-4 mb-md-5">
                    <a href="index" class="d-block auth-logo">
                        <img src="{{ URL::asset('build/images/logo-dark.png') }}" alt="" height="18"
                             class="auth-logo-dark">
                        <img src="{{ URL::asset('build/images/logo-light.png') }}" alt="" height="18"
                             class="auth-logo-light">
                    </a>
                </div>
                <div class="my-auto">

                    <div>
                        <h5 class="text-primary">Create your free ILS  account now!</h5>
                    </div>
                    <div class="mt-4">
                        <form method="POST" class="form-horizontal" action="{{ route('register') }}" enctype="multipart/form-data" wire:ignore.self>
                            @csrf
                            @if($currentStep === 1)
                            <div class="mb-3">
                                <label for="useremail" class="form-label">Email Address (Work or Personal) <span class="text-danger">*</span></label>
                                <input type="email" class="form-control @error('email') is-invalid @enderror" id="useremail"
                                        name="email" placeholder="Enter email" autofocus required wire:model="email">
                                @error('email')
                                <span class="invalid-feedback" role="alert">
                                                            <strong>{{ $message }}</strong>
                                                        </span>
                                @enderror
                            </div>



                            <div class="mb-3">
                                <label for="userpassword" class="form-label">Password <span class="text-danger">*</span></label>
                                <input type="password" class="form-control @error('password') is-invalid @enderror" id="userpassword" name="password"
                                       placeholder="Enter password" autofocus required wire:model="password">
                                @error('password')
                                <span class="invalid-feedback" role="alert">
                                                            <strong>{{ $message }}</strong>
                                                        </span>
                                @enderror
                            </div>
                                <div class="mt-4 d-grid">
                                    <button class="btn btn-primary waves-effect waves-light"
                                            type="button" wire:click="goToSecondStep">Create My Free Account</button>
                                </div>
                            @elseif($currentStep === 2)
                            <div class="mb-3" wire:ignore.self>
                                <label for="firstName" class="form-label">First Name <span class="text-danger">*</span></label>
                                <input type="text" class="form-control @error('firstName') is-invalid @enderror"
                                       value="{{ old('firstName') }}" id="firstName" name="firstName" autofocus required
                                       placeholder="First Name" wire:model="firstName">
                                @error('firstName')
                                <span class="invalid-feedback" role="alert">
                                                            <strong>{{ $message }}</strong>
                                                        </span>
                                @enderror
                            </div>
                            <div class="mb-3">
                                <label for="lastName" class="form-label">Last Name <span class="text-danger">*</span></label>
                                <input type="text" class="form-control @error('lastName') is-invalid @enderror"
                                       value="{{ old('lastName') }}" id="lastName" name="lastName" autofocus required
                                       placeholder="Last Name" wire:model="lastName">
                                @error('lastName')
                                <span class="invalid-feedback" role="alert">
                                                            <strong>{{ $message }}</strong>
                                                        </span>
                                @enderror
                            </div>
                                    <div class="mb-3">
                                        <label class="form-label">Shipping from</label>
                                        <select class="form-control select2">
                                            @foreach($countries as $country)
                                                <option value="{{$country->code}}">{{$country->name}}</option>
                                            @endforeach
                                        </select>

                                    </div>
                                <div class="mb-3">
                                    <label for="phoneNumber" class="form-label">Mobile Number <span class="text-danger">*</span></label>
                                    <input type="tel" class="form-control @error('phoneNumber') is-invalid @enderror"
                                           value="{{ old('phoneNumber') }}" id="phoneNumber" name="phoneNumber" autofocus required
                                           placeholder="__ ___ ___" wire:model="phoneNumber">
                                    @error('phoneNumber')
                                    <span class="invalid-feedback" role="alert"><strong>{{ $message }}</strong></span>
                                    @enderror
                                </div>
                                <div class="mt-4 d-grid">
                                    <button class="btn btn-primary waves-effect waves-light"
                                            type="button" wire:click="goToThirdStep">Create My Free Account</button>
                                </div>
                                @else
                                <div class="my-auto">
                                    <div class="text-center">

                                        <div class="avatar-md mx-auto">
                                            <div class="avatar-title rounded-circle bg-light">
                                                <i class="bx bxs-envelope h1 mb-0 text-primary"></i>
                                            </div>
                                        </div>
                                        <div class="p-2 mt-4">

                                            <h4>Verify your phone</h4>
                                            <p class="mb-5">Please enter the 4 digit code sent to <span
                                                    class="fw-semibold">{{$phoneNumber}}</span></p>

                                            <form>
                                                <div class="row">
                                                    <div class="col-3">
                                                        <div class="mb-3">
                                                            <label for="digit1" class="visually-hidden">Digit
                                                                1</label>
                                                            <input type="text"
                                                                   class="form-control form-control-lg text-center two-step"
                                                                   maxLength="1" id="digit1-input">
                                                        </div>
                                                    </div>

                                                    <div class="col-3">
                                                        <div class="mb-3">
                                                            <label for="digit2" class="visually-hidden">Digit
                                                                2</label>
                                                            <input type="text"
                                                                   class="form-control form-control-lg text-center two-step"
                                                                   maxLength="1" id="digit2-input">
                                                        </div>
                                                    </div>

                                                    <div class="col-3">
                                                        <div class="mb-3">
                                                            <label for="digit3" class="visually-hidden">Digit
                                                                3</label>
                                                            <input type="text"
                                                                   class="form-control form-control-lg text-center two-step"
                                                                   maxLength="1" id="digit3-input">
                                                        </div>
                                                    </div>

                                                    <div class="col-3">
                                                        <div class="mb-3">
                                                            <label for="digit4" class="visually-hidden">Digit
                                                                4</label>
                                                            <input type="text"
                                                                   class="form-control form-control-lg text-center two-step"
                                                                   maxLength="1" id="digit4-input">
                                                        </div>
                                                    </div>
                                                </div>
                                            </form>

                                            <div class="mt-4">
                                                <a href="index" class="btn btn-success w-md">Confirm</a>
                                            </div>
                                        </div>

                                    </div>
                                </div>
                            @endif

                            <div class="mt-4 text-center">
                                <p class="mb-0">By registering you agree to the ILS <a href="#"
                                                                                         class="text-primary">Terms & Conditions and Privacy Policy</a></p>
                            </div>
                        </form>

                        <div class="mt-3 text-center">
                            <p>Already have an account ? <a href="{{ url('login') }}"
                                                            class="fw-medium text-primary"> Login</a> </p>
                        </div>

                    </div>
                </div>

            </div>


        </div>
    </div>
</div>
