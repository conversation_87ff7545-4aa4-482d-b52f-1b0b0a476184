<div x-data="{ step: @entangle('step') }" class="space-y-6">

    {{-- Custom Header with <PERSON><PERSON><PERSON> (Step 2 + Has International PDF) --}}
    <div class="flex items-center justify-between">
        <template x-if="step === 2 && '{{ $internationalPdfUrl }}'">
            <x-filament::button
                color="primary"
                size="sm"
                x-on:click="step = 3"
            >
                {{ __('translation.proforma_invoice') }}
            </x-filament::button>
        </template>
    </div>

    <!-- Step 1: Table + Create Button -->
    <div x-show="step === 1">
        {{ $this->table }}
        <div class="flex justify-between">
            <span>الرصيد المتاح</span>
            <span>{{Auth::user()->wallet_balance_formatted}} ر. س.</span>
        </div>
        <div class="flex justify-between">
            <span>إجمالي الرصيد</span>
            <span>{{$ordersCount}} ر. س.</span>
        </div>

        <div class="mt-4 text-right">
            <x-filament::button color="primary" wire:click="createShipments">
                {{ __('translation.create_shipment') }}
            </x-filament::button>
        </div>
    </div>

    <!-- Step 2: Confirmation + PDF Viewer -->
    <div x-show="step === 2">
        @if ($pdfUrl)
            <div class="mt-6">
                @include('components.pdf-viewer', ['url' => $pdfUrl])
            </div>
        @endif
    </div>

    <!-- Step 3: International Proforma Invoice -->
    <div x-show="step === 3">
        @if ($internationalPdfUrl)
            <div class="mt-6">
                @include('components.pdf-viewer', ['url' => $internationalPdfUrl])
            </div>
        @endif
    </div>
</div>
