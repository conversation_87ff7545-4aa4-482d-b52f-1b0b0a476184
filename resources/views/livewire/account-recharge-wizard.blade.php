<div>
    <div class="wizard">

        <!-- Step Indicators -->
        <x-filament::card>
            <div class="steps flex justify-between mb-4">
                <div class="{{ $currentStep === 1 ? 'text-primary-500 font-bold' : 'text-gray-400' }}">
                    1. حدد المبلغ
                </div>
                <div class="{{ $currentStep === 2 ? 'text-primary-500 font-bold' : 'text-gray-400' }}">
                    2. اختيار طريقة الدفع
                </div>
                <div class="{{ $currentStep === 3 ? 'text-primary-500 font-bold' : 'text-gray-400' }}">
                    3. الدفع
                </div>
            </div>
        </x-filament::card>

        <!-- Step 1 -->
        @if ($currentStep === 1)
            <x-filament::card>
                <div class="grid grid-rows-3 grid-flow-col gap-4">
                    <div class="row-span-3">
                        <div class="text-3xl text-center p-6">
                            @if(isset($amount))
                                <p>{{$amount}} ر. س.</p>
                            @else
                                <p>0 ر. س.</p>

                            @endif

                        </div>
                        <div class="mb-4">
                            <x-filament::input
                                type="number"
                                wire:model.live.debounce.500ms="amount"
                                placeholder="أدخل المبلغ"
                                class="w-full"
                            />
                        </div>
                        <div class="flex items-center justify-center w-full max-w-md">
                            <div class="border-t border-gray-300 flex-grow"></div>
                            <span class="mx-3 text-gray-500">Or</span>
                            <div class="border-t border-gray-300 flex-grow"></div>
                        </div>

                        <h2 class="text-lg font-bold mb-4">حدد المبلغ</h2>
                        <div class="flex flex-col gap-y-3">
                            <div class="flex gap-x-3 justify-center">
                                <x-filament::button wire:click="selectAmount(100)" color="primary" class="custom-size">
                                    100
                                </x-filament::button>
                                <x-filament::button wire:click="selectAmount(500)" color="primary" class="custom-size">
                                    500
                                </x-filament::button>
                            </div>
                            <div class="flex gap-x-3 justify-center">
                                <x-filament::button wire:click="selectAmount(1000)" color="primary" class="custom-size">
                                    1000
                                </x-filament::button>
                                <x-filament::button wire:click="selectAmount(2000)" color="primary" class="custom-size">
                                    2000
                                </x-filament::button>
                            </div>
                        </div>
                    </div>
                    <div class="col-span-2 color1 rounded p-3 ">
                        <h2 class="mb-5 font-bold text-black">ملخص الطلب</h2>
                        <div class="flex justify-between mb-1 text-gray-500">
                            <p class="">المجموع الفرعي</p>
                            @if(isset($amount))
                                <p>{{$amount}} ر. س.</p>
                            @else
                                <p>0 ر. س.</p>

                            @endif


                        </div>
                        <div class="flex justify-between mb-2 text-gray-500">
                            <p class="tex">قيمة الضريبة المضافة</p>
                            @if(isset($tva))
                                <p>
                                    {{$tva}} ر. س.
                                </p>
                            @else
                                <p>
                                    0 ر. س.
                                </p>
                            @endif

                        </div>
                        <div class="flex items-center justify-center w-full max-w-md">
                            <div class="border-t border-gray-300 w-full"></div>
                        </div>
                        <div class="flex justify-between mt-2">
                            <p class="text-black font-medium">الإجمالي</p>
                            @if(isset($total))
                                <p>{{$total}} ر. س.</p>
                            @else
                                <p>0 ر. س.</p>
                            @endif

                        </div>
                    </div>
                </div>

                <div class="flex justify-end">
                    @if(isset($amount) && $amount > 0)
                        <x-filament::button wire:click="nextStep" color="success" class="mt-4">
                            التالي
                        </x-filament::button>
                    @endif
                </div>
            </x-filament::card>

        @elseif($currentStep === 2)
            <x-filament::card>
                <h2 class="text-lg font-bold mb-4">@lang('translation.transfer_method')</h2>
                <div class="flex justify-between" style="margin: 0 auto">

                    <div class="transfer-option cursor-pointer" wire:click="selectTransferMethod('bank')">
                        <img src="{{ URL::asset('build/images/bank_transfer.png') }}" alt="Bank Transfer" class="object-cover">
                        <p class="text-center mt-2">@lang('translation.bank_transfer')</p>
                    </div>

                    <div class="transfer-option cursor-pointer" wire:click="selectTransferMethod('direct')">
                        <img src="{{ URL::asset('build/images/direct_transfer.png') }}" alt="Direct Transfer" class="object-cover">
                        <p class="text-center mt-2">@lang('translation.direct_transfer')</p>
                    </div>
                </div>
                <div class="flex justify-between mt-4">
                    <x-filament::button wire:click="previousStep" color="success">
                        السابق
                    </x-filament::button>
                </div>

            </x-filament::card>
        <!-- Step 3 -->
        @elseif($currentStep === 3)
            <x-filament::card>
                @if($selectedTransferMethod === 'bank')
                    <h2 class="text-lg font-bold mb-4">@lang('translation.bank_transfer_message')</h2>
                    <h1 class="text-lg font-bold mb-4">البنك الأهلي</h1>
                    <h2 class="text-lg font-bold mb-4">رقم الحساب</h2>
                    <h2>**************</h2>
                    <h2 class="text-lg font-bold mb-4">الأيبان</h2>
                    <h2>SA36100000**************</h2>
                    <x-filament::input.wrapper>
                        <x-filament::input
                            type="file"
                            wire:model="transferProof"
                        />
                    </x-filament::input.wrapper>
                    @error('transferProof') <p class="text-red-500 text-sm mt-1">{{ $message }}</p> @enderror

                    @if ($transferProof)
                        <div class="mt-4">
                            <p>@lang('translation.preview')</p>
                            <img src="{{ $transferProof->temporaryUrl() }}" alt="Transfer Proof Preview" class="max-w-xs max-h-48">
                        </div>
                    @endif

                    <div class="flex justify-between mt-4">
                        <x-filament::button wire:click="previousStep" color="success">
                            السابق
                        </x-filament::button>
                        <x-filament::button wire:click="submitBankTransfer" color="primary">
                            @lang('translation.submit_transfer')
                        </x-filament::button>
                    </div>

                @elseif($selectedTransferMethod === 'direct')
                    @if ($checkoutId)
                        <div x-data="{ initialized: false }" x-init="
                    if (!initialized) {
                        const container = document.getElementById('payment-widget-container');
                        if (container) {
                            container.innerHTML = '';
                            const form = document.createElement('form');
                            form.className = 'paymentWidgets';
                            form.action = 'https://www.gotreek.com/merchant/payment';  //Your action
                            form.setAttribute('data-brands', 'MADA');
                            container.appendChild(form);

                            const script = document.createElement('script');
                            script.src = '{{$baseUrl}}/v1/paymentWidgets.js?checkoutId={{ $checkoutId }}';
                            script.async = true;
                            script.onload = () => {
                                initialized = true;
                                console.log('Payment widget initialized');
                                const wpwlScript = document.createElement('script');
                                wpwlScript.type = 'text/javascript';
                                wpwlScript.textContent = `var wpwlOptions = { paymentTarget: '_top' }`;
                                document.body.appendChild(wpwlScript);
                            };
                            document.body.appendChild(script);
                        }
                    }
                ">
                            <div id="payment-widget-container"></div>
                        </div>
                    @else
                        <p>Loading payment widget...</p>
                    @endif

                    <div class="flex justify-between mt-4">
                        <x-filament::button wire:click="previousStep" color="success">
                            السابق
                        </x-filament::button>
                    </div>
                @else
                    <p>Please select a transfer method in the previous step.</p>
                    <div class="flex justify-between mt-4">
                        <x-filament::button wire:click="previousStep" color="secondary">
                            السابق
                        </x-filament::button>
                    </div>
                @endif
            </x-filament::card>

        @endif

        <!-- Step 3 -->
        @if ($currentStep === 4)
            <x-filament::card>
                <h2 class="text-lg font-bold mb-4">ملخص الطلب</h2>
                <p class="mb-2">المبلغ: {{ $amount }} ر.س</p>
                <div class="flex justify-between mt-4">
                    <x-filament::button wire:click="previousStep" color="success">
                        السابق
                    </x-filament::button>
                    <x-filament::button wire:click="submit" color="primary">
                        إرسال
                    </x-filament::button>
                </div>
            </x-filament::card>
        @endif
    </div>
</div>
