<div class="space-y-6">

    <div class="space-y-4">
        @forelse ($shipments as $shipment)
            <div class="flex items-center justify-between bg-white shadow-sm rounded-lg p-4">
                <!-- Shipment Icon -->
                <div class="flex flex-row ">
                    <div>
                        <img src="{{ asset('build/images/'. $shipment->service->logo_big) }}"
                             alt="{{ $shipment->tracking_number }}"
                             class="w-20 h-15 rounded-md bg-gray-100">
                    </div>

                    <!-- Shipment Details -->
                    <div class="ml-4">
                        <h3 class="text-sm font-medium text-gray-800">
                            {{ $shipment->tracking_number }}
                        </h3>
{{--                        <p class="text-sm text-gray-500">--}}
                        {{--                            {{ $shipment->created_at->format('M d, Y') }}--}}
                        {{--                        </p>--}}
                    </div>
                </div>

                <!-- Shipment Status -->
{{--                <div>--}}
{{--                    <span--}}
{{--                        class="px-2 py-1 text-xs font-semibold rounded-full {{ $shipment->status === 'Delivered' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800' }}">--}}
{{--                        {{ ucfirst($shipment->status) }}--}}
{{--                    </span>--}}
{{--                </div>--}}

                <!-- Actions -->
                <div>
                    <a id="link11" class="block text-blue-600 text-sm font-medium rounded-lg border px-3 py-1 mr-3 hover:bg-gray-100"
                       href={{ $shipment->service->getEditUrl() }}>
                        <i class="fa-solid fa-ellipsis"></i>
                        {{__('translation.edit')}}
                    </a>
                </div>
            </div>
        @empty
            <div class="text-center text-gray-500">
                <p>{{ __('translation.no_shipments_found') }}</p>

            </div>
        @endforelse
    </div>
</div>
