<div>
    <form >
        <div class="row justify-content-center">

            <div
                class="col-md-4 mb-3 z-9999"
            >
                <label
                    for="departure"
                    class="form-label font-noto"
                    style="font-size: 1.25rem"
                >
                    {{ __('translation.departure_city_label') }} <span class="text-danger">*</span>
                    <i
                        class="fas fa-info-circle"
                        style="font-size: 1rem; color: gray"
                    ></i>
                </label>
                <input
                    wire:model="shipper"
                    type="text"
                    class="form-control"
                    id="departure"
                    placeholder="{{ __('translation.departure_city_placeholder') }}"
                    required
                />
                <div id="departureList" class="dropdown-menu show z-9999" style="display: none;"></div>

            </div>
            <div
                class="col-md-4 mb-3 z-9999">
                <label
                    for="destination"
                    class="form-label font-noto"
                    style="font-size: 1.25rem"
                >
                    {{ __('translation.destination_city_label') }} <span class="text-danger">*</span>
                    <i
                        class="fas fa-info-circle"
                        style="font-size: 1rem; color: gray"
                    ></i>
                </label>
                <input
                    wire:model="receiver"
                    type="text"
                    class="form-control"
                    id="destination"
                    placeholder="{{ __('translation.destination_city_placeholder') }}"
                    required=""
                />
                <div id="destinationList" class="dropdown-menu show" style="display: none;"></div>



            </div>
            <div>
                <div class="container mt-4">
                    <div class="row g-3">
                        <!-- Total Weight -->
                        <div class="col-md-3 ">
                            <label class="form-label">{{__('translation.total_weight')}} ({{__('translation.kg')}})<span class="text-danger">*</span>
                                <i class="bi bi-info-circle" data-bs-toggle="tooltip"
                                   title="Enter the weight in kilograms"></i>
                            </label>
                            <div class="input-group">
                                <button
                                    wire:click="decrementWeight"
                                    class="custom-btn-calc rounded-0 btn btn btn-outline-secondary rounded-0"
                                    type="button">-
                                </button>
                                <input type="text" class="form-control text-center border-right-0" wire:model="weight" id="weightInput">
                                <button
                                    wire:click="incrementWeight"
                                    class="custom-btn-calc rounded-0 btn btn btn-outline-secondary rounded-0"
                                    type="button">+
                                </button>
                            </div>
                        </div>
                        <div class="col-md-3 ">
                            <label class="form-label">{{__('translation.length')}} <span class="text-danger">*</span>
                                <i class="bi bi-info-circle" data-bs-toggle="tooltip"
                                   title="Enter the length in cm"></i>
                            </label>
                            <div class="input-group">
                                <button
                                    wire:click="decrementLength"
                                    class="custom-btn-calc rounded-0 btn btn btn-outline-secondary"
                                    type="button">-
                                </button>
                                <input type="text" class="form-control text-center"
                                       wire:model="length"    id="lengthInput">

                                <button
                                    wire:click="incrementLength"
                                    class="custom-btn-calc rounded-0 btn btn btn-outline-secondary"
                                    type="button">+
                                </button>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">{{__('translation.width')}} <span
                                    class="text-danger">*</span>
                                <i class="bi bi-info-circle" data-bs-toggle="tooltip"
                                   title="Enter the width in cm"></i>
                            </label>
                            <div class="input-group">
                                <button wire:click="decrementWidth" class="custom-btn-calc rounded-0 btn btn btn-outline-secondary"
                                        type="button">-
                                </button>
                                <input type="text" class="form-control text-center" wire:model="width"
                                       id="widthInput">
                                <button wire:click="incrementWidth" class="custom-btn-calc rounded-0 btn btn btn-outline-secondary"
                                        type="button">+
                                </button>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">{{__('translation.height')}} <span
                                    class="text-danger">*</span>
                                <i class="bi bi-info-circle" data-bs-toggle="tooltip"
                                   title="Enter the height in cm"></i>
                            </label>
                            <div class="input-group">
                                <button wire:click="decrementHeight" class="custom-btn-calc rounded-0 btn btn btn-outline-secondary"
                                        type="button">-
                                </button>
                                <input type="text" class="form-control text-center" wire:model="height"
                                       id="heightInput">
                                <button wire:click="incrementHeight" class="custom-btn-calc rounded-0 btn btn-outline-secondary"
                                        type="button">+
                                </button>
                            </div>
                        </div>

                    </div>
                </div>
            </div>
            <div >
                <br>
                <button wire:click="calculate" type="button" class="custom-button m-2 font-noto">{{__('translation.calculate_shipping_rates')}}
                </button>

                <div class="d-flex flex-column w-50 " style="margin: 0 auto">
                    @foreach($prices as $shipmentCompany => $price)
                        <div class="price-item">
                           {{$price}} {{__('translation.sar')}}
                            <img src="{{asset("build/images/$shipmentCompany-big.png")}}" alt="" style="height: 50px;width: auto">
                        </div>
                    @endforeach
                </div>
            </div>
        </div>
    </form>
</div>
