@props([
    'availableHeight' => null,
    'availableWidth' => null,
    'flip' => true,
    'maxHeight' => null,
    'offset' => 8,
    'placement' => null,
    'shift' => false,
    'size' => false,
    'sizePadding' => 16,
    'teleport' => false,
    'trigger' => null,
    'width' => null,
])

@php
    use Filament\Support\Enums\MaxWidth;

    $sizeConfig = collect([
        'availableHeight' => $availableHeight,
        'availableWidth' => $availableWidth,
        'padding' => $sizePadding,
    ])->filter()->toJson();
@endphp

<div
    x-data="{
        allDisabled: true,
        toggle: function (event) {
            if(this.allDisabled) return;
            $refs.panel.toggle(event)
        },

        open: function (event) {
            if(this.allDisabled) return;
            $refs.panel.open(event)
        },

        close: function (event) {
            $refs.panel.close(event)
        },

        checkDisabled() {
            const buttons = $refs.panel.querySelectorAll('button');
            const disabledButtons = $refs.panel.querySelectorAll('button[disabled]');
            this.allDisabled = buttons.length > 0 && buttons.length === disabledButtons.length;
        }
    }"
    x-init="
        checkDisabled();
        setInterval(() => checkDisabled(), 500);
    "
    x-on:livewire:load.window="checkDisabled()"
    x-on:livewire:update.window="checkDisabled()"
    {{ $attributes->class(['fi-dropdown']) }}
>
    <div
        x-on:click="toggle"
        x-bind:class="{ 'opacity-50 cursor-not-allowed': allDisabled }"
        {{ $trigger->attributes->class(['fi-dropdown-trigger flex cursor-pointer']) }}
    >
        {{ $trigger }}
    </div>

    @if (! \Filament\Support\is_slot_empty($slot))
        <div
            x-cloak
            x-float{{ $placement ? ".placement.{$placement}" : '' }}{{ $size ? '.size' : '' }}{{ $flip ? '.flip' : '' }}{{ $shift ? '.shift' : '' }}{{ $teleport ? '.teleport' : '' }}{{ $offset ? '.offset' : '' }}="{ offset: {{ $offset }}, {{ $size ? ('size: ' . $sizeConfig) : '' }} }"
            x-ref="panel"
            x-transition:enter-start="opacity-0"
            x-transition:leave-end="opacity-0"
            @if ($attributes->has('wire:key'))
                wire:ignore.self
                wire:key="{{ $attributes->get('wire:key') }}.panel"
            @endif
            @class([
                'fi-dropdown-panel absolute z-10 w-screen divide-y divide-gray-100 rounded-lg bg-white shadow-lg ring-1 ring-gray-950/5 transition dark:divide-white/5 dark:bg-gray-900 dark:ring-white/10',
                match ($width) {
                    // These max width classes need to be `!important` otherwise they will be usurped by the Floating UI "size" middleware.
                    MaxWidth::ExtraSmall, 'xs' => '!max-w-xs',
                    MaxWidth::Small, 'sm' => '!max-w-sm',
                    MaxWidth::Medium, 'md' => '!max-w-md',
                    MaxWidth::Large, 'lg' => '!max-w-lg',
                    MaxWidth::ExtraLarge, 'xl' => '!max-w-xl',
                    MaxWidth::TwoExtraLarge, '2xl' => '!max-w-2xl',
                    MaxWidth::ThreeExtraLarge, '3xl' => '!max-w-3xl',
                    MaxWidth::FourExtraLarge, '4xl' => '!max-w-4xl',
                    MaxWidth::FiveExtraLarge, '5xl' => '!max-w-5xl',
                    MaxWidth::SixExtraLarge, '6xl' => '!max-w-6xl',
                    MaxWidth::SevenExtraLarge, '7xl' => '!max-w-7xl',
                    null => '!max-w-[14rem]',
                    default => $width,
                },
                'overflow-y-auto' => $maxHeight || $size,
            ])
            @style([
                "max-height: {$maxHeight}" => $maxHeight,
            ])
        >
            {{ $slot }}
        </div>
    @endif
</div>
