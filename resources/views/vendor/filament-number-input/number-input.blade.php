<x-dynamic-component
    :component="$getFieldWrapperView()"
    :field="$field"
    :id="$getId()"
    :label="$getLabel()"
    :label-sr-only="$isLabelHidden()"
    :helper-text="$getHelperText()"
    :hint-icon="$getHintIcon()"
    :required="$isRequired()"
    :state-path="$getStatePath()"
    :plusIcon="$getPlusIcon()"
>
<div 
    x-data="{
        state: $wire.entangle('{{ $getStatePath() }}'),
        disabled: {{ $isDisabled() ? 'true' : 'false' }},
        incrementDisabled: false,
        decrementDisabled: false,
        inputError: null,

        init() {
            this.$nextTick(() => {
                this.syncInput()
                this.checkDisabled()
            })
        },

        toEnglishDigits(str) {
            const arabic = ['٠','١','٢','٣','٤','٥','٦','٧','٨','٩']
            const english = ['0','1','2','3','4','5','6','7','8','9']
            return str?.replace(/[٠-٩]/g, d => english[arabic.indexOf(d)]) ?? ''
        },

        syncInput() {
            let val = this.toEnglishDigits(this.$refs.numberInput.value)
            if (!val || isNaN(val)) {
                val = this.state !== null ? this.state : ''
            }

            this.$refs.numberInput.value = val
            this.state = val ? Number(val) : null
        },

        increase() {
            this.syncInput()
            let step = Number(this.$refs.numberInput.getAttribute('step') ?? 1)
            let current = Number(this.state ?? 0)
            let newValue = current + step
            this.state = newValue
            this.$refs.numberInput.value = newValue
            $wire.set('{{ $getStatePath() }}', newValue)
            this.validateData()
            this.checkDisabled()
        },

        decrease() {
            this.syncInput()
            let step = Number(this.$refs.numberInput.getAttribute('step') ?? 1)
            let current = Number(this.state ?? 0)
            let newValue = current - step
            this.state = newValue
            this.$refs.numberInput.value = newValue
            $wire.set('{{ $getStatePath() }}', newValue)
            this.validateData()
            this.checkDisabled()
        },

        onInput() {
            this.syncInput()
            $wire.set('{{ $getStatePath() }}', this.state)
            this.validateData()
            this.checkDisabled()
        },

        validateData() {
            this.inputError = null
            const value = this.state
            const min = Number(this.$refs.numberInput.min ?? null)
            const max = Number(this.$refs.numberInput.max ?? null)

            if (min && value < min) {
                this.inputError = `The input value must be ≥ <span class='font-bold'>${min}</span>.`
                return
            }

            if (max && value > max) {
                this.inputError = `The input value must be ≤ <span class='font-bold'>${max}</span>.`
                return
            }

            return true
        },

        checkDisabled() {
            const value = Number(this.state ?? NaN)
            const maxAttr = this.$refs.numberInput.getAttribute('max')
            const minAttr = this.$refs.numberInput.getAttribute('min')

            const max = maxAttr !== null ? Number(maxAttr) : null
            const min = minAttr !== null ? Number(minAttr) : null

            this.incrementDisabled = (max !== null && !isNaN(value)) ? value >= max : false
            this.decrementDisabled = (min !== null && !isNaN(value)) ? value <= min : false
        }
    }"
        >
        <div {{ $attributes->class(["relative flex items-center"])->merge($getExtraInputAttributes())}}>
            <button x-on:click="decrease()" type="button" id="decrement-button" data-input-counter-decrement="quantity-input"
                :disabled="decrementDisabled || disabled"
                class="disabled:bg-gray-400 disabled:hover:bg-gray-400 dark:bg-gray-700 dark:hover:bg-gray-600 dark:border-gray-600 hover:bg-gray-200 border border-gray-300 rounded-s-lg py-2.5 px-3 focus:ring-gray-100 dark:focus:ring-gray-700 focus:ring-2 focus:outline-none">
                <x-filament::icon
                    class="w-4 h-4 text-gray-900 dark:text-white"
                    stroke-width="2"
                    :icon="$getMinusIcon()"
                >
                    <svg class="w-4 h-4 text-gray-900 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 18 2">
                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M1 1h16"/>
                    </svg>
                </x-filament::icon>
            </button>
            <input
                x-ref="numberInput"
                 type="text"
                inputmode="numeric"
                pattern="[0-9]*"
                {{ $applyStateBindingModifiers('wire:model') }}="{{ $getStatePath() }}"
            
                id="{{ $getId() }}"
                x-model = "state"
                aria-describedby="helper-text-explanation"
                placeholder="0"
                {!! ($placeholder = $getPlaceholder()) ? "placeholder=\"{$placeholder}\"" : null !!}
                {!! ($interval = $getStep()) ? "step=\"{$interval}\"" : null !!}
                {!! $isDisabled()||$isManualInputDisabled() ? 'disabled' : null !!}
                @if (! $isConcealed())
                    {!! filled($value = $getMaxValue()) ? "max=\"{$value}\"" : null !!}
                    {!! filled($value = $getMinValue()) ? "min=\"{$value}\"" : null !!}
                    {!! $isRequired() ? 'required' : null !!}
                @endif
                x-on:blur="onInput()"
                x-on:change="onInput()"
                class="bg-gray-50 border-x-0 border-y border-gray-300 outline-none text-center text-gray-900 text-base sm:text-sm sm:leading-6 focus:ring-primary-500 focus:border-primary-500 block w-full py-1.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-primary-500 dark:focus:border-primary-500 filament-number-input"
            >
            <button x-on:click="increase()" type="button" id="increment-button" data-input-counter-increment="quantity-input"
                :disabled="incrementDisabled || disabled"
                class="stroke-2 disabled:bg-gray-400 disabled:hover:bg-gray-400 dark:bg-gray-700 dark:hover:bg-gray-600 dark:border-gray-600 hover:bg-gray-200 border border-gray-300 rounded-e-lg px-3 py-2.5 focus:ring-gray-100 dark:focus:ring-gray-700 focus:ring-2 focus:outline-none">
                <x-filament::icon
                    class="w-4 h-4 text-gray-900 dark:text-white"
                    stroke-width="2"
                    :icon="$getPlusIcon()"
                >
                    <svg class="w-4 h-4 text-gray-900 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 18 18">
                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 1v16M1 9h16"/>
                    </svg>
                </x-filament::icon>
            </button>
        </div>
        <template x-if="inputError">
            <div class="text-danger-400" x-html="inputError"></div>
        </template>
    </div>
    <style>
        .filament-number-input::-webkit-outer-spin-button,
        .filament-number-input::-webkit-inner-spin-button {
            -webkit-appearance: none;
        }
        .filament-number-input {
            -moz-appearance: textfield;
        }
    </style>
</x-dynamic-component>
