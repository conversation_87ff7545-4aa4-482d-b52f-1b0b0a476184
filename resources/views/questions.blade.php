<!DOCTYPE html>
<html lang="{{ app()->getLocale() }}" dir="{{ app()->getLocale() === 'ar' ? 'rtl' : 'ltr' }}">
@include('layouts.head')
<body>
<div class="nav-bg"></div>
<div class="p-0 container-fluid container-lg">
    @include('navbar')

    <!-- FAQ Section -->
    <section
        class="faq-section py-5"
        data-aos="fade-up"
        data-aos-duration="800"
        data-aos-once="false"
    >
        <div class="container">
            <h1
                class="text-center mb-5 font-kufi fs-1 fw-bold"
                data-aos="fade-down"
                data-aos-duration="800"
                data-aos-once="false"
            >
                {!! __('translation.faq_title') !!}
            </h1>
            <h4
                class="text-center mb-5 font-kufi lead-color fs-4 fw-medium"
                data-aos="fade-up"
                data-aos-duration="800"
                data-aos-once="false"
            >
                {{ __('translation.faq_subtitle') }}
            </h4>
            <div class="accordion" id="faqAccordion">
                <h4
                    class="font-kufi fw-bold"
                    data-aos="fade-right"
                    data-aos-duration="800"
                    data-aos-once="false"
                >
                    {{ __('translation.faq_services') }}
                </h4>
                @foreach($servicesFaq as $faq)
                    @include('components.faqItem', ['faq' => $faq])
                @endforeach

                <div class="accordion mt-4" id="faqAccordion2">
                    <h4
                        class="font-kufi fw-bold"
                        data-aos="fade-right"
                        data-aos-duration="800"
                        data-aos-once="false"
                    >
                        {{ __('translation.faq_shipment') }}
                    </h4>
                    @foreach($shipmentFaq as $faq)
                        @include('components.faqItem', ['faq' => $faq])
                    @endforeach
                </div>
                <div class="accordion mt-4" id="faqAccordion2">
                    <h4
                        data-aos="fade-right"
                        data-aos-duration="800"
                        data-aos-once="false"
                    >
                        {{ __('translation.faq_link') }}
                    </h4>
                    @foreach($linkFaq as $faq)
                        @include('components.faqItem', ['faq' => $faq])
                    @endforeach
                </div>
            </div>
        </div>
    </section>
</div>
@include('components.footer')

<script>
    AOS.init({
        duration: 800, // Default animation duration
        once: false, // Ensures animations repeat
        easing: "ease-out", // Smooth easing for animations
    });

    // Add scrolled class to navbar and nav-bg on scroll
    window.addEventListener("scroll", function () {
        const navbar = document.querySelector(".navbar");
        const navBg = document.querySelector(".nav-bg");

        if (window.scrollY > 50) {
            if (navbar) navbar.classList.add("scrolled");
            if (navBg) navBg.classList.add("scrolled");
        } else {
            if (navbar) navbar.classList.remove("scrolled");
            if (navBg) navBg.classList.remove("scrolled");
        }
    });
</script>
</body>
</html>
