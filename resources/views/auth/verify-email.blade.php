<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>{{ __('auth.verify_email.title') }} - {{ config('app.name') }}</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <!-- Arabic Font -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', 'Poppins', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
            direction: rtl;
        }

        .verification-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            max-width: 500px;
            width: 100%;
            animation: slideUp 0.8s ease-out;
        }

        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .verification-header {
            background: linear-gradient(135deg, #667eea, #764ba2);
            padding: 2rem;
            text-align: center;
            color: white;
        }

        .verification-icon {
            width: 80px;
            height: 80px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1rem;
            backdrop-filter: blur(10px);
        }

        .verification-icon i {
            font-size: 2rem;
            color: white;
        }

        .verification-header h1 {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
        }

        .verification-header p {
            opacity: 0.9;
            font-size: 0.9rem;
        }

        .verification-body {
            padding: 2.5rem;
            text-align: center;
        }

                 .status-message {
             background: #f8f9fa;
             border-right: 4px solid #28a745;
             padding: 1rem;
             margin-bottom: 2rem;
             border-radius: 8px;
             text-align: right;
         }

         .status-message.success {
             border-right-color: #28a745;
             background: #d4edda;
             color: #155724;
         }

         .status-message.info {
             border-right-color: #17a2b8;
             background: #d1ecf1;
             color: #0c5460;
         }

        .main-message {
            color: #6c757d;
            font-size: 1rem;
            line-height: 1.6;
            margin-bottom: 2rem;
        }

        .action-buttons {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        .btn-primary-custom {
            background: linear-gradient(135deg, #667eea, #764ba2);
            border: none;
            padding: 12px 30px;
            border-radius: 50px;
            color: white;
            font-weight: 600;
            font-size: 1rem;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
        }

        .btn-primary-custom:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
            color: white;
        }

        .btn-secondary-custom {
            background: transparent;
            border: 2px solid #e9ecef;
            padding: 12px 30px;
            border-radius: 50px;
            color: #6c757d;
            font-weight: 500;
            font-size: 0.9rem;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
        }

        .btn-secondary-custom:hover {
            border-color: #667eea;
            color: #667eea;
            background: rgba(102, 126, 234, 0.05);
        }

        .loading-spinner {
            display: none;
            width: 20px;
            height: 20px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top-color: white;
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        .footer-text {
            margin-top: 2rem;
            padding-top: 2rem;
            border-top: 1px solid #e9ecef;
            color: #adb5bd;
            font-size: 0.85rem;
        }

        .footer-text a {
            color: #667eea;
            text-decoration: none;
        }

        .footer-text a:hover {
            text-decoration: underline;
        }

        @media (max-width: 576px) {
            .verification-container {
                margin: 10px;
                border-radius: 15px;
            }

            .verification-header {
                padding: 1.5rem;
            }

            .verification-body {
                padding: 2rem 1.5rem;
            }

            .verification-header h1 {
                font-size: 1.3rem;
            }

            .action-buttons {
                gap: 0.8rem;
            }
        }
    </style>
</head>
<body>
    <div class="verification-container">
        <div class="verification-header">
            <div class="verification-icon">
                <i class="fas fa-envelope-open"></i>
            </div>
            <h1>{{ __('auth.verify_email.heading') }}</h1>
            <p>{{ __('auth.verify_email.subheading') }}</p>
        </div>

        <div class="verification-body">
                        @if (session('resent'))
                <div class="status-message success">
                    <i class="fas fa-check-circle ms-2"></i>
                    {{ __('auth.verify_email.resent_message') }}
                </div>
            @endif

            @if (session('message'))
                <div class="status-message success">
                    <i class="fas fa-check-circle ms-2"></i>
                    {{ session('message') }}
                </div>
            @endif

            @if (session('verified'))
                <div class="status-message success">
                    <i class="fas fa-check-circle ms-2"></i>
                    {{ __('auth.verify_email.verified_message') }}
                </div>
            @endif

            <div class="main-message">
                {{ __('auth.verify_email.main_message') }}
                <br><br>
                {{ __('auth.verify_email.spam_message') }}
            </div>

            <div class="action-buttons">
                <form method="POST" action="{{ route('verification.send') }}" id="resendForm">
                    @csrf
                    <button type="submit" class="btn-primary-custom" id="resendBtn">
                        <span class="btn-text">
                            <i class="fas fa-paper-plane"></i>
                            {{ __('auth.verify_email.resend_button') }}
                        </span>
                        <div class="loading-spinner"></div>
                    </button>
                </form>


            </div>

                        <div class="footer-text">
                {{ __('auth.verify_email.need_help') }}
                <a href="tel:966 53 311 2592">{{ __('auth.verify_email.contact_support') }}</a>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // Handle resend button loading state
        document.getElementById('resendForm').addEventListener('submit', function() {
            const btn = document.getElementById('resendBtn');
            const btnText = btn.querySelector('.btn-text');
            const spinner = btn.querySelector('.loading-spinner');

            btn.disabled = true;
            btnText.style.display = 'none';
            spinner.style.display = 'block';

            // Re-enable after 3 seconds in case of errors
            setTimeout(function() {
                btn.disabled = false;
                btnText.style.display = 'flex';
                spinner.style.display = 'none';
            }, 3000);
        });

        // Auto-hide success messages after 5 seconds
        setTimeout(function() {
            const successMessages = document.querySelectorAll('.status-message.success');
            successMessages.forEach(function(message) {
                message.style.transition = 'opacity 0.5s ease';
                message.style.opacity = '0';
                setTimeout(function() {
                    message.style.display = 'none';
                }, 500);
            });
        }, 5000);
    </script>
</body>
</html>
