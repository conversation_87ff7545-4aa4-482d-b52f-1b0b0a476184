<html lang="{{ app()->getLocale() }}" dir="{{ app()->getLocale() === 'ar' ? 'rtl' : 'ltr' }}">
@include('layouts.head')
<body>
<!-- Navbar Section -->
<div class="nav-bg"></div>
<div class="p-0 container-fluid container-lg">
    @include('navbar')

    <!-- Hero and Image Section -->
    <section
        class="hero-section py-5"
        data-aos="fade-up"
        data-aos-once="false"
    >
        <div class="container">
            <div class="row align-items-center justify-content-center">
                <!-- Text Section -->
                <div
                    class="col-md-6 order-md-1 text-section text-center"
                    data-aos="fade-right"
                    data-aos-once="false"
                >
                    <h1 class="text-center mb-5 font-kufi fw-bolder">
                        {!! __('translation.hero_title') !!}
                    </h1>
                    <h4 class="lead mb-4 font-kufi fw-light">
                        {!! __('translation.hero_subtitle') !!}
                    </h4>
                    <a
                        href="/merchant/login"
                        class="custom-button m-2 font-noto"
                        data-aos="zoom-in"
                        data-aos-once="false"
                    >{{ __('translation.start_free_button') }}</a>
                    <a
                        href="https://calendly.com/gotreek-sa/30min"
                        class="custom2-button m-2 font-noto"
                        data-aos="zoom-in"
                        data-aos-once="false"
                    >{{ __('translation.contact_sales_button') }}</a>
                </div>

                <!-- Image Section -->
                <div
                    class="col-md-6 order-md-2 image-section text-center"
                    data-aos="fade-left"
                    data-aos-once="false"
                >
                    <img
                        src="{{ URL::asset('build/images/image_avion.svg') }}"
                        alt="Shipping Graphic"
                        class="mx-auto d-block lazy-placeholder"
                        loading="lazy"
                    />
                </div>
            </div>
        </div>
    </section>

    <!-- Services Section -->
    <section
        class="services-section"
        data-aos="fade-up"
        data-aos-once="false"
    >
        <div class="container text-center">
            <div class="row">
                <div class="col-md-12">
                    <!-- Button Group -->
                    <div
                        class="btn-group flex-wrap d-flex justify-content-center"
                        role="group"
                        aria-label="Service buttons"
                    >
                        <button
                            type="button"
                            class="custom4-button font-noto fw-medium"
                            id="btn-oms"
                            onclick="showImage('oms',this)"
                            data-aos="fade-up"
                            data-aos-once="false"
                        >
                            {{ __('translation.ship') }}
                        </button>
                        <button
                            type="button"
                            class="custom4-button font-noto fw-medium"
                            id="btn-rms"
                            onclick="showImage('rms',this)"
                            data-aos="fade-up"
                            data-aos-once="false"
                        >
                            {{ __('translation.manage') }}
                        </button>
                        <button
                            type="button"
                            class="custom4-button font-noto fw-medium"
                            id="btn-tracking"
                            onclick="showImage('tracking',this)"
                            data-aos="fade-up"
                            data-aos-once="false"
                        >
                            {{ __('translation.track') }}
                        </button>
                        <button
                            type="button"
                            class="custom4-button font-noto fw-medium"
                            id="btn-lms"
                            onclick="showImage('lms',this)"
                            data-aos="fade-up"
                            data-aos-once="false"
                        >
                            {{ __('translation.analyze') }}
                        </button>
                    </div>
                    
                    <!-- Service Images Container -->
                    <div class="service-images-container mt-4">
                        <img
                            id="service-image"
                            src="{{ URL::asset('build/images/pages_photos/oms.png') }}"
                            alt="Service Image"
                            class="img-fluid mx-auto d-block"
                            loading="lazy"
                        />
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Partner Logos Section -->
    <section
        class="partners-section py-4"
        data-aos="fade-up"
        data-aos-once="false"
    >
        <div dir="ltr" class="container text-center">
            <h2 class="custom-h2 font-kufi fw-bolder">
                {{ __('translation.partners_trust_us') }}
            </h2>
            @include('components.carousel')
        </div>
    </section>

    <!-- Integrated Shipping Portal Section -->
    <section
        class="shipping-portal-section py-5"
        data-aos="fade-up"
        data-aos-once="false"
    >
        <div class="container text-center">
            <h2 class="custom-h2 font-kufi fw-bolder mb-4 lh-lg">
                {{ __('translation.shipping_portal_title') }}
            </h2>
            <div class="row">
                <!-- LMS -->
                <div class="col-md-4" data-aos="flip-left" data-aos-once="false">
                    <img
                        src="{{ URL::asset('build/images/icon-lms.svg') }}"
                        alt="LMS Icon"
                        class="img-fluid mb-3 w-25"
                        loading="lazy"
                    />
                    <h5 class="font-kufi">{{ __('translation.lms') }}</h5>
                </div>
                <!-- RMS -->
                <div class="col-md-4" data-aos="flip-left" data-aos-once="false">
                    <img
                        src="{{ URL::asset('build/images/icon-rms.svg') }}"
                        alt="RMS Icon"
                        class="img-fluid mb-3 w-25"
                        loading="lazy"
                    />
                    <h5 class="font-kufi">{{ __('translation.rms') }}</h5>
                </div>
                <!-- OMS -->
                <div class="col-md-4" data-aos="flip-left" data-aos-once="false">
                    <img
                        src="{{ URL::asset('build/images/icon-oms.svg') }}"
                        alt="OMS Icon"
                        class="img-fluid mb-3 w-25"
                    />
                    <h5 class="font-kufi">{{ __('translation.oms') }}</h5>
                </div>
            </div>
            <div class="row mt-4">
                <!-- Integration with Sales Platforms -->
                <div class="col-md-4" data-aos="flip-right" data-aos-once="false">
                    <img
                        src="{{ URL::asset('build/images/icon-platform-link.svg') }}"
                        alt="Platform Link Icon"
                        class="img-fluid mb-3 w-25"
                    />
                    <h5 class="font-kufi">{{ __('translation.platform_integration') }}</h5>
                </div>
                <!-- Analytics and Reporting -->
                <div class="col-md-4" data-aos="flip-right" data-aos-once="false">
                    <img
                        src="{{ URL::asset('build/images/icon-analytics.svg') }}"
                        alt="Analytics Icon"
                        class="img-fluid mb-3 w-25"
                    />
                    <h5 class="font-kufi">{{ __('translation.analytics_reporting') }}</h5>
                </div>
                <!-- Operation Automation -->
                <div class="col-md-4" data-aos="flip-right" data-aos-once="false">
                    <img
                        src="{{ URL::asset('build/images/icon-automation.svg') }}"
                        alt="Automation Icon"
                        class="img-fluid mb-3 w-25"
                    />
                    <h5 class="font-kufi">{{ __('translation.operation_automation') }}</h5>
                </div>
            </div>
        </div>
    </section>

    <!-- Needs Section -->
    <section
        class="needs-section mb-0 py-4 aos-init aos-animate"
        data-aos="fade-up"
        data-aos-once="false"
    >
        <div class="container">
            <div dir="ltr" class="container text-center">
                <h2 class="custom-h2 font-kufi fw-bolder">
                    {{ __('translation.accreditations') }}
                </h2>
                @include('components.carousel1')
            </div>
        </div>
    </section>
</div>


@include('components.footer')
<script>
    // Simple image display function
    function showImage(service, button) {
        // Update image source based on service name
        const imageElement = document.getElementById('service-image');
        if (imageElement) {
            imageElement.src = '{{ URL::asset("build/images/pages_photos") }}/' + service + '.png';
        }

        // Update button states
        const serviceButtons = document.querySelectorAll(".custom4-button");
        serviceButtons.forEach((btn) => btn.classList.remove("selected"));
        button.classList.add("selected");
    }

    // Use optimized ready function
    window.whenReady(function () {
        // Use optimized AOS
        window.initOptimizedAOS();

        // Initialize first button with requestAnimationFrame for smooth rendering
        window.scheduleWork(function() {
            const firstButton = document.getElementById("btn-oms");
            if (firstButton) {
                showImage("oms", firstButton);
            }
        });
    });
</script>
</body>
</html>
