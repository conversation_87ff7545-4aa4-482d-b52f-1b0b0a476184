<!DOCTYPE html>
<html lang="{{ app()->getLocale() }}" dir="{{ app()->getLocale() === 'ar' ? 'rtl' : 'ltr' }}">
@include('layouts.head')

<body class="overflow-x-hidden">
<div class="container">
    <div class="row">
        <div class="nav-bg"></div>
        @include('navbar')
    </div>

    <div class="row h-50 mt-5">
        <div class="col-12 col-xl-8 my-5">
            <div class="row">
                <h1 class="text-start font-kufi fw-bold fs-1">
                    {{ __('translation.main_title') }}
                </h1>
            </div>

            <!-- Tracking Form -->
            <form class="row flex-column flex-lg-row my-4 row-gap-3">
                <div class="col">
                    <label for="trackingNumber" class="form-label font-noto fs-6 fw-medium">
                        {{ __('translation.tracking_number_label') }} <span class="text-danger">*</span>
                    </label>
                    <input type="text" class="form-control" id="trackingNumber" placeholder="{{ __('translation.tracking_number_placeholder') }}" required />
                </div>

                <div class="col">
                    <label for="orderNumber" class="form-label font-noto fs-6 fw-medium">
                        {{ __('translation.order_number_label') }}
                    </label>
                    <input type="text" class="form-control" id="orderNumber" placeholder="{{ __('translation.order_number_placeholder') }}" />
                </div>

                <div class="col col-lg-2 align-self-end">
                    <button type="button" id="searchButton" class="custom-button padding-ship py-0 w-100 font-kufi aos-init aos-animate d-flex justify-content-center align-items-center column-gap-1" data-aos="zoom-in" data-aos-duration="800" data-aos-delay="600">
                        <span>{{ __('translation.search_button') }}</span>
                        <i class="fa-brands fa-searchengin"></i>
                    </button>
                </div>
            </form>

            <div id="orderResults" class="mt-4"></div>

            <div class="row">
                <span class="font-kufi lead-color">
                    {{ __('translation.additional_info') }}
                </span>
            </div>

            <div class="ship-track mt-3 d-flex flex-column flex-lg-row justify-content-between align-items-center row-gap-3"></div>
        </div>

        <div class="d-none col-4 d-lg-block">
            <img src="{{ URL::asset('build/images/Truck.gif') }}" alt="truck"/>
        </div>
    </div>

    <div class="row">
        <div class="col-12 d-flex justify-content-between align-items-center mt-5 mb-4">
            <button type="button" class="btn btn-light">
                <a href="mailto:{{ __('translation.footer_email') }}" class="font-noto lead-color">
                    {{ __('translation.footer_email') }} <i class="fa-regular fa-envelope"></i>
                </a>
            </button>
            <span class="font-noto lead-color">{{ __('translation.footer_copyright') }}</span>
        </div>
    </div>
</div>

@include('components.footer')

<!-- Scripts -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/aos@2.3.4/dist/aos.js"></script>

<script>
    function getShipmentCompanyLogo(shipmentCompany) {
        const logoMap = {
            'aramex': '{{ URL::asset("build/images/aramex.png") }}',
            'barq': '{{ URL::asset("build/images/barq.png") }}',
            'transcorp': '{{ URL::asset("build/images/transcorp.png") }}',
            'thabit': '{{ URL::asset("build/images/thabit.png") }}',
            'jt': '{{ URL::asset("build/images/jt.png") }}',
            'spl': '{{ URL::asset("build/images/spl.jpg") }}'
        };
        return logoMap[shipmentCompany.toLowerCase()] || '{{ URL::asset("build/images/treek-icon.png") }}';
    }

    const translations = {
        pending: "{{ __('translation.order_status.pending') }}",
        awaiting_pickup: "{{ __('translation.order_status.awaiting_pickup') }}",
        currently_shipping: "{{ __('translation.order_status.currently_shipping') }}",
        delivered: "{{ __('translation.order_status.delivered') }}",
        returned: "{{ __('translation.order_status.delivered') }}",
        shipment_on_hold: "{{ __('translation.order_status.delivered') }}",
        canceled: "{{ __('translation.order_status.delivered') }}",
        failed: "{{ __('translation.order_status.delivered') }}",
        not_changed: "{{ __('translation.order_status.delivered') }}",
    };

    function executeSearch() {
        let trackingNumber = $("#trackingNumber").val();
        let orderNumber = $("#orderNumber").val();

        $.ajax({
            url: "{{ route('order.search') }}",
            type: "GET",
            data: {
                tracking_number: trackingNumber,
                order_number: orderNumber
            },
            success: function (response) {
                let html = "";
                let logoHtml = "";

                if (response.length > 0) {
                    const firstOrder = response[0];
                    if (firstOrder.shipment_company) {
                        const logoPath = getShipmentCompanyLogo(firstOrder.shipment_company);
                        logoHtml = `<img id="ship-2" src="${logoPath}" alt="${firstOrder.shipment_company} Logo" class="logo-grayscale" />`;
                        $(".ship-track").html(logoHtml);
                    }

                    response.forEach(order => {
                        const statusSteps = {
                            'pending': "{{ __('translation.order_status.pending') }}",
                            'awaiting_pickup': "{{ __('translation.order_status.awaiting_pickup') }}",
                            'currently_shipping': "{{ __('translation.order_status.currently_shipping') }}",
                            'delivered': "{{ __('translation.order_status.delivered') }}"
                        };

                        const statusKeys = Object.keys(statusSteps);
                        const currentStatus = order.status;
                        const currentIndex = statusKeys.indexOf(currentStatus);
                        const isRtl = "{{ app()->getLocale() }}" === "ar";

                        let shipmentProgressLineHtml = `<div class="shipment-progress ${isRtl ? 'rtl' : 'ltr'}" dir="${isRtl ? 'rtl' : 'ltr'}">`;

                        statusKeys.forEach((key, index) => {
                            const isActive = index <= currentIndex;
                            const label = statusSteps[key];
                            shipmentProgressLineHtml += `
                                <div class="shipment-step ${isActive ? 'active' : ''}">
                                    <div class="step-head">
                                        <div class="circle">`;

                            if (key === currentStatus) {
                                shipmentProgressLineHtml += `<img src="{{ asset('build/images/Truck.png') }}" alt="🚚" class="truck-icon ${isRtl ? 'rtl' : ''}" />`;
                            } else if (isActive) {
                                shipmentProgressLineHtml += `<i class="fa fa-check"></i>`;
                            }

                            shipmentProgressLineHtml += `</div></div>
                                    <div class="label">${label}</div>
                                </div>`;
                        });

                        shipmentProgressLineHtml += `</div>`;

                        html += shipmentProgressLineHtml + `
                            <div class="shipment-tracking-info-box p-3">
                                <p><strong>{{__('translation.order_number_label')}} : </strong>${order.order_number ?? 'N/A'}</p>
                                <p><strong>{{__('translation.shipment_reference')}} : </strong>${order.shipment_reference ?? 'N/A'}</p>
                                <p><strong>{{__('translation.shipment_tracking_link')}} : </strong> <a style="color: #6f42c1;" href="${order.shipment_tracking_link}">${order.shipment_tracking_link}</a></p>
                            </div>`;

                        let orderHistoriesHtml = `<div class="shipment-histories-info-box p-3">`;
                        orderHistoriesHtml += `<div class="d-flex flex-column mx-4" style="width:100%; margin:0 auto">`;
                        orderHistoriesHtml += `<h5 class="my-4"><strong>{{__('translation.order_history')}}</strong></h5>`;

                        order.histories.forEach(history => {
                            const translatedEvent = translations[history.event_type.toLowerCase()] || history.event_type;
                            orderHistoriesHtml += `
                                <div class="history-item mb-1">
                                    <div class="history-time">${history.action_time || ''}</div>
                                    <div class="history-event"><strong>${translatedEvent || ''}</strong></div>
                                    <div class="history-desc">${history.description || ''}</div>
                                </div>`;
                        });

                        orderHistoriesHtml += `</div></div>`;
                        html += orderHistoriesHtml;
                    });
                } else {
                    html = '<p class="text-danger">No orders found.</p>';
                    $(".ship-track").html('');
                }

                $("#orderResults").html(html);
            },
            error: function () {
                $("#orderResults").html('<p class="text-danger">Error fetching data.</p>');
            }
        });
    }

    $(document).ready(function () {
        AOS.init({ duration: 800, once: false, easing: "ease-out" });

        const urlParams = new URLSearchParams(window.location.search);
        const prefilledTracking = urlParams.get('trackingNumber') || '';
        const prefilledOrder = urlParams.get('orderNumber') || '';

        if (prefilledTracking) $("#trackingNumber").val(prefilledTracking);
        if (prefilledOrder) $("#orderNumber").val(prefilledOrder);

        $("#searchButton").on("click", executeSearch);

        if (prefilledTracking || prefilledOrder) {
            executeSearch();
        }

        $(".navbar-toggler").on("click", function () {
            $(".nav-bg").toggleClass("bg-white");
        });

        window.addEventListener("scroll", function () {
            const navbar = document.querySelector(".navbar");
            const navBg = document.querySelector(".nav-bg");

            if (window.scrollY > 5) {
                if (navbar) navbar.classList.add("scrolled");
                if (navBg) navBg.classList.add("scrolled");
            } else {
                if (navbar) navbar.classList.remove("scrolled");
                if (navBg) navBg.classList.remove("scrolled");
            }
        });
    });
</script>
</body>
</html>