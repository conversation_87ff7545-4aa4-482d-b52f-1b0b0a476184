<!DOCTYPE html>
<html lang="{{ app()->getLocale() }}" dir="{{ app()->getLocale() === 'ar' ? 'rtl' : 'ltr' }}">
@include('layouts.head')
<body>
<div class="nav-bg"></div>
<div class="p-0 container-fluid container-lg">
    @include('navbar')

    <!-- Hero and Image Section -->
    <section class="hero-section py-5">
        <div class="container">
            <div class="row align-items-center justify-content-center">
                <!-- Text Section -->
                <div
                    class="col-md-6 order-md-1 text-section text-center"
                    data-aos="fade-right"
                    data-aos-duration="800"
                    data-aos-once="false"
                >
                    <h1 class="text-center mb-5 font-kufi fw-bolder">
                        {!! __('translation.hero_title') !!}
                    </h1>
                    <h4 class="lead mb-4 font-kufi fw-light">
                        {!!  __('translation.hero_subtitle') !!}
                    </h4>
                    <a
                        href="/merchant/login"
                        class="custom-button m-2 font-noto"
                        data-aos="zoom-in"
                        data-aos-once="false"
                    >{{ __('translation.start_free_button') }}</a>
                    <a
                        href="https://calendly.com/gotreek-sa/30min"
                        class="custom2-button m-2 font-noto"
                        data-aos="zoom-in"
                        data-aos-once="false"
                    >{{ __('translation.contact_sales_button') }}</a>
                </div>

                <!-- Image Section -->
                <div
                    class="col-md-6 order-md-2 image-section text-center"
                    data-aos="fade-left"
                    data-aos-duration="800"
                    data-aos-once="false"
                >
                    <img
                        src="{{ URL::asset('build/images/image2.svg') }}"
                        alt="Shipping Graphic"
                        class="mx-auto d-block"
                    />
                </div>
            </div>
        </div>
    </section>

    <!-- Integrated Shipping Portal Section -->
    <section
        class="shipping-portal-section py-5"
        data-aos="fade-up"
        data-aos-duration="800"
        data-aos-once="false"
    >
        <div class="container text-center">
            <h2
                class="text-center mb-5 font-kufi fw-bold"
                data-aos="fade-down"
                data-aos-duration="800"
                data-aos-once="false"
            >
                {{ __('translation.shipping_portal_title') }}
            </h2>
            <div class="row">
                <!-- Custom Prices -->
                <div
                    class="col-md-6"
                    data-aos="fade-right"
                    data-aos-duration="800"
                    data-aos-once="false"
                >
                    <img
                        src="{{ URL::asset('build/images/prices.svg') }}"
                        alt="Custom Prices Icon"
                        class="img-fluid mb-3"
                        style="width: 18%"
                    />
                    <h5 class="font-kufi">{{ __('translation.custom_prices_title') }}</h5>
                    <h6 class="fs-4 fw-light mb-4 font-kufi lead-color">
                        {!! nl2br(e(__('translation.custom_prices_description'))) !!}
                    </h6>
                </div>
                <!-- Treek Prices -->
                <div
                    class="col-md-6"
                    data-aos="fade-left"
                    data-aos-duration="800"
                    data-aos-once="false"
                >
                    <img
                        src="{{ URL::asset('build/images/treek-prices.svg') }}"
                        alt="Treek Prices Icon"
                        class="img-fluid mb-3"
                        style="width: 18%"
                    />
                    <h5 class="font-kufi">{{ __('translation.treek_prices_title') }}</h5>
                    <h6 class="fw-light fs-4 mb-4 font-kufi lead-color">
                        {!! nl2br(e(__('translation.treek_prices_description'))) !!}
                    </h6>
                </div>
            </div>
        </div>
    </section>

    <!-- Ecommerce Section -->
    <section class="hero-section py-5">
        <div class="container">
            <div class="row align-items-center justify-content-center">
                <!-- Text Section -->
                <div class="col-md-6 order-md-1 text-section text-center">
                    <h2 class="text-center mb-5 font-kufi fw-bold">
                        {{ __('translation.ecommerce_title') }}
                    </h2>
                    <h6 class="lead-color fs-4 mb-4 font-kufi fw-light">
                        {{ __('translation.ecommerce_description') }}
                    </h6>
                </div>

                <!-- Image Section -->
                <div class="col-md-6 order-md-2 image-section text-center">
                    <img
                        src="{{ URL::asset('build/images/image3.svg') }}"
                        alt="Ecommerce Graphic"
                        class="img-fluid mb-3 w-100"
                    />
                </div>
            </div>
        </div>
    </section>

    <!-- Local and International Shipping Section -->
    <section class="hero-section py-5">
        <div class="container">
            <div class="row align-items-center justify-content-center">
                <!-- Image Section -->
                <div class="col-md-6 order-md-1 image-section text-center">
                    <img
                        src="{{ URL::asset('build/images/earth-icon.svg') }}"
                        alt="Earth Icon"
                        class="img-fluid mb-3 w-75"
                    />
                </div>

                <!-- Text Section -->
                <div class="col-md-6 order-md-2 text-section text-center">
                    <h2 class="text-center mb-5 font-kufi fw-bold">
                        {{ __('translation.local_international_title') }}
                    </h2>
                    <p class="lead-color font-kufi mb-4 d-flex flex-wrap justify-content-center">
                        <span
                            class="flex-fill text-center mb-3">{{ __('translation.local_international_point_1') }}</span>
                        <span
                            class="flex-fill text-center mb-3">{{ __('translation.local_international_point_2') }}</span>
                        <span
                            class="flex-fill text-center mb-3">{{ __('translation.local_international_point_3') }}</span>
                        <span
                            class="flex-fill text-center mb-3">{{ __('translation.local_international_point_4') }}</span>
                        <span
                            class="flex-fill text-center mb-3">{{ __('translation.local_international_point_5') }}</span>
                    </p>
                </div>
            </div>
        </div>
    </section>
</div>
<!-- Footer Section -->
@include('components.footer')
<script>
    // Use optimized AOS initialization from performance utils
    window.whenReady(function() {
        window.initOptimizedAOS();
    });
</script>
</body>
</html>
