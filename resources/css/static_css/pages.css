:root {
    --bs-primary: #6f42c1;
}

.main-color {
    color: #6f42c1;
}

.lead-color {
    color: #6a7072;
}

.font-kufi {
    font-family: "Noto Kufi Arabic", serif;
    line-height: 2.7rem;
}

.font-noto {
    font-family: "Noto Sans Arabic", serif !important;
}

.lead {
    color: #6a7072;
    font-size: 30px;
}

.padding-ship {
    padding-top: 1px !important;
    padding-bottom: 1px !important;
    margin-bottom: 1px;
}

.logo {
    height: 40px;
}

.nav-link:hover {
    text-decoration: underline;
    text-decoration-color: var(--bs-primary);
    text-decoration-thickness: 5px;
    text-underline-offset: 7px;
}

.navbar-nav {
    display: flex;
    flex-wrap: wrap;
    padding-right: 0;
}

.navbar-nav .nav-link {
    white-space: nowrap;
    transform: none;
    font-family: "Noto Kufi Arabic", serif;
    font-size: 15px;
    font-weight: bold;
}

.navbar-nav .custom3-button {
    white-space: nowrap; /* Prevent button text wrapping */
}

.active-link {
    text-decoration: underline;
    text-decoration-color: var(--bs-primary);
    text-decoration-thickness: 5px;
    text-underline-offset: 7px;
}

.footerhome {
    background: linear-gradient(
        135deg,
        #6f42c1,
        #6f42c1
    ); /* Beautiful purple gradient */
    color: #fff; /* White text for contrast */
    padding: 40px 20px;
    text-align: center;
}

.footerhome .footer-heading {
    margin-bottom: 15px;

    text-transform: uppercase;
}

.footerhome .footer-description,
.footerhome .footer-email-description,
.footerhome .footer-social-description {
    margin-bottom: 10px;
    line-height: 1.6;
}

.footerhome .footer-phone {
    margin-bottom: 10px;
}

.footerhome .footer-email {
    color: #060219; /* Bright gold color for email link */

    text-decoration: none;
}

.footerhome .footer-email:hover {
    text-decoration: underline;
}

.social-icons {
    margin-top: 20px;
}

.social-icons .social-icon {
    display: inline-block;
    margin: 0 10px;
    color: #fff;
    transition: transform 0.3s ease,
    color 0.3s ease;
}

.social-icons .social-icon:hover {
    transform: scale(1.2); /* Slight zoom effect on hover */
    color: #6200ff; /* Change icon color on hover */
}

.footer {
    background-color: white;
    color: black;
    bottom: 0;
    width: 100%;
}

.hero-section {
    color: black;
}

.custom-button {
    background-color: #6f42c1;
    color: white;
    border: 2px solid black;
    padding: 10px 30px;
    border-radius: 10px;
    font-size: 20px;

    cursor: pointer;
    box-shadow: 4px 4px 0px black;
    transition: background-color 0.3s ease;
    font-weight: normal;
    font-style: normal;
    text-decoration: none;
}

.custom-button:hover {
    background-color: #563d7c;
}

.custom2-button {
    background-color: white;
    color: black;
    border: 2px solid black;
    padding: 10px 30px;
    border-radius: 10px;
    font-size: 20px;

    cursor: pointer;
    box-shadow: 4px 4px 0px black;
    transition: background-color 0.3s ease;
    font-weight: normal;
    font-style: normal;
    text-decoration: none;
}

.custom2-button:hover {
    background-color: #6f42c1;
    color: white;
}

.custom3-button {
    background-color: white;
    color: #6f42c1;
    border: 2px solid black;
    padding: 5px 15px;
    border-radius: 10px;

    cursor: pointer;
    box-shadow: 2px 2px 0px #6f42c1;
    transition: background-color 0.3s ease;
    font-weight: bold;
    font-style: normal;
    text-decoration: none;
}

.custom3-button:hover {
    background-color: #6f42c1;
    color: white;
}

.custom4-button {
    background-color: transparent;
    border: none;
    color: #000;
    padding: 15px 32px;

    cursor: pointer;
    border-radius: 50px;
    position: relative;
    margin: 30px;
    transition: all 0.3s ease;
}

.custom4-button:hover {
    background-color: transparent;
    box-shadow: none;
}

.custom4-button:focus,
.custom4-button.selected {
    background-color: #6f42c1;
    color: white;
    box-shadow: 0px 6px 12px rgba(0, 0, 0, 0.3);
    outline: none;
}

.form-control {
    background-color: white;
    color: #6f42c1;
    border: 2px solid black;
    padding: 10px;
    border-radius: 10px;

    box-shadow: 2px 2px 0px #000000;
    transition: 0.3s ease;

    font-style: normal;
}

.form-control::placeholder {
    opacity: 0.6; /* Adjust the opacity value as needed */
    font-family: "Noto Kufi Arabic", serif;
}

.custom-h2 {
    text-decoration: underline;
    text-decoration-color: var(--bs-primary);
    text-decoration-thickness: 8px;
    text-underline-offset: 15px;
    margin-bottom: 20px;
}

.accordion-button {
    background-color: #f1f1f9;
    color: #6f42c1;
    font-weight: bold;
}

.accordion-button:not(.collapsed) {
    background-color: #e6e6f2;
}

.accordion-body {
    color: #666;
}

.accordion-button::after {
    display: none;
}

.accordion-button:hover {
    background-color: #6f42c1; /* Change to desired hover color */
    color: #fff; /* Change text color on hover */
}

.stats-section .stat-value {
    color: #6f42c1; /* Matches the primary color in your design */
}

.stats-section .stat-description {
    color: #000;
}

.services-section {
    margin-bottom: 50px;
}

@media (max-width: 1200px) {
    .navbar-collapse {
        text-align: center;
        background-color: white;
    }
}

@media (max-width: 991px) {
    .navbar-collapse {
        /*text-align: center;*/
        /*background-color: white;*/
    }

    .custom-button,
    .custom2-button {
        display: block;
        width: 100%;
        margin-bottom: 10px;
    }


}

.custom-button,
.custom2-button {
    display: inline-block;
    width: auto;
}

/* Consistent margin between sections */
section {
    margin-bottom: 30px;
    margin-top: 30px;
}

@media (max-width: 768px) {
    .custom-button,
    .custom2-button {
        width: 100%;
        margin-bottom: 15px;
    }

    .text-section {
        margin-bottom: 20px;
    }

    .image-section {
        margin-top: 30px;
        margin-bottom: 30px; /* Adjusted for mobile devices */
    }

    .hero-section {
        padding-top: 30px;
        padding-bottom: 30px;
    }

    .shipping-portal-section .col-md-6 {
        margin-bottom: 20px;
    }

    .footer .custom-button {
        margin-bottom: 20px;
    }

    .navbar-nav {
        margin-top: 50px;
    }

    section {
        margin-bottom: 0px;
        margin-top: 0px;
    }
}

.accordion-button:hover {
    transform: scale(1.05);
    transition: transform 0.3s ease-in-out;
}

.navbar {
    position: sticky;
    top: 0;
    z-index: 1030;
    transition: all 0.3s ease;
    background-color: transparent; /* Make the navbar transparent */
    background-image: none; /* No additional background for the navbar */
    box-shadow: none; /* Remove shadow for a cleaner look */
    width: 100%;

    color: #ffffff; /* Ensure the text is visible */
}

.navbar.scrolled {
    background: transparent;
}

.shipping-portal-section img {
    transition: transform 0.4s ease-in-out,
    filter 0.4s ease-in-out;
}

.shipping-portal-section img:hover {
    filter: brightness(1.3); /* Brighten the icon */
    transform: rotate(10deg); /* Add a slight rotation */
}

body {
    overflow-x: hidden;
    background-color: #faf5ff;
    background-image: linear-gradient(0deg, #faf5ff, #faf5ff00 40%),
    radial-gradient(circle at 0 0, #e6d6f3 0, #e6d6f300 40%),
    linear-gradient(180deg, #f1e7ffb3 -10%, #f1e7ff00 80%),
    radial-gradient(circle at 95% 0, #f8ecff 0, #e6d6f300 30%);
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
}

.nav-bg {
    width: 100%;
    height: 66px;
    position: fixed;
    top: 0;
    z-index: 1029;
    transition: all 0.2s ease;
    background-color: transparent; /* Make the navbar transparent */
    background-image: none; /* No additional background for the navbar */
    box-shadow: none; /* Remove shadow for a cleaner look */
    font-weight: bold;
    color: #ffffff;
}

.nav-bg.scrolled {
    background: rgba(255, 255, 255, 1);
}

.navbar-brand {
    margin: 0;
}

.nav-bg.active {
    background-color: white;
}

.slick-slide {
    padding: 10px;
}

.slick-slide img {
    width: 150px; /* Fixed width */
    margin: auto;

    object-fit: cover; /* Ensures the image scales properly */
    border-radius: 10px;
}

div.slick-track {
    display: flex;
    justify-content: center;
    align-items: center;
}

.form-control:focus {
    box-shadow: none;
    border-color: var(--bs-primary);
}

.custom-w {
    min-width: 180px;
}

.ship-track img {
    height: auto;
    width: 10px;
}

/* Apply grayscale filter */
.logo-grayscale {
    filter: grayscale(100%);
    transition: filter 0.3s ease;
}

/* Optional: Add hover effect to show original colors */
.logo-grayscale:hover {
    filter: none;
}

.dropdown-item.active, .dropdown-item:active {
    background-color: #b879f3 !important;
}

.your-class {
    width: 100%;
    overflow: hidden;
}

.bg-white {
    background-color: white !important;
}

.custom-radio {
    border: 2px solid #4C9BF5;
    border-radius: 8px;
    padding: 10px 15px;
    background-color: #EAF3FF;
    display: flex;
    justify-content: start;
    gap: 5px;
    align-items: center;
}

.custom-radio input[type="radio"] {
    width: 18px;
    height: 18px;
    accent-color: #4C9BF5;
}

.modal-button {
    background-color: #4C9BF5;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 8px;
    cursor: pointer;
}

.modal-button:hover {
    background-color: #3A8BE0;
}

.custom-btn-calc {
    background-color: white;
    color: #6f42c1;
    border: 2px solid black;


    box-shadow: 2px 2px 0px #000000;
    transition: 0.3s ease;

    font-style: normal;
}
.custom-btn-calc:hover {
    background-color: #6f42c1;
    color: white;
    border-color: black !important;

}
.z-9999 {
    z-index: 9999 !important;
}

.shipment-progress {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    position: relative;
    margin-top: 3rem;
    padding: 2rem;
    border: 2px solid #ccc;
    border-radius: 15px;
    background-color: transparent;
}

.shipment-step {
    flex: 1;
    text-align: center;
    position: relative;
}

.step-head {
    position: relative;
    height: 60px;
}

/* Default LTR logic */
.shipment-progress .shipment-step:not(:last-child) .step-head::after {
    content: '';
    position: absolute;
    top: 20px;
    width: 100%;
    height: 4px;
    background-color: #ccc;
    z-index: 0;
}

.shipment-progress.rtl .shipment-step:not(:last-child) .step-head::after {
    left: -50%;
}

.shipment-progress.ltr .shipment-step:not(:last-child) .step-head::after {
    left: 50%;
}

.shipment-step.active:not(:last-child) .step-head::after {
    background-color: #6f42c1;
}

.circle {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: #ccc;
    margin: 0 auto;
    position: relative;
    z-index: 2;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 18px;
}

.shipment-step.active .circle {
    background-color: #6f42c1;
}

.label {
    margin-top: 0.75rem;
    font-weight: bold;
    color: #999;
    line-height: 1.4;
    max-width: 90px;
    margin-left: auto;
    margin-right: auto;
    word-break: break-word;
}

.shipment-step.active .label {
    color: #000;
}

.truck-icon {
    width: 70px;
    height: 50px;
    position: absolute;
    z-index: 3;
    left: 50%;
    transform: translateX(-50%) scale(1.2);
    transition: left 0.5s ease;
    direction: ltr;
}

.truck-icon.rtl {
    transform: translateX(-50%) scaleX(-1.2);
}

.shipment-tracking-info-box {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    flex-direction: column;
    position: relative;
    margin-top: 3rem;
    padding: 2rem;
    border: 2px solid #ccc;
    border-radius: 15px;
    background-color: transparent;
    flex-wrap: wrap;
}

.shipment-histories-info-box {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    position: relative;
    margin-top: 3rem;
    padding: 2rem;
    border: 2px solid #ccc;
    border-radius: 15px;
    background-color: transparent;
    flex-wrap: wrap;
}

.history-item {
    background-color: white;
    color: #6f42c1;
    border: 2px solid black;
    padding: 10px;
    border-radius: 10px;
    box-shadow: 2px 2px 0 #000000;
    transition: 0.3s ease;
    font-style: normal;
    display: flex;
    justify-content: space-between;
    gap: 1rem;
    margin-bottom: 0.5rem;
    width: 100%;
}

.history-time {
    width: 20%;
    font-weight: bold;
}

.history-event {
    width: 20%;
    text-align: center;
}

.history-desc {
    width: 60%;
}