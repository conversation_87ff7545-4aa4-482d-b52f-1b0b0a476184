APP_NAME=Laravel
APP_ENV=local
APP_KEY=
APP_DEBUG=true
APP_TIMEZONE=UTC
APP_URL=http://localhost

APP_LOCALE=en
APP_FALLBACK_LOCALE=en
APP_FAKER_LOCALE=en_US

APP_MAINTENANCE_DRIVER=file
# APP_MAINTENANCE_STORE=database

BCRYPT_ROUNDS=12

LOG_CHANNEL=stack
LOG_STACK=single
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug
DB_COLLATION=utf8mb4_unicode_ci
DB_CONNECTION=sqlite
# DB_HOST=127.0.0.1
# DB_PORT=3306
# DB_DATABASE=laravel
# DB_USERNAME=root
# DB_PASSWORD=

SESSION_DRIVER=database
SESSION_LIFETIME=120
SESSION_ENCRYPT=false
SESSION_PATH=/
SESSION_DOMAIN=null

BROADCAST_CONNECTION=log
FILESYSTEM_DISK=local
QUEUE_CONNECTION=database

CACHE_STORE=database
CACHE_PREFIX=

MEMCACHED_HOST=127.0.0.1

REDIS_CLIENT=phpredis
REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

MAIL_MAILER=log
MAIL_HOST=127.0.0.1
MAIL_PORT=2525
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_ENCRYPTION=null
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=
AWS_USE_PATH_STYLE_ENDPOINT=false

VITE_APP_NAME="${APP_NAME}"
SALLA_OAUTH_CLIENT_ID="c15b3d85-c777-4362-a5cb-5febdc3eae54"
SALLA_OAUTH_CLIENT_SECRET="4f2eaa11e8e2a4a280bec41728329e7a"
ZID_CLIENT_ID="3883"
ZID_CLIENT_SECRET="OyOulJHTCL39ce35y0d9uAuOXd4sVO3LHMekou6R"
SHOPIFY_CLIENT_ID="d04788efc5e046206835fc6b73bd3594"
SHOPIFY_CLIENT_SECRET="626b2bd44c80207148679004026c6ec1"
BARQ_FLEET_BASE_URL=https://staging.barqfleet.com

ALTHABIT_BASE_URL=
ALTHABIT_USERNAME=
ALTHABIT_PASSWORD=
